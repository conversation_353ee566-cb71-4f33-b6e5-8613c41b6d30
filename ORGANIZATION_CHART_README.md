# 组织架构图功能说明

## 概述

组织架构图功能提供了一个可视化的组织层级结构展示系统，整合了部门、岗位、员工三类数据，支持树形和图形两种展示方式，具有完整的搜索、筛选、导出等功能。

## 功能特性

### 1. 可视化展示
- **树形视图**：以树形结构展示组织架构，支持节点展开/收缩
- **图形视图**：使用ECharts图表库展示组织关系图
- **节点详情**：点击节点可查看详细信息，包括基本信息、统计数据、层级路径

### 2. 数据整合
- **部门管理**：展示部门层级结构和基本信息
- **岗位管理**：显示各部门下的岗位设置和编制情况
- **员工管理**：展示员工分布和基本信息

### 3. 交互功能
- **搜索功能**：支持按关键词搜索部门、岗位、员工
- **类型筛选**：可按节点类型（部门/岗位/员工）进行筛选
- **展开控制**：支持一键展开/收缩所有节点
- **定位功能**：搜索结果可直接定位到树形结构中

### 4. 统计信息
- **实时统计**：显示部门总数、岗位总数、员工总数等统计信息
- **层级统计**：显示各节点的直接下属和总下属数量

### 5. 导出功能
- **图片导出**：图形视图支持导出为PNG格式图片
- **数据导出**：支持导出组织架构数据为JSON格式

## 技术实现

### 后端架构

#### API接口
```
/api/v1/system/organizationChart/
├── getOrganizationTree     # 获取完整组织架构树
├── getDepartmentTree       # 获取指定部门子树
├── getOrganizationStats    # 获取统计信息
└── searchNodes            # 搜索节点
```

#### 核心组件
- **OrganizationChartApi**: API接口定义
- **OrganizationChartModel**: 数据传输对象
- **OrganizationChartService**: 业务逻辑服务
- **OrganizationChartRestController**: REST控制器

#### 数据聚合
系统整合了三个数据源：
- **DepartmentService**: 部门数据服务
- **PositionService**: 岗位数据服务  
- **EmployeeService**: 员工数据服务

### 前端架构

#### 页面组件
```
src/views/modules/system/organizationChart/
├── index.vue                    # 主页面组件
├── components/
│   └── NodeDetail.vue          # 节点详情组件
└── api/
    └── organizationChart.js    # API调用封装
```

#### 技术栈
- **Vue.js**: 前端框架
- **Element UI**: UI组件库
- **ECharts**: 图表库（用于图形视图）
- **Vuex**: 状态管理（租户和月份信息）

## 使用说明

### 1. 访问路径
前端页面路径：`/hr/organizationChart`

### 2. 权限要求
需要 `system_organization_chart_select` 权限

### 3. 操作指南

#### 基本操作
1. **切换视图**：点击工具栏中的"树形视图"或"图形视图"按钮
2. **搜索节点**：在搜索框中输入关键词，选择节点类型，点击搜索
3. **查看详情**：点击节点的"详情"按钮查看完整信息
4. **展开控制**：使用"展开全部"/"收缩全部"按钮控制树形结构

#### 高级功能
1. **定位节点**：在搜索结果中点击"定位"按钮可在树形视图中高亮显示
2. **导出数据**：点击"导出"按钮可导出当前视图的数据或图片
3. **全屏查看**：点击"全屏"按钮进入全屏模式
4. **月份切换**：通过顶部月份选择器切换查看不同月份的数据

### 4. 数据说明

#### 节点类型
- **部门（department）**：组织的部门单位
- **岗位（position）**：部门下的职位设置
- **员工（employee）**：具体的员工信息

#### 数据关系
```
部门 (Department)
├── 子部门 (Sub-Department)
│   ├── 岗位 (Position)
│   │   ├── 员工 (Employee)
│   │   └── 员工 (Employee)
│   └── 岗位 (Position)
└── 岗位 (Position)
    └── 员工 (Employee)
```

## 配置说明

### 1. 路由配置
在 `src/router/index.js` 中已配置路由：
```javascript
{
  path: 'organizationChart',
  name: 'OrganizationChart',
  component: () => import('@/views/modules/system/organizationChart/index.vue'),
  meta: {
    title: '组织架构图',
    icon: 'el-icon-share',
  },
}
```

### 2. 权限配置
需要在权限管理中配置以下权限：
- `system_organization_chart_select`: 查询权限

### 3. 菜单配置
组织架构图已集成到"人事管理"模块下，可通过菜单直接访问。

## 扩展说明

### 1. 自定义节点类型
如需添加新的节点类型，需要：
1. 在 `OrganizationChartModel` 中添加相应字段
2. 在 `OrganizationChartService` 中添加数据处理逻辑
3. 在前端组件中添加对应的图标和样式

### 2. 自定义导出格式
可以扩展导出功能支持更多格式：
- PDF格式导出
- Excel格式导出
- SVG格式导出

### 3. 性能优化
对于大型组织架构，可以考虑：
- 懒加载：按需加载子节点数据
- 虚拟滚动：处理大量节点的渲染性能
- 缓存策略：缓存常用的组织架构数据

## 注意事项

1. **数据一致性**：组织架构数据依赖于部门、岗位、员工三个模块的数据，请确保这些模块的数据完整性
2. **权限控制**：系统会根据当前用户的租户ID自动过滤数据
3. **月份数据**：系统支持按月份查看历史组织架构数据
4. **浏览器兼容性**：建议使用现代浏览器以获得最佳体验
5. **性能考虑**：对于超大型组织（1000+节点），建议分批加载或使用部门树功能

## 故障排除

### 常见问题
1. **数据不显示**：检查权限配置和租户设置
2. **图表不渲染**：确认ECharts库已正确加载
3. **搜索无结果**：检查搜索关键词和节点类型筛选
4. **导出失败**：检查浏览器的下载权限设置

### 日志查看
后端日志位置：`logs/opsli-boot.log`
前端错误：浏览器开发者工具Console面板

# Design Document

## Overview

The salary management enhancement will complete the backend implementation for the OPSLI salary management system by implementing the missing service layers, REST controllers, and API interfaces for SalaryTemplateItemMonthly, EmployeeSalaryDetailMonthly, and PositionSalaryStandardMonthly. The implementation follows OPSLI's standard multi-layered architecture pattern and integrates with the existing salary infrastructure to provide a complete salary management workflow.

## Architecture

### Backend Architecture

The backend follows OPSLI's standard layered architecture, building upon existing salary entities and mappers:

```
API Layer (opsli-api)
├── SalaryTemplateItemApi.java (Interface definition)
├── EmployeeSalaryDetailApi.java (Interface definition)
├── PositionSalaryStandardApi.java (Interface definition)
└── Corresponding Model classes (DTOs/Wrappers)

Web Layer (opsli-modulars-system)
├── SalaryTemplateItemMonthlyRestController.java (REST endpoints)
├── EmployeeSalaryDetailMonthlyRestController.java (REST endpoints)
└── PositionSalaryStandardMonthlyRestController.java (REST endpoints)

Service Layer
├── ISalaryTemplateItemMonthlyService.java (Service interface)
├── IEmployeeSalaryDetailMonthlyService.java (Service interface)
├── IPositionSalaryStandardMonthlyService.java (Service interface)
└── Corresponding ServiceImpl classes (Business logic)

Data Access Layer (Already exists)
├── SalaryTemplateItemMonthlyMapper.java (MyBatis interface)
├── EmployeeSalaryDetailMonthlyMapper.java (MyBatis interface)
├── PositionSalaryStandardMonthlyMapper.java (MyBatis interface)
└── Corresponding Entity classes
```

### Frontend Integration

The frontend salary template configuration will be updated to use the correct APIs:

```
Frontend Updates
├── salaryTemplateItem.js (New API service for template items)
├── employeeSalaryDetail.js (New API service for employee details)
├── positionSalaryStandard.js (New API service for position standards)
└── Updated salary template configuration components
```

## Components and Interfaces

### Backend Components

#### 1. Service Layer Interfaces

**ISalaryTemplateItemMonthlyService**
- Template item CRUD operations with tenant and month filtering
- Batch operations for template item configuration
- Display order management and validation rule handling
- Integration with salary item validation

**IEmployeeSalaryDetailMonthlyService**
- Employee salary detail CRUD operations with multi-dimensional filtering
- Automatic pre-filling from position salary standards
- Calculation value updates and modification tracking
- Batch operations for salary data import/export

**IPositionSalaryStandardMonthlyService**
- Position salary standard CRUD operations
- Standard value configuration with min/max validation
- Auto-fill configuration management
- Integration with employee salary detail pre-filling

#### 2. Service Implementation Classes

**SalaryTemplateItemMonthlyServiceImpl**
- Business logic for template item management
- Validation of template-item associations
- Display order conflict resolution
- Integration with existing salary template services

**EmployeeSalaryDetailMonthlyServiceImpl**
- Business logic for employee salary detail management
- Position standard integration for auto-filling
- Calculation engine integration
- Modification tracking and audit trail management

**PositionSalaryStandardMonthlyServiceImpl**
- Business logic for position salary standard management
- Standard value validation and range checking
- Auto-fill configuration validation
- Integration with position and template services

#### 3. REST Controller Layer

**SalaryTemplateItemMonthlyRestController**
- RESTful endpoints for template item operations
- Pagination support for large template configurations
- Batch operations for template item management
- Permission-based access control

**EmployeeSalaryDetailMonthlyRestController**
- RESTful endpoints for employee salary detail operations
- Advanced filtering by employee, template, and salary item
- Calculation status tracking endpoints
- Bulk update operations for salary adjustments

**PositionSalaryStandardMonthlyRestController**
- RESTful endpoints for position salary standard operations
- Position-based filtering and template integration
- Standard value validation endpoints
- Auto-fill configuration management

#### 4. API Interface Layer

**SalaryTemplateItemApi**
- Swagger-documented API interface
- Request/response model definitions
- Validation annotations and error handling
- Integration with existing salary APIs

**EmployeeSalaryDetailApi**
- Comprehensive API interface for salary details
- Complex query parameter support
- Calculation result models
- Modification tracking models

**PositionSalaryStandardApi**
- Position salary standard API interface
- Standard value configuration models
- Auto-fill configuration models
- Validation rule models

## Data Models

### API Data Models

#### SalaryTemplateItemModel
```java
public class SalaryTemplateItemModel {
    private Long tenantId;
    private Date dataMonth;
    private Long templateId;
    private Long salaryItemId;
    private String salaryItemName;
    private String salaryItemCategory;
    private Integer displayOrder;
    private Boolean isRequired;
    private Boolean isEditable;
    private String defaultValue;
    private String validationRules; // JSON format
    private Integer status;
    // Audit fields
}
```

#### EmployeeSalaryDetailModel
```java
public class EmployeeSalaryDetailModel {
    private Long tenantId;
    private Date dataMonth;
    private Long employeeId;
    private String employeeName;
    private Long salaryItemId;
    private String salaryItemName;
    private String actualValue;
    private String standardValue;
    private BigDecimal calculatedValue;
    private String valueSource;
    private Boolean isModified;
    private String modificationReason;
    private String calculationMethod;
    private String sourceData; // JSON format
    private String calculationNote;
    private Date lastCalculatedAt;
    // Audit fields
}
```

#### PositionSalaryStandardModel
```java
public class PositionSalaryStandardModel {
    private Long tenantId;
    private Date dataMonth;
    private Long positionId;
    private String positionName;
    private Long templateId;
    private String templateName;
    private Long salaryItemId;
    private String salaryItemName;
    private String standardValue;
    private String minValue;
    private String maxValue;
    private Boolean isAutoFill;
    private Boolean isReadonly;
    private String description;
    // Audit fields
}
```

## Error Handling

### Backend Error Handling
- Custom exceptions for salary business logic violations
- Validation error handling for salary calculations
- Data consistency error handling across related entities
- Permission-based access error handling

### API Error Responses
- Standardized error response format
- Detailed validation error messages
- Business rule violation descriptions
- User-friendly error messages for frontend display

## Testing Strategy

### Backend Testing
1. **Unit Tests**
   - Service layer business logic testing
   - Calculation engine testing
   - Validation rule testing
   - Auto-fill logic testing

2. **Integration Tests**
   - Controller endpoint testing
   - Database transaction testing
   - Multi-entity relationship testing
   - Permission-based access testing

3. **API Tests**
   - REST endpoint functionality testing
   - Request/response validation testing
   - Error handling testing
   - Performance testing for large datasets

## Security Considerations

### Authentication & Authorization
- JWT token-based authentication for all endpoints
- Role-based permission checking for salary operations
- Multi-tenant data isolation enforcement
- Sensitive salary data access logging

### Data Security
- Input validation for all salary data
- SQL injection prevention in dynamic queries
- Salary calculation result integrity validation
- Audit trail protection and immutability

## Performance Considerations

### Backend Performance
- Database query optimization for complex salary queries
- Pagination for large employee salary datasets
- Caching strategy for frequently accessed position standards
- Batch processing for bulk salary operations

### Calculation Performance
- Efficient salary calculation algorithms
- Lazy loading for calculation dependencies
- Result caching for repeated calculations
- Asynchronous processing for bulk calculations

## Integration Points

### System Integration
- Integration with existing salary item management
- Integration with employee management system
- Integration with position management system
- Integration with tenant and permission systems

### Frontend Integration
- Updated API service files for new endpoints
- Modified salary template configuration components
- Enhanced error handling and validation display
- Improved user experience for salary data entry

## Migration and Deployment

### Database Migration
- No schema changes required (entities already exist)
- Data validation scripts for existing salary data
- Index optimization for new query patterns
- Performance monitoring for new endpoints

### Service Deployment
- Backward compatibility with existing salary APIs
- Gradual rollout of new functionality
- Monitoring and alerting for new services
- Documentation updates for API changes
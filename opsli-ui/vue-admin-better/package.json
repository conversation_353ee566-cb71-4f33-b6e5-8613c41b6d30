{"name": "vue-admin-better", "version": "2.1.5", "author": "vue-admin-better", "participants": [], "homepage": "https://chu1204505056.gitee.io/vue-admin-better", "scripts": {"globle": "npm config set registry https://registry.npm.taobao.org && npm install -g yarn && yarn config set registry https://registry.npm.taobao.org && yarn config set ignore-engines true && yarn install", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint --fix", "increase-memory-limit": "increase-memory-limit", "postinstall": "patch-package"}, "repository": {"type": "git", "url": "git+https://github.com/chuzhixin/vue-admin-beautiful.git"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "dependencies": {"axios": "^1.3.4", "better-scroll": "^2.0.4", "caniuse-lite": "^1.0.30001464", "clipboard": "^2.0.11", "codemirror": "5.45.0", "core-js": "^3.29.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "echarts": "5.4.1", "element-resize-detector": "^1.2.2", "element-ui": "^2.15.13", "file-saver": "^2.0.2", "github-markdown-css": "^5.1.0", "jquery": "^3.7.1", "js-cookie": "^2.2.1", "jsencrypt": "^3.3.2", "jsonlint": "^1.6.3", "layouts": "file:layouts", "lodash": "^4.17.21", "maptalks": "^0.49.5", "mapv": "^2.0.62", "marked": "^4.1.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "orgchart": "^5.0.0", "qs": "^6.11.1", "screenfull": "^5.2.0", "sortablejs": "^1.15.0", "vab-icon": "file:vab-icon", "vue": "~2.6.14", "vue-amap": "^0.5.10", "vue-echarts": "5.0.0-beta.0", "vue-qart": "^2.2.0", "vue-router": "^3.5.3", "vue-template-compiler": "~2.6.14", "vuedraggable": "^2.24.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/composition-api": "^1.7.1", "@vue/eslint-config-prettier": "^7.1.0", "babel-eslint": "^10.1.0", "body-parser": "^1.20.2", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^7.32.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.1.1", "filemanager-webpack-plugin": "^8.0.0", "lint-staged": "^13.2.0", "patch-package": "^6.2.2", "plop": "^2.7.4", "prettier": "^2.8.4", "sass": "~1.32.13", "sass-loader": "^10.1.1", "script-loader": "^0.7.2", "stylelint": "^15.2.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.0.0", "svg-sprite-loader": "^6.0.11", "vue-cropper": "^0.5.5", "webpackbar": "^5.0.2"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "vue-admin", "element-admin", "boilerplate", "admin-template", "management-system"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}
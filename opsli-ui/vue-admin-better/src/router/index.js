/* eslint-disable */
/**
 * @<NAME_EMAIL>
 * @description router全局配置，如有必要可分文件抽离，其中asyncRoutes只有在intelligence模式下才会用到，vip文档中已提供路由的基础图标与小清新图标的配置方案，请仔细阅读
 */

import Vue from "vue";
import VueRouter from "vue-router";
import Layout from "@/layouts";
import { publicPath, routerMode } from "@/config";

Vue.use(VueRouter);
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register/index"),
    hidden: true,
  },
  {
    path: "/personal",
    component: Layout,
    hidden: true,
    redirect: "personal",
    children: [
      {
        path: "center",
        name: "center",
        component: () => import("@/views/personalCenter/index"),
        meta: {
          title: "个人中心",
        },
      },
    ],
  },
  {
    path: "/personalCenter",
    component: Layout,
    hidden: true,
    redirect: "personalCenter",
    children: [
      {
        path: "personalCenter",
        name: "PersonalCenter",
        component: () => import("@/views/personalCenter/index"),
        meta: {
          title: "个人中心",
        },
      },
    ],
  },
  {
    path: "/401",
    name: "401",
    component: () => import("@/views/401"),
    hidden: true,
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/404"),
    hidden: true,
  },
];


export const asyncRoutes = [
  {
    path: '/hr',
    component: Layout,
    redirect: 'noRedirect',
    name: 'HrModule',
    meta: {
      title: '人事管理',
      icon: 'el-icon-user-solid',
    },
    children: [
      {
        path: 'dashboard',
        name: 'HrDashboard',
        component: () =>
          import('@/views/modules/system/hrManagement/index.vue'),
        meta: {
          title: 'HR数据概览',
          icon: 'el-icon-s-data',
        },
      },
      {
        path: 'organizationChart',
        name: 'OrganizationChart',
        component: () =>
          import('@/views/modules/system/organizationChart/index.vue'),
        meta: {
          title: '组织架构图',
          icon: 'el-icon-share',
        },
      },
    ],
  },
];

const router = new VueRouter({
  base: publicPath,
  mode: routerMode,
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRoutes,
});
//注释的地方是允许路由重复点击，如果你觉得框架路由跳转规范太过严格可选择放开
/* const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
}; */

export function resetRouter() {
  router.matcher = new VueRouter({
    base: publicPath,
    mode: routerMode,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  }).matcher;
}

export default router;

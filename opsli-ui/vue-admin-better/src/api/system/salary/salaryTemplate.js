import request from "@/utils/request";

/**
 * 获取薪资模板分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/template/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有薪资模板数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/template/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个薪资模板数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/template/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增薪资模板
 * @param {Object} data - 薪资模板数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/template/insert",
    method: "post",
    data,
  });
}

/**
 * 更新薪资模板
 * @param {Object} data - 薪资模板数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/template/update",
    method: "post",
    data,
  });
}

/**
 * 删除薪资模板
 * @param {Object} data - 薪资模板ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/template/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除薪资模板
 * @param {Object} data - 薪资模板ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/template/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据数据月份获取薪资模板
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function findByDataMonth(dataMonth) {
  return request({
    url: "/api/v1/system/salary/template/findByDataMonth",
    method: "get",
    params: { dataMonth },
  });
}

/**
 * 获取默认薪资模板
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function findDefault(dataMonth) {
  return request({
    url: "/api/v1/system/salary/template/findDefault",
    method: "get",
    params: { dataMonth },
  });
}

/**
 * 检查模板名称是否唯一
 * @param {Object} data - 薪资模板数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkNameUnique(data) {
  return request({
    url: "/api/v1/system/salary/template/checkNameUnique",
    method: "post",
    data,
  });
}

/**
 * 设置默认模板
 * @param {String} id - 模板ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function setDefault(id, dataMonth) {
  return request({
    url: "/api/v1/system/salary/template/setDefault",
    method: "post",
    params: { id, dataMonth },
  });
}

/**
 * 复制模板到新月份
 * @param {String} sourceTemplateId - 源模板ID
 * @param {Date} targetDataMonth - 目标月份
 * @param {String} newTemplateName - 新模板名称
 * @returns {Promise} - 返回请求Promise
 */
export function copyToNewMonth(sourceTemplateId, targetDataMonth, newTemplateName) {
  return request({
    url: "/api/v1/system/salary/template/copyToNewMonth",
    method: "post",
    params: { sourceTemplateId, targetDataMonth, newTemplateName },
  });
}

/**
 * 批量启用/禁用薪资模板
 * @param {String} ids - ID数组字符串
 * @param {Boolean} status - 状态
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateStatus(ids, status) {
  return request({
    url: "/api/v1/system/salary/template/batchUpdateStatus",
    method: "post",
    params: { ids, status },
  });
}

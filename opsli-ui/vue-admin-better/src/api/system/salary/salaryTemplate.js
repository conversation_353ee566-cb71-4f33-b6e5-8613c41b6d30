import request from "@/utils/request";

/**
 * 获取薪资模板分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/template/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有薪资模板数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/template/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个薪资模板数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/template/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增薪资模板
 * @param {Object} data - 薪资模板数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/template/insert",
    method: "post",
    data,
  });
}

/**
 * 更新薪资模板
 * @param {Object} data - 薪资模板数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/template/update",
    method: "post",
    data,
  });
}

/**
 * 删除薪资模板
 * @param {Object} data - 薪资模板ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/template/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除薪资模板
 * @param {Object} data - 薪资模板ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/template/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 检查模板名称是否唯一
 * @param {Object} data - 薪资模板数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkNameUnique(data) {
  return request({
    url: "/api/v1/system/salary/template/checkNameUnique",
    method: "post",
    data,
  });
}


/**
 * 复制模板
 * @param {Long} sourceTemplateId - 源模板ID
 * @param {String} newTemplateName - 新模板名称
 * @param {Date} targetDataMonth - 目标数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function copyTemplate(
  sourceTemplateId,
  newTemplateName,
  targetDataMonth
) {
  return request({
    url: "/api/v1/system/salary/template/copy",
    method: "post",
    params: { sourceTemplateId, newTemplateName, targetDataMonth },
  });
}

/**
 * 获取模板统计信息
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function getTemplateStats(templateId, tenantId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/template/stats",
    method: "get",
    params: { templateId, tenantId, dataMonth },
  });
}

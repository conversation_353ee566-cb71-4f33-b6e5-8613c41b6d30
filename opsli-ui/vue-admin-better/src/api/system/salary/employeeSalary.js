import request from "@/utils/request";

/**
 * 获取员工薪资分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/employee/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有员工薪资数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/employee/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个员工薪资数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/employee/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增员工薪资
 * @param {Object} data - 员工薪资数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/employee/insert",
    method: "post",
    data,
  });
}

/**
 * 更新员工薪资
 * @param {Object} data - 员工薪资数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/employee/update",
    method: "post",
    data,
  });
}

/**
 * 删除员工薪资
 * @param {Object} data - 员工薪资ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/employee/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除员工薪资
 * @param {Object} data - 员工薪资ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/employee/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据数据月份获取员工薪资
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function findByDataMonth(dataMonth) {
  return request({
    url: "/api/v1/system/salary/employee/findByDataMonth",
    method: "get",
    params: { dataMonth },
  });
}

/**
 * 根据员工ID获取薪资
 * @param {Number} employeeId - 员工ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function findByEmployee(employeeId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/employee/findByEmployee",
    method: "get",
    params: { employeeId, dataMonth },
  });
}

/**
 * 批量更新薪资状态
 * @param {String} ids - ID数组字符串
 * @param {String} status - 状态
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateStatus(ids, status) {
  return request({
    url: "/api/v1/system/salary/employee/batchUpdateStatus",
    method: "post",
    params: { ids, status },
  });
}

/**
 * 计算员工薪资
 * @param {Number} employeeId - 员工ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function calculateSalary(employeeId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/employee/calculateSalary",
    method: "post",
    params: { employeeId, dataMonth },
  });
}

/**
 * 批量计算员工薪资
 * @param {String} employeeIds - 员工ID数组字符串
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function batchCalculateSalary(employeeIds, dataMonth) {
  return request({
    url: "/api/v1/system/salary/employee/batchCalculateSalary",
    method: "post",
    params: { employeeIds, dataMonth },
  });
}

/**
 * 审批员工薪资
 * @param {String} id - 薪资ID
 * @returns {Promise} - 返回请求Promise
 */
export function approve(id) {
  return request({
    url: "/api/v1/system/salary/employee/approve",
    method: "post",
    params: { id },
  });
}

/**
 * 批量审批员工薪资
 * @param {String} ids - ID数组字符串
 * @returns {Promise} - 返回请求Promise
 */
export function batchApprove(ids) {
  return request({
    url: "/api/v1/system/salary/employee/batchApprove",
    method: "post",
    params: { ids },
  });
}

/**
 * 查询员工薪资历史记录
 * @param {Number} employeeId - 员工ID
 * @param {Number} limit - 限制数量
 * @returns {Promise} - 返回请求Promise
 */
export function findSalaryHistory(employeeId, limit = 12) {
  return request({
    url: "/api/v1/system/salary/employee/findSalaryHistory",
    method: "get",
    params: { employeeId, limit },
  });
}

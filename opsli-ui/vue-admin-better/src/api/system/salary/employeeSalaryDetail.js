import request from "@/utils/request";

/**
 * 获取员工薪资明细分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有员工薪资明细数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个员工薪资明细数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增员工薪资明细
 * @param {Object} data - 员工薪资明细数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/insert",
    method: "post",
    data,
  });
}

/**
 * 更新员工薪资明细
 * @param {Object} data - 员工薪资明细数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/update",
    method: "post",
    data,
  });
}

/**
 * 删除员工薪资明细
 * @param {Object} data - 员工薪资明细ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除员工薪资明细
 * @param {Object} data - 员工薪资明细ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据租户ID、数据月份和员工ID查询薪资明细
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} employeeId - 员工ID
 * @returns {Promise} - 返回请求Promise
 */
export function findByEmployee(tenantId, dataMonth, employeeId) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findByEmployee",
    method: "get",
    params: { tenantId, dataMonth, employeeId },
  });
}

/**
 * 根据租户ID、数据月份和员工ID列表查询薪资明细
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {String} employeeIds - 员工ID列表
 * @returns {Promise} - 返回请求Promise
 */
export function findByEmployees(tenantId, dataMonth, employeeIds) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findByEmployees",
    method: "get",
    params: { tenantId, dataMonth, employeeIds },
  });
}

/**
 * 根据薪资项目ID查询相关的薪资明细
 * @param {Long} salaryItemId - 薪资项目ID
 * @returns {Promise} - 返回请求Promise
 */
export function findBySalaryItem(salaryItemId) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findBySalaryItem",
    method: "get",
    params: { salaryItemId },
  });
}

/**
 * 根据员工ID、薪资项目ID和数据月份查询薪资明细
 * @param {Long} employeeId - 员工ID
 * @param {Long} salaryItemId - 薪资项目ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function findByEmployeeAndSalaryItem(employeeId, salaryItemId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findByEmployeeAndSalaryItem",
    method: "get",
    params: { employeeId, salaryItemId, dataMonth },
  });
}

/**
 * 检查员工薪资明细是否已存在
 * @param {Object} data - 员工薪资明细数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkExists(data) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/checkExists",
    method: "post",
    data,
  });
}

/**
 * 根据值来源查询薪资明细
 * @param {String} valueSource - 值来源
 * @returns {Promise} - 返回请求Promise
 */
export function findByValueSource(valueSource) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findByValueSource",
    method: "get",
    params: { valueSource },
  });
}

/**
 * 根据员工ID查询薪资明细历史记录
 * @param {Long} employeeId - 员工ID
 * @param {Long} salaryItemId - 薪资项目ID
 * @param {Number} limit - 限制数量
 * @returns {Promise} - 返回请求Promise
 */
export function findDetailHistory(employeeId, salaryItemId, limit = 10) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/findDetailHistory",
    method: "get",
    params: { employeeId, salaryItemId, limit },
  });
}

/**
 * 批量保存员工薪资明细
 * @param {Long} employeeId - 员工ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Array} salaryDetails - 薪资明细列表
 * @returns {Promise} - 返回请求Promise
 */
export function batchSave(employeeId, tenantId, dataMonth, salaryDetails) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/batchSave",
    method: "post",
    params: { employeeId, tenantId, dataMonth },
    data: salaryDetails,
  });
}

/**
 * 删除员工薪资明细
 * @param {Long} employeeId - 员工ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} tenantId - 租户ID
 * @returns {Promise} - 返回请求Promise
 */
export function deleteByEmployee(employeeId, dataMonth, tenantId) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/deleteByEmployee",
    method: "post",
    params: { employeeId, dataMonth, tenantId },
  });
}

/**
 * 根据岗位标准自动预填充员工薪资明细
 * @param {Long} employeeId - 员工ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} templateId - 薪资模板ID
 * @returns {Promise} - 返回请求Promise
 */
export function autoFillFromStandards(employeeId, tenantId, dataMonth, templateId) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/autoFillFromStandards",
    method: "post",
    params: { employeeId, tenantId, dataMonth, templateId },
  });
}

/**
 * 批量更新计算值
 * @param {Array} salaryDetailIds - 薪资明细ID列表
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateCalculatedValues(salaryDetailIds) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/batchUpdateCalculatedValues",
    method: "post",
    data: salaryDetailIds,
  });
}

/**
 * 标记为已修改
 * @param {String} id - 薪资明细ID
 * @param {String} modificationReason - 修改原因
 * @returns {Promise} - 返回请求Promise
 */
export function markAsModified(id, modificationReason) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/markAsModified",
    method: "post",
    params: { id, modificationReason },
  });
}

/**
 * 批量导入员工薪资明细
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Array} salaryDetails - 薪资明细列表
 * @returns {Promise} - 返回请求Promise
 */
export function batchImport(tenantId, dataMonth, salaryDetails) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/batchImport",
    method: "post",
    params: { tenantId, dataMonth },
    data: salaryDetails,
  });
}

/**
 * 复制上月薪资明细到当月
 * @param {Long} employeeId - 员工ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} sourceMonth - 源月份
 * @param {Date} targetMonth - 目标月份
 * @returns {Promise} - 返回请求Promise
 */
export function copyFromPreviousMonth(employeeId, tenantId, sourceMonth, targetMonth) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/copyFromPreviousMonth",
    method: "post",
    params: { employeeId, tenantId, sourceMonth, targetMonth },
  });
}

/**
 * 获取员工薪资明细统计信息
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} employeeId - 员工ID
 * @returns {Promise} - 返回请求Promise
 */
export function getStatistics(tenantId, dataMonth, employeeId) {
  return request({
    url: "/api/v1/system/salary/employeeDetail/getStatistics",
    method: "get",
    params: { tenantId, dataMonth, employeeId },
  });
}
import request from "@/utils/request";

/**
 * 获取薪资模板项目分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有薪资模板项目数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个薪资模板项目数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增薪资模板项目
 * @param {Object} data - 薪资模板项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/insert",
    method: "post",
    data,
  });
}

/**
 * 更新薪资模板项目
 * @param {Object} data - 薪资模板项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/update",
    method: "post",
    data,
  });
}

/**
 * 删除薪资模板项目
 * @param {Object} data - 薪资模板项目ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除薪资模板项目
 * @param {Object} data - 薪资模板项目ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据租户ID、数据月份和模板ID查询模板项目
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} templateId - 模板ID
 * @returns {Promise} - 返回请求Promise
 */
export function findByTemplate(tenantId, dataMonth, templateId) {
  return request({
    url: "/api/v1/system/salary/templateItem/findByTemplate",
    method: "get",
    params: { tenantId, dataMonth, templateId },
  });
}

/**
 * 根据租户ID、数据月份和模板ID查询启用的模板项目
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} templateId - 模板ID
 * @returns {Promise} - 返回请求Promise
 */
export function findEnabledByTemplate(tenantId, dataMonth, templateId) {
  return request({
    url: "/api/v1/system/salary/templateItem/findEnabledByTemplate",
    method: "get",
    params: { tenantId, dataMonth, templateId },
  });
}

/**
 * 根据薪资项目ID查询关联的模板项目
 * @param {Long} salaryItemId - 薪资项目ID
 * @returns {Promise} - 返回请求Promise
 */
export function findBySalaryItem(salaryItemId) {
  return request({
    url: "/api/v1/system/salary/templateItem/findBySalaryItem",
    method: "get",
    params: { salaryItemId },
  });
}

/**
 * 检查模板项目是否已存在
 * @param {Object} data - 薪资模板项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkExists(data) {
  return request({
    url: "/api/v1/system/salary/templateItem/checkExists",
    method: "post",
    data,
  });
}

/**
 * 批量保存模板项目配置
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Array} templateItems - 模板项目列表
 * @returns {Promise} - 返回请求Promise
 */
export function batchSave(templateId, tenantId, dataMonth, templateItems) {
  return request({
    url: "/api/v1/system/salary/templateItem/batchSave",
    method: "post",
    params: { templateId, tenantId, dataMonth },
    data: templateItems,
  });
}

/**
 * 删除模板的所有项目
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function deleteByTemplate(templateId, tenantId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/templateItem/deleteByTemplate",
    method: "post",
    params: { templateId, tenantId, dataMonth },
  });
}

/**
 * 更新显示顺序
 * @param {Array} templateItemIds - 模板项目ID列表（按新顺序排列）
 * @returns {Promise} - 返回请求Promise
 */
export function updateDisplayOrder(templateItemIds) {
  return request({
    url: "/api/v1/system/salary/templateItem/updateDisplayOrder",
    method: "post",
    data: templateItemIds,
  });
}

/**
 * 批量启用/禁用模板项目
 * @param {String} ids - ID数组字符串
 * @param {Boolean} status - 状态
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateStatus(ids, status) {
  return request({
    url: "/api/v1/system/salary/templateItem/batchUpdateStatus",
    method: "post",
    params: { ids, status },
  });
}

/**
 * 复制模板项目到新模板
 * @param {Long} sourceTemplateId - 源模板ID
 * @param {Long} targetTemplateId - 目标模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function copyToNewTemplate(sourceTemplateId, targetTemplateId, tenantId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/templateItem/copyToNewTemplate",
    method: "post",
    params: { sourceTemplateId, targetTemplateId, tenantId, dataMonth },
  });
}

/**
 * 获取模板项目的最大显示顺序
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function getMaxDisplayOrder(templateId, tenantId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/templateItem/getMaxDisplayOrder",
    method: "get",
    params: { templateId, tenantId, dataMonth },
  });
}
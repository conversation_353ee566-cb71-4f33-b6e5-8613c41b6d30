import request from "@/utils/request";

/**
 * 获取岗位薪资标准分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有岗位薪资标准数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个岗位薪资标准数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增岗位薪资标准
 * @param {Object} data - 岗位薪资标准数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/insert",
    method: "post",
    data,
  });
}

/**
 * 更新岗位薪资标准
 * @param {Object} data - 岗位薪资标准数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/update",
    method: "post",
    data,
  });
}

/**
 * 删除岗位薪资标准
 * @param {Object} data - 岗位薪资标准ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除岗位薪资标准
 * @param {Object} data - 岗位薪资标准ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据租户ID、数据月份和岗位ID查询薪资标准
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} positionId - 岗位ID
 * @returns {Promise} - 返回请求Promise
 */
export function findByPosition(tenantId, dataMonth, positionId) {
  return request({
    url: "/api/v1/system/salary/positionStandard/findByPosition",
    method: "get",
    params: { tenantId, dataMonth, positionId },
  });
}

/**
 * 根据租户ID、数据月份和模板ID查询薪资标准
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} templateId - 模板ID
 * @returns {Promise} - 返回请求Promise
 */
export function findByTemplate(tenantId, dataMonth, templateId) {
  return request({
    url: "/api/v1/system/salary/positionStandard/findByTemplate",
    method: "get",
    params: { tenantId, dataMonth, templateId },
  });
}

/**
 * 根据岗位ID、模板ID和数据月份查询薪资标准
 * @param {Long} positionId - 岗位ID
 * @param {Long} templateId - 模板ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function findByPositionAndTemplate(positionId, templateId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/positionStandard/findByPositionAndTemplate",
    method: "get",
    params: { positionId, templateId, dataMonth },
  });
}

/**
 * 根据薪资项目ID查询关联的岗位标准
 * @param {Long} salaryItemId - 薪资项目ID
 * @returns {Promise} - 返回请求Promise
 */
export function findBySalaryItem(salaryItemId) {
  return request({
    url: "/api/v1/system/salary/positionStandard/findBySalaryItem",
    method: "get",
    params: { salaryItemId },
  });
}

/**
 * 根据岗位ID、薪资项目ID和数据月份查询薪资标准
 * @param {Long} positionId - 岗位ID
 * @param {Long} salaryItemId - 薪资项目ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function findByPositionAndSalaryItem(positionId, salaryItemId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/positionStandard/findByPositionAndSalaryItem",
    method: "get",
    params: { positionId, salaryItemId, dataMonth },
  });
}

/**
 * 检查岗位薪资标准是否已存在
 * @param {Object} data - 岗位薪资标准数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkExists(data) {
  return request({
    url: "/api/v1/system/salary/positionStandard/checkExists",
    method: "post",
    data,
  });
}

/**
 * 批量保存岗位薪资标准
 * @param {Long} positionId - 岗位ID
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Array} standards - 薪资标准列表
 * @returns {Promise} - 返回请求Promise
 */
export function batchSave(positionId, templateId, tenantId, dataMonth, standards) {
  return request({
    url: "/api/v1/system/salary/positionStandard/batchSave",
    method: "post",
    params: { positionId, templateId, tenantId, dataMonth },
    data: standards,
  });
}

/**
 * 删除岗位薪资标准
 * @param {Long} positionId - 岗位ID
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function deleteByPositionAndTemplate(positionId, templateId, tenantId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/positionStandard/deleteByPositionAndTemplate",
    method: "post",
    params: { positionId, templateId, tenantId, dataMonth },
  });
}

/**
 * 批量启用/禁用自动预填充
 * @param {String} ids - ID数组字符串
 * @param {Boolean} isAutoFill - 是否自动预填充
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateAutoFill(ids, isAutoFill) {
  return request({
    url: "/api/v1/system/salary/positionStandard/batchUpdateAutoFill",
    method: "post",
    params: { ids, isAutoFill },
  });
}

/**
 * 批量设置只读状态
 * @param {String} ids - ID数组字符串
 * @param {Boolean} isReadonly - 是否只读
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateReadonly(ids, isReadonly) {
  return request({
    url: "/api/v1/system/salary/positionStandard/batchUpdateReadonly",
    method: "post",
    params: { ids, isReadonly },
  });
}

/**
 * 复制岗位薪资标准到新模板
 * @param {Long} sourcePositionId - 源岗位ID
 * @param {Long} sourceTemplateId - 源模板ID
 * @param {Long} targetPositionId - 目标岗位ID
 * @param {Long} targetTemplateId - 目标模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function copyToNewTemplate(sourcePositionId, sourceTemplateId, targetPositionId, targetTemplateId, tenantId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/positionStandard/copyToNewTemplate",
    method: "post",
    params: { sourcePositionId, sourceTemplateId, targetPositionId, targetTemplateId, tenantId, dataMonth },
  });
}

/**
 * 复制岗位薪资标准到新月份
 * @param {Long} positionId - 岗位ID
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} sourceMonth - 源月份
 * @param {Date} targetMonth - 目标月份
 * @returns {Promise} - 返回请求Promise
 */
export function copyToNewMonth(positionId, templateId, tenantId, sourceMonth, targetMonth) {
  return request({
    url: "/api/v1/system/salary/positionStandard/copyToNewMonth",
    method: "post",
    params: { positionId, templateId, tenantId, sourceMonth, targetMonth },
  });
}

/**
 * 根据模板项目自动创建岗位标准
 * @param {Long} positionId - 岗位ID
 * @param {Long} templateId - 模板ID
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @returns {Promise} - 返回请求Promise
 */
export function autoCreateFromTemplate(positionId, templateId, tenantId, dataMonth) {
  return request({
    url: "/api/v1/system/salary/positionStandard/autoCreateFromTemplate",
    method: "post",
    params: { positionId, templateId, tenantId, dataMonth },
  });
}

/**
 * 批量导入岗位薪资标准
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Array} standards - 薪资标准列表
 * @returns {Promise} - 返回请求Promise
 */
export function batchImport(tenantId, dataMonth, standards) {
  return request({
    url: "/api/v1/system/salary/positionStandard/batchImport",
    method: "post",
    params: { tenantId, dataMonth },
    data: standards,
  });
}

/**
 * 验证标准值范围
 * @param {String} standardValue - 标准值
 * @param {String} minValue - 最小值
 * @param {String} maxValue - 最大值
 * @returns {Promise} - 返回请求Promise
 */
export function validateValueRange(standardValue, minValue, maxValue) {
  return request({
    url: "/api/v1/system/salary/positionStandard/validateValueRange",
    method: "post",
    params: { standardValue, minValue, maxValue },
  });
}

/**
 * 获取岗位薪资标准统计信息
 * @param {Long} tenantId - 租户ID
 * @param {Date} dataMonth - 数据月份
 * @param {Long} positionId - 岗位ID
 * @returns {Promise} - 返回请求Promise
 */
export function getStatistics(tenantId, dataMonth, positionId) {
  return request({
    url: "/api/v1/system/salary/positionStandard/getStatistics",
    method: "get",
    params: { tenantId, dataMonth, positionId },
  });
}
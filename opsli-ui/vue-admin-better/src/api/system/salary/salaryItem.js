import request from "@/utils/request";

/**
 * 获取薪资项目分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/item/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有薪资项目数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/item/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取启用的薪资项目数据
 * @param {Long} tenantId - 租户ID
 * @returns {Promise} - 返回请求Promise
 */
export function findEnabled(tenantId) {
  return request({
    url: "/api/v1/system/salary/item/findEnabled",
    method: "get",
    params: { tenantId },
  });
}

/**
 * 根据分类获取薪资项目
 * @param {Long} tenantId - 租户ID
 * @param {String} category - 项目分类
 * @returns {Promise} - 返回请求Promise
 */
export function findByCategory(tenantId, category) {
  return request({
    url: "/api/v1/system/salary/item/findByCategory",
    method: "get",
    params: { tenantId, category },
  });
}

/**
 * 获取单个薪资项目数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/item/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增薪资项目
 * @param {Object} data - 薪资项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/item/insert",
    method: "post",
    data,
  });
}

/**
 * 更新薪资项目
 * @param {Object} data - 薪资项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/item/update",
    method: "post",
    data,
  });
}

/**
 * 删除薪资项目
 * @param {Object} data - 薪资项目ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/item/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除薪资项目
 * @param {Object} data - 薪资项目ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/item/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 检查项目名称是否唯一
 * @param {Long} tenantId - 租户ID
 * @param {String} name - 项目名称
 * @param {String} id - 项目ID（编辑时排除自己）
 * @returns {Promise} - 返回请求Promise
 */
export function checkNameUnique(tenantId, name, id) {
  return request({
    url: "/api/v1/system/salary/item/checkNameUnique",
    method: "post",
    params: { tenantId, name, id },
  });
}

/**
 * 批量更新状态
 * @param {Long} itemId - 薪资项目ID
 * @param {Boolean} status - 状态
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateStatus(itemId, status) {
  return request({
    url: "/api/v1/system/salary/item/updateStatus",
    method: "post",
    params: { itemId, status },
  });
}

/**
 * 初始化系统项目
 * @param {Long} tenantId - 租户ID
 * @returns {Promise} - 返回请求Promise
 */
export function initSystemItems(tenantId) {
  return request({
    url: "/api/v1/system/salary/item/initSystemItems",
    method: "post",
    params: { tenantId },
  });
}

/**
 * 验证计算公式
 * @param {String} formula - 计算公式
 * @returns {Promise} - 返回请求Promise
 */
export function validateFormula(formula) {
  return request({
    url: "/api/v1/system/salary/item/validateFormula",
    method: "post",
    params: { formula },
  });
}

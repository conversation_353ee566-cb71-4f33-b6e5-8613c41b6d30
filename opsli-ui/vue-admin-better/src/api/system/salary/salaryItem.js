import request from "@/utils/request";

/**
 * 获取薪资项目分页数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getList(data) {
  return request({
    url: "/api/v1/system/salary/item/findPage",
    method: "get",
    params: data,
  });
}

/**
 * 获取所有薪资项目数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getAll(data) {
  return request({
    url: "/api/v1/system/salary/item/findAll",
    method: "get",
    params: data,
  });
}

/**
 * 获取单个薪资项目数据
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOne(data) {
  return request({
    url: "/api/v1/system/salary/item/get",
    method: "get",
    params: data,
  });
}

/**
 * 新增薪资项目
 * @param {Object} data - 薪资项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function doInsert(data) {
  return request({
    url: "/api/v1/system/salary/item/insert",
    method: "post",
    data,
  });
}

/**
 * 更新薪资项目
 * @param {Object} data - 薪资项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function doUpdate(data) {
  return request({
    url: "/api/v1/system/salary/item/update",
    method: "post",
    data,
  });
}

/**
 * 删除薪资项目
 * @param {Object} data - 薪资项目ID
 * @returns {Promise} - 返回请求Promise
 */
export function doDelete(data) {
  return request({
    url: "/api/v1/system/salary/item/del",
    method: "post",
    params: data,
  });
}

/**
 * 批量删除薪资项目
 * @param {Object} data - 薪资项目ID数组
 * @returns {Promise} - 返回请求Promise
 */
export function doDeleteAll(data) {
  return request({
    url: "/api/v1/system/salary/item/delAll",
    method: "post",
    params: data,
  });
}

/**
 * 根据分类获取薪资项目
 * @param {String} category - 分类
 * @returns {Promise} - 返回请求Promise
 */
export function findByCategory(category) {
  return request({
    url: "/api/v1/system/salary/item/findByCategory",
    method: "get",
    params: { category },
  });
}

/**
 * 获取启用的薪资项目
 * @returns {Promise} - 返回请求Promise
 */
export function findEnabled() {
  return request({
    url: "/api/v1/system/salary/item/findEnabled",
    method: "get",
  });
}

/**
 * 检查薪资项目名称是否唯一
 * @param {Object} data - 薪资项目数据
 * @returns {Promise} - 返回请求Promise
 */
export function checkNameUnique(data) {
  return request({
    url: "/api/v1/system/salary/item/checkNameUnique",
    method: "post",
    data,
  });
}

/**
 * 批量启用/禁用薪资项目
 * @param {String} ids - ID数组字符串
 * @param {Boolean} status - 状态
 * @returns {Promise} - 返回请求Promise
 */
export function batchUpdateStatus(ids, status) {
  return request({
    url: "/api/v1/system/salary/item/batchUpdateStatus",
    method: "post",
    params: { ids, status },
  });
}

/**
 * 初始化系统内置薪资项目
 * @returns {Promise} - 返回请求Promise
 */
export function initSystemItems() {
  return request({
    url: "/api/v1/system/salary/item/initSystemItems",
    method: "post",
  });
}

/**
 * 验证计算公式
 * @param {String} formula - 计算公式
 * @returns {Promise} - 返回请求Promise
 */
export function validateFormula(formula) {
  return request({
    url: "/api/v1/system/salary/item/validateFormula",
    method: "post",
    params: { formula },
  });
}

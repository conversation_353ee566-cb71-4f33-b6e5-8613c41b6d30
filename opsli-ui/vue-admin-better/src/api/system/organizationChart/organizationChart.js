import request from "@/utils/request";

/**
 * 获取完整的组织架构树
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOrganizationTree(data) {
  return request({
    url: "/api/v1/system/organizationChart/getOrganizationTree",
    method: "get",
    params: data,
  });
}

/**
 * 获取指定部门的组织架构子树
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getDepartmentTree(data) {
  return request({
    url: "/api/v1/system/organizationChart/getDepartmentTree",
    method: "get",
    params: data,
  });
}

/**
 * 获取组织架构统计信息
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function getOrganizationStats(data) {
  return request({
    url: "/api/v1/system/organizationChart/getOrganizationStats",
    method: "get",
    params: data,
  });
}

/**
 * 搜索组织架构节点
 * @param {Object} data - 查询参数
 * @returns {Promise} - 返回请求Promise
 */
export function searchNodes(data) {
  return request({
    url: "/api/v1/system/organizationChart/searchNodes",
    method: "get",
    params: data,
  });
}

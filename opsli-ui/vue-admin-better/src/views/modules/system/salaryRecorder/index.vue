<template>
  <div class="salary-recorder-container">
    <el-row :gutter="15">
      <el-col>
        <!-- 查询表单 -->
        <vab-query-form>
          <vab-query-form-left-panel :span="6">
            <el-button
              v-if="$perms('system_salary_employee_insert')"
              icon="el-icon-plus"
              type="primary"
              @click="handleInsert"
            > 录入薪资 </el-button>
            <el-button
              v-if="$perms('system_salary_employee_update')"
              icon="el-icon-refresh"
              type="success"
              @click="handleBatchCalculate"
              :disabled="!selectedRows.length"
            > 批量计算 </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="18">
            <el-form :inline="true" :model="queryForm" @submit.native.prevent>
              <el-form-item>
                <el-date-picker
                  v-model="queryForm.dataMonth"
                  type="month"
                  placeholder="选择数据月份"
                  format="yyyy-MM"
                  value-format="yyyy-MM-dd"
                  @change="handleMonthChange"
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.employeeName_LIKE"
                  placeholder="请输入员工姓名"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.employeeNumber_LIKE"
                  placeholder="请输入员工编号"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.status_EQ"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="草稿" value="草稿"></el-option>
                  <el-option label="待审核" value="待审核"></el-option>
                  <el-option label="已审核" value="已审核"></el-option>
                  <el-option label="已发放" value="已发放"></el-option>
                  <el-option label="已归档" value="已归档"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="queryData">
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <!-- 员工薪资列表 -->
        <el-table
          ref="salaryTable"
          v-loading="listLoading"
          :data="salaryData"
          :element-loading-text="elementLoadingText"
          :highlight-current-row="true"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="employeeName"
            label="员工姓名"
            min-width="100"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="employeeNumber"
            label="员工编号"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="dataMonth"
            label="数据月份"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatMonth(scope.row.dataMonth) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="templateName"
            label="薪资模板"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="baseSalaryTotal"
            label="基本工资"
            min-width="100"
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ formatMoney(scope.row.baseSalaryTotal) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="allowanceTotal"
            label="补贴合计"
            min-width="100"
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ formatMoney(scope.row.allowanceTotal) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="commissionTotal"
            label="提成合计"
            min-width="100"
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ formatMoney(scope.row.commissionTotal) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="bonusTotal"
            label="奖金合计"
            min-width="100"
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ formatMoney(scope.row.bonusTotal) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="deductionTotal"
            label="扣除合计"
            min-width="100"
            align="right"
          >
            <template slot-scope="scope">
              <span>{{ formatMoney(scope.row.deductionTotal) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="grossSalary"
            label="应发工资"
            min-width="120"
            align="right"
          >
            <template slot-scope="scope">
              <span class="gross-salary">{{ formatMoney(scope.row.grossSalary) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="netSalary"
            label="实发工资"
            min-width="120"
            align="right"
          >
            <template slot-scope="scope">
              <span class="net-salary">{{ formatMoney(scope.row.netSalary) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="状态"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" min-width="200" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="$perms('system_salary_employee_select')"
                type="text"
                @click="handleViewDetail(scope.row)"
              >
                查看明细
              </el-button>
              <el-button
                v-if="$perms('system_salary_employee_update') && scope.row.status === '草稿'"
                type="text"
                @click="handleUpdate(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="$perms('system_salary_employee_update') && scope.row.status === '草稿'"
                type="text"
                @click="handleCalculate(scope.row)"
              >
                计算
              </el-button>
              <el-button
                v-if="$perms('system_salary_employee_update') && scope.row.status === '待审核'"
                type="text"
                @click="handleApprove(scope.row)"
              >
                审批
              </el-button>
              <el-button
                v-if="$perms('system_salary_employee_delete') && scope.row.status === '草稿'"
                type="text"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>

    <!-- 薪资录入编辑对话框 -->
    <employee-salary-edit ref="salaryEdit" @fetchData="fetchSalaryData"></employee-salary-edit>

    <!-- 薪资明细查看对话框 -->
    <employee-salary-detail ref="salaryDetail"></employee-salary-detail>
  </div>
</template>

<script>
import { getList, doDelete, calculateSalary, batchCalculateSalary, approve } from '@/api/system/salary/employeeSalary';
import EmployeeSalaryEdit from './components/EmployeeSalaryEdit'
import EmployeeSalaryDetail from './components/EmployeeSalaryDetail'

export default {
  name: 'SalaryRecorder',
  components: {
    EmployeeSalaryEdit,
    EmployeeSalaryDetail
  },
  data() {
    return {
      salaryList: null,
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      selectedRows: [],
      elementLoadingText: "正在加载...",
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        dataMonth: this.getCurrentMonth(),
        employeeName_LIKE: "",
        employeeNumber_LIKE: "",
        status_EQ: "",
      },
    };
  },
  computed: {
    salaryData() {
      return this.salaryList || [];
    },
  },
  created() {
    this.fetchSalaryData();
  },
  methods: {
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleInsert() {
      this.$refs["salaryEdit"].showEdit(null, this.queryForm.dataMonth);
    },
    handleUpdate(row) {
      if (row.id) {
        this.$refs["salaryEdit"].showEdit(row);
      }
    },
    handleViewDetail(row) {
      if (row.id) {
        this.$refs["salaryDetail"].showDetail(row);
      }
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前员工薪资记录吗", null, async () => {
          const { msg } = await doDelete({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchSalaryData();
        });
      }
    },
    handleCalculate(row) {
      if (row.id) {
        this.$baseConfirm("你确定要重新计算当前员工薪资吗", null, async () => {
          try {
            const { msg } = await calculateSalary(row.employeeId, row.dataMonth);
            this.$baseMessage(msg, "success");
            await this.fetchSalaryData();
          } catch (error) {
            this.$baseMessage(error.response?.data?.msg || "计算失败", "error");
          }
        });
      }
    },
    handleBatchCalculate() {
      if (!this.selectedRows.length) {
        this.$baseMessage("请先选择要计算的员工薪资记录", "warning");
        return;
      }

      this.$baseConfirm("你确定要批量计算选中的员工薪资吗", null, async () => {
        try {
          const employeeIds = this.selectedRows.map(row => row.employeeId).join(',');
          const dataMonth = this.queryForm.dataMonth;
          const { msg } = await batchCalculateSalary(employeeIds, dataMonth);
          this.$baseMessage(msg, "success");
          await this.fetchSalaryData();
        } catch (error) {
          this.$baseMessage(error.response?.data?.msg || "批量计算失败", "error");
        }
      });
    },
    handleApprove(row) {
      if (row.id) {
        this.$baseConfirm("你确定要审批通过当前员工薪资吗", null, async () => {
          try {
            const { msg } = await approve(row.id);
            this.$baseMessage(msg, "success");
            await this.fetchSalaryData();
          } catch (error) {
            this.$baseMessage(error.response?.data?.msg || "审批失败", "error");
          }
        });
      }
    },
    handleMonthChange(value) {
      this.queryForm.dataMonth = value;
      this.queryData();
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchSalaryData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchSalaryData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchSalaryData();
    },
    async fetchSalaryData() {
      this.listLoading = true;
      try {
        const { data } = await getList(this.queryForm);
        this.salaryList = data.records;
        this.total = data.total;
      } catch (error) {
        this.$baseMessage("获取数据失败", "error");
      } finally {
        this.listLoading = false;
      }
    },
    getCurrentMonth() {
      const date = new Date();
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      return `${year}-${month}-01`;
    },
    formatMonth(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      return `${year}-${month}`;
    },
    formatMoney(amount) {
      if (amount == null || amount === '') return '0.00';
      return Number(amount).toFixed(2);
    },
    getStatusTagType(status) {
      const typeMap = {
        '草稿': 'info',
        '待审核': 'warning',
        '已审核': 'success',
        '已发放': 'primary',
        '已归档': 'default'
      };
      return typeMap[status] || 'default';
    },
  },
};
</script>

<style lang="scss" scoped>
.salary-recorder-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.gross-salary {
  font-weight: bold;
  color: #409eff;
}

.net-salary {
  font-weight: bold;
  color: #67c23a;
}
</style>
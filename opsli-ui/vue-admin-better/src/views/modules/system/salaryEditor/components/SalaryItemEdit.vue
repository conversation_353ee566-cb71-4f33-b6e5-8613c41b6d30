<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="800px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="项目名称" prop="name">
            <el-input v-model="form.name" autocomplete="off" placeholder="请输入项目名称"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="项目分类" prop="category">
            <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
              <el-option label="基本工资" value="基本工资"></el-option>
              <el-option label="补贴" value="补贴"></el-option>
              <el-option label="提成" value="提成"></el-option>
              <el-option label="奖金" value="奖金"></el-option>
              <el-option label="扣除" value="扣除"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="数据类型" prop="dataType">
            <el-select v-model="form.dataType" placeholder="请选择数据类型" style="width: 100%" @change="handleDataTypeChange">
              <el-option label="小数" value="decimal"></el-option>
              <el-option label="整数" value="integer"></el-option>
              <el-option label="百分比" value="percentage"></el-option>
              <el-option label="文本" value="text"></el-option>
              <el-option label="布尔值" value="boolean"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="form.unit" autocomplete="off" placeholder="如：元、%、天等"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12" v-if="form.dataType === 'decimal'">
          <el-form-item label="小数位数" prop="decimalPlaces">
            <el-input-number v-model="form.decimalPlaces" :min="0" :max="6" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="项目状态" prop="status">
            <el-switch
              v-model="form.status"
              active-text="启用"
              inactive-text="禁用"
            ></el-switch>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="计算公式" prop="calculationFormula">
            <el-input
              v-model="form.calculationFormula"
              type="textarea"
              :rows="3"
              placeholder="可选，支持动态计算公式，如：${基本工资} * ${绩效占比} / 100"
            ></el-input>
            <div class="formula-help">
              <small class="text-muted">
                公式说明：使用 ${项目名称} 引用其他薪资项目的值，支持基本数学运算符 +、-、*、/、()
              </small>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
      <el-button v-if="form.calculationFormula" type="info" @click="validateFormula">验证公式</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { doInsert, doUpdate, checkNameUnique, validateFormula } from '@/api/system/salary/salaryItem';
import { isNull } from "@/utils/validate";

export default {
  name: "SalaryItemEdit",
  data() {
    return {
      form: {
        tenantId: null,
        name: '',
        category: '',
        dataType: 'decimal',
        unit: '',
        decimalPlaces: 2,
        calculationFormula: '',
        isSystemItem: false,
        status: true
      },
      rules: {
        name: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
          { min: 1, max: 100, message: "长度在 1 到 100 个字符", trigger: "blur" },
          { validator: this.validateName, trigger: "blur" }
        ],
        category: [
          { required: true, message: "请选择项目分类", trigger: "change" }
        ],
        dataType: [
          { required: true, message: "请选择数据类型", trigger: "change" }
        ]
      },
      dialogFormVisible: false,
      title: "",
      originalName: '',
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
    }),
  },
  methods: {
    // 显示编辑对话框
    async showEdit(row) {
      if (!row) {
        // 新增模式
        this.title = "添加薪资项目";
        // 重置表单为初始状态
        this.form = {
          tenantId: this.tenantId,
          name: '',
          category: '',
          dataType: 'decimal',
          unit: '',
          decimalPlaces: 2,
          calculationFormula: '',
          isSystemItem: false,
          status: true
        };
        this.originalName = '';
      } else {
        // 编辑模式
        this.title = "编辑薪资项目";
        this.form = Object.assign({}, row);
        this.originalName = row.name;
      }

      this.dialogFormVisible = true;
    },

    // 关闭对话框
    close() {
      this.dialogFormVisible = false;
      this.$refs["form"].resetFields();
      // 重置表单为初始状态
      this.form = {
        tenantId: null,
        name: '',
        category: '',
        dataType: 'decimal',
        unit: '',
        decimalPlaces: 2,
        calculationFormula: '',
        isSystemItem: false,
        status: true
      };
      this.originalName = '';
    },

    // 保存薪资项目
    save() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          try {
            // 修改
            if (!isNull(this.form.id)) {
              const { msg } = await doUpdate(this.form);
              this.$baseMessage(msg, "success");
            } else {
              const { msg } = await doInsert(this.form);
              this.$baseMessage(msg, "success");
            }

            await this.$emit("fetchData");
            this.close();
          } catch (error) {
            console.error("保存薪资项目失败:", error);
            this.$baseMessage("保存薪资项目失败，请稍后重试", "error");
          }
        } else {
          return false;
        }
      });
    },

    // 验证项目名称唯一性
    async validateName(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      // 如果是编辑模式且名称未改变，则不需要验证
      if (this.form.id && value === this.originalName) {
        callback();
        return;
      }

      try {
        const { data } = await checkNameUnique(this.tenantId, value, this.form.id);
        
        if (!data) {
          callback(new Error('项目名称已存在'));
        } else {
          callback();
        }
      } catch (error) {
        console.error("验证项目名称失败:", error);
        // 验证失败时不阻止用户继续操作，但记录错误
        callback();
      }
    },

    // 数据类型改变时的处理
    handleDataTypeChange(value) {
      if (value === 'decimal') {
        this.form.decimalPlaces = 2;
        if (!this.form.unit) {
          this.form.unit = '元';
        }
      } else if (value === 'percentage') {
        this.form.decimalPlaces = 2;
        this.form.unit = '%';
      } else if (value === 'integer') {
        this.form.decimalPlaces = 0;
      } else {
        this.form.decimalPlaces = null;
      }
    },

    // 验证计算公式
    async validateFormula() {
      if (!this.form.calculationFormula) {
        this.$baseMessage("请先输入计算公式", "warning");
        return;
      }

      try {
        const { data } = await validateFormula(this.form.calculationFormula);
        if (data) {
          this.$baseMessage("公式验证通过", "success");
        } else {
          this.$baseMessage("公式格式不正确，请检查语法", "error");
        }
      } catch (error) {
        console.error("验证公式失败:", error);
        this.$baseMessage("验证公式失败", "error");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.formula-help {
  margin-top: 5px;
  
  .text-muted {
    color: #999;
    font-size: 12px;
    line-height: 1.4;
  }
}
</style>

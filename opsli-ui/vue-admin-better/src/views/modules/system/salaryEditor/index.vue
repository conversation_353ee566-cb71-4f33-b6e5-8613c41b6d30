<template>
  <div class="salary-editor-container">
    <el-row :gutter="15">
      <el-col>
        <vab-query-form>
          <vab-query-form-left-panel :span="6">
            <el-button
              v-if="$perms('system_salary_item_insert')"
              icon="el-icon-plus"
              type="primary"
              @click="handleInsert"
            > 添加薪资项目 </el-button>
            <el-button
              v-if="$perms('system_salary_item_insert')"
              icon="el-icon-setting"
              type="success"
              @click="handleInitSystem"
            > 初始化系统项目 </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="18">
            <el-form :inline="true" :model="queryForm" @submit.native.prevent>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.name_LIKE"
                  placeholder="请输入项目名称"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.category_EQ"
                  placeholder="请选择分类"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="基本工资" value="基本工资"></el-option>
                  <el-option label="补贴" value="补贴"></el-option>
                  <el-option label="提成" value="提成"></el-option>
                  <el-option label="奖金" value="奖金"></el-option>
                  <el-option label="扣除" value="扣除"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.dataType_EQ"
                  placeholder="请选择数据类型"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="小数" value="decimal"></el-option>
                  <el-option label="整数" value="integer"></el-option>
                  <el-option label="百分比" value="percentage"></el-option>
                  <el-option label="文本" value="text"></el-option>
                  <el-option label="布尔值" value="boolean"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="queryData">
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <el-table
          ref="salaryItemTable"
          v-loading="listLoading"
          :data="tableData"
          :element-loading-text="elementLoadingText"
          :highlight-current-row="true"
          @current-change="setSelectRows"
        >
          <el-table-column
            show-overflow-tooltip
            prop="name"
            label="项目名称"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="category"
            label="分类"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="getCategoryTagType(scope.row.category)">
                {{ scope.row.category }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="dataType"
            label="数据类型"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatDataType(scope.row.dataType) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="unit"
            label="单位"
            min-width="80"
            align="center"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="decimalPlaces"
            label="小数位数"
            min-width="100"
            align="center"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="isSystemItem"
            label="系统内置"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.isSystemItem ? 'warning' : 'info'">
                {{ scope.row.isSystemItem ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="状态"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.status ? 'success' : 'danger'">
                {{ scope.row.status ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="createTime"
            label="创建时间"
            min-width="150"
          ></el-table-column>

          <el-table-column label="操作" min-width="180" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="$perms('system_salary_item_update')"
                type="text"
                @click="handleUpdate(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="$perms('system_salary_item_delete') && !scope.row.isSystemItem"
                type="text"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
              <el-button
                v-if="$perms('system_salary_item_update')"
                type="text"
                @click="handleToggleStatus(scope.row)"
              >
                {{ scope.row.status ? '禁用' : '启用' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>

    <!-- 编辑对话框 -->
    <salary-item-edit ref="edit" @fetchData="fetchData"></salary-item-edit>
  </div>
</template>

<script>
import { getList, doDelete, batchUpdateStatus, initSystemItems } from '@/api/system/salary/salaryItem';
import SalaryItemEdit from './components/SalaryItemEdit'

export default {
  name: 'SalaryEditor',
  components: { SalaryItemEdit },
  data() {
    return {
      list: null,
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      currentRow: null,
      elementLoadingText: "正在加载...",
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        name_LIKE: "",
        category_EQ: "",
        dataType_EQ: "",
      },
    };
  },
  computed: {
    tableData() {
      return this.list || [];
    },
  },
  created() {
    this.fetchData();
  },
  methods: {
    setSelectRows(val) {
      this.currentRow = val;
    },
    handleInsert() {
      this.$refs["edit"].showEdit(null);
    },
    handleUpdate(row) {
      if (row.id) {
        this.$refs["edit"].showEdit(row);
      }
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前薪资项目吗", null, async () => {
          const { msg } = await doDelete({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchData();
        });
      }
    },
    handleToggleStatus(row) {
      if (row.id) {
        const action = row.status ? '禁用' : '启用';
        this.$baseConfirm(`你确定要${action}当前薪资项目吗`, null, async () => {
          const { msg } = await batchUpdateStatus(row.id, !row.status);
          this.$baseMessage(msg, "success");
          await this.fetchData();
        });
      }
    },
    handleInitSystem() {
      this.$baseConfirm("你确定要初始化系统内置薪资项目吗？这将创建标准的薪资项目模板", null, async () => {
        try {
          const { msg } = await initSystemItems();
          this.$baseMessage(msg, "success");
          await this.fetchData();
        } catch (error) {
          this.$baseMessage(error.response?.data?.msg || "初始化失败", "error");
        }
      });
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchData();
    },
    async fetchData() {
      this.listLoading = true;
      try {
        const { data } = await getList(this.queryForm);
        this.list = data.records;
        this.total = data.total;
      } catch (error) {
        this.$baseMessage("获取数据失败", "error");
      } finally {
        this.listLoading = false;
      }
    },
    getCategoryTagType(category) {
      const typeMap = {
        '基本工资': 'primary',
        '补贴': 'success',
        '提成': 'warning',
        '奖金': 'danger',
        '扣除': 'info'
      };
      return typeMap[category] || 'default';
    },
    formatDataType(dataType) {
      const typeMap = {
        'decimal': '小数',
        'integer': '整数',
        'percentage': '百分比',
        'text': '文本',
        'boolean': '布尔值'
      };
      return typeMap[dataType] || dataType;
    },
  },
};
</script>

<style lang="scss" scoped>
.salary-editor-container {
  padding: 20px;
  background-color: #f0f2f5;
}
</style>
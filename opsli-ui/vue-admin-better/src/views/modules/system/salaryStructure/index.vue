<template>
  <div class="salary-structure-container">
    <el-row :gutter="15">
      <el-col>
        <!-- 查询表单 -->
        <vab-query-form>
          <vab-query-form-left-panel :span="6">
            <el-button
              v-if="$perms('system_salary_template_insert')"
              icon="el-icon-plus"
              type="primary"
              @click="handleInsertTemplate"
            > 新建薪资模板 </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="18">
            <el-form :inline="true" :model="queryForm" @submit.native.prevent>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.name_LIKE"
                  placeholder="请输入模板名称"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-select
                  v-model="queryForm.scopeType_EQ"
                  placeholder="请选择适用范围"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="全部" value="全部"></el-option>
                  <el-option label="部门" value="部门"></el-option>
                  <el-option label="岗位" value="岗位"></el-option>
                  <el-option label="员工" value="员工"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="queryData">
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <!-- 薪资模板列表 -->
        <el-table
          ref="templateTable"
          v-loading="listLoading"
          :data="templateData"
          :element-loading-text="elementLoadingText"
          :highlight-current-row="true"
          @current-change="setSelectTemplate"
        >
          <el-table-column
            show-overflow-tooltip
            prop="name"
            label="模板名称"
            min-width="150"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="dataMonth"
            label="数据月份"
            min-width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ formatMonth(scope.row.dataMonth) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="scopeType"
            label="适用范围"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="getScopeTagType(scope.row.scopeType)">
                {{ scope.row.scopeType }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="priority"
            label="优先级"
            min-width="80"
            align="center"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="isDefault"
            label="默认模板"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.isDefault ? 'success' : 'info'">
                {{ scope.row.isDefault ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="状态"
            min-width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="scope.row.status ? 'success' : 'danger'">
                {{ scope.row.status ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="description"
            label="描述"
            min-width="200"
          ></el-table-column>

          <el-table-column label="操作" min-width="250" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="$perms('system_salary_template_select')"
                type="text"
                @click="handleConfigTemplate(scope.row)"
              >
                配置项目
              </el-button>
              <el-button
                v-if="$perms('system_salary_template_update')"
                type="text"
                @click="handleUpdateTemplate(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="$perms('system_salary_template_update') && !scope.row.isDefault"
                type="text"
                @click="handleSetDefault(scope.row)"
              >
                设为默认
              </el-button>
              <el-button
                v-if="$perms('system_salary_template_delete')"
                type="text"
                @click="handleDeleteTemplate(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>

    <!-- 薪资模板编辑对话框 -->
    <salary-template-edit ref="templateEdit" @fetchData="fetchTemplateData"></salary-template-edit>

    <!-- 薪资项目配置对话框 -->
    <salary-template-config ref="templateConfig" @fetchData="fetchTemplateData"></salary-template-config>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getList as getTemplateList, doDelete as deleteTemplate, setDefault } from '@/api/system/salary/salaryTemplate';
import SalaryTemplateEdit from './components/SalaryTemplateEdit'
import SalaryTemplateConfig from './components/SalaryTemplateConfig'

export default {
  name: 'SalaryStructure',
  components: {
    SalaryTemplateEdit,
    SalaryTemplateConfig
  },
  data() {
    return {
      templateList: null,
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      currentTemplate: null,
      elementLoadingText: "正在加载...",
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        name_LIKE: "",
        scopeType_EQ: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
    templateData() {
      return this.templateList || [];
    },
    readyToFetch() {
      return this.tenantId && this.selectedMonth;
    },
  },
  watch: {
    readyToFetch: {
      handler(newVal) {
        if (newVal) {
          this.fetchTemplateData();
        }
      },
      immediate: true,
    },
  },
  created() {
    // 移除直接调用fetchTemplateData，改为通过watch监听
  },
  methods: {
    setSelectTemplate(val) {
      this.currentTemplate = val;
    },
    handleInsertTemplate() {
      this.$refs["templateEdit"].showEdit(null, this.selectedMonth);
    },
    handleUpdateTemplate(row) {
      if (row.id) {
        this.$refs["templateEdit"].showEdit(row);
      }
    },
    handleConfigTemplate(row) {
      if (row.id) {
        this.$refs["templateConfig"].showConfig(row);
      }
    },
    handleDeleteTemplate(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前薪资模板吗", null, async () => {
          const { msg } = await deleteTemplate({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchTemplateData();
        });
      }
    },
    handleSetDefault(row) {
      if (row.id) {
        this.$baseConfirm("你确定要将此模板设为默认模板吗", null, async () => {
          const { msg } = await setDefault(row.id, row.dataMonth);
          this.$baseMessage(msg, "success");
          await this.fetchTemplateData();
        });
      }
    },

    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchTemplateData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchTemplateData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchTemplateData();
    },
    async fetchTemplateData() {
      if (!this.readyToFetch) {
        return;
      }

      this.listLoading = true;
      try {
        // 添加租户ID和数据月份到查询参数
        const queryParams = {
          ...this.queryForm,
          tenantId: this.tenantId,
          dataMonth: this.selectedMonth,
        };
        const { data } = await getTemplateList(queryParams);
        this.templateList = data.records;
        this.total = data.total;
      } catch (error) {
        this.$baseMessage("获取数据失败", "error");
      } finally {
        this.listLoading = false;
      }
    },

    formatMonth(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      return `${year}-${month}`;
    },
    getScopeTagType(scopeType) {
      const typeMap = {
        '全部': 'primary',
        '部门': 'success',
        '岗位': 'warning',
        '员工': 'danger'
      };
      return typeMap[scopeType] || 'default';
    },
  },
};
</script>

<style lang="scss" scoped>
.salary-structure-container {
  padding: 20px;
  background-color: #f0f2f5;
}
</style>
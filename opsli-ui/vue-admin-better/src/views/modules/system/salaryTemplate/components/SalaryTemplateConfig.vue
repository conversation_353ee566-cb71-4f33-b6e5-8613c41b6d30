<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="template-config-container">
      <!-- 模板信息 -->
      <el-card class="template-info-card" shadow="never">
        <div slot="header" class="clearfix">
          <span>模板信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">{{ templateInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="数据月份">{{ formatMonth(templateInfo.dataMonth) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 薪资项目配置 -->
      <el-card class="template-items-card" shadow="never">
        <div slot="header" class="clearfix">
          <span>薪资项目配置</span>
          <el-button
            style="float: right; padding: 3px 0"
            type="text"
            @click="handleAddItem"
          >
            添加项目
          </el-button>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            @click="handleAddItem"
          >
            添加项目
          </el-button>
          <el-button
            size="small"
            type="success"
            icon="el-icon-check"
            :disabled="selectedItems.length === 0"
            @click="handleBatchEnable"
          >
            批量启用
          </el-button>
          <el-button
            size="small"
            type="warning"
            icon="el-icon-close"
            :disabled="selectedItems.length === 0"
            @click="handleBatchDisable"
          >
            批量禁用
          </el-button>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            :disabled="selectedItems.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
        </div>

        <!-- 项目列表 -->
        <el-table
          ref="itemsTable"
          v-loading="itemsLoading"
          :data="templateItems"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          
          <el-table-column
            prop="displayOrder"
            label="显示顺序"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.displayOrder"
                :min="1"
                :max="999"
                size="mini"
                @change="handleOrderChange"
              ></el-input-number>
            </template>
          </el-table-column>

          <el-table-column
            prop="salaryItemName"
            label="薪资项目"
            min-width="150"
          ></el-table-column>

          <el-table-column
            prop="salaryItemCategory"
            label="项目分类"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag :type="getCategoryTagType(scope.row.salaryItemCategory)">
                {{ scope.row.salaryItemCategory }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="isRequired"
            label="是否必填"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.isRequired"
                active-color="#13ce66"
                inactive-color="#ff4949"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column
            prop="isEditable"
            label="是否可编辑"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.isEditable"
                active-color="#13ce66"
                inactive-color="#ff4949"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column
            prop="defaultValue"
            label="默认值"
            width="120"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.defaultValue"
                size="mini"
                placeholder="请输入默认值"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column
            prop="status"
            label="状态"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleEditValidation(scope.row)"
              >
                验证规则
              </el-button>
              <el-button
                size="mini"
                type="text"
                style="color: #f56c6c"
                @click="handleDeleteItem(scope.row, scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saveLoading">保存配置</el-button>
    </div>

    <!-- 添加项目对话框 -->
    <el-dialog
      title="添加薪资项目"
      :visible.sync="addItemDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-table
        ref="salaryItemsTable"
        v-loading="salaryItemsLoading"
        :data="availableSalaryItems"
        @selection-change="handleSalaryItemSelection"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="name" label="项目名称" min-width="150"></el-table-column>
        <el-table-column prop="category" label="项目分类" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getCategoryTagType(scope.row.category)">
              {{ scope.row.category }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dataType" label="数据类型" width="100" align="center"></el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="addItemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmAddItems" :disabled="selectedSalaryItems.length === 0">
          确定添加 ({{ selectedSalaryItems.length }})
        </el-button>
      </div>
    </el-dialog>

    <!-- 验证规则对话框 -->
    <el-dialog
      title="配置验证规则"
      :visible.sync="validationDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="validationForm" label-width="120px">
        <el-form-item label="最小值">
          <el-input v-model="validationForm.min" placeholder="请输入最小值"></el-input>
        </el-form-item>
        <el-form-item label="最大值">
          <el-input v-model="validationForm.max" placeholder="请输入最大值"></el-input>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-switch v-model="validationForm.required"></el-switch>
        </el-form-item>
        <el-form-item label="自定义规则">
          <el-input
            v-model="validationForm.pattern"
            placeholder="请输入正则表达式"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="validationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveValidation">保存</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { 
  findByTemplate, 
  batchSave, 
  batchUpdateStatus, 
  updateDisplayOrder 
} from '@/api/system/salary/salaryTemplateItem';
import { findEnabled } from '@/api/system/salary/salaryItem';

export default {
  name: 'SalaryTemplateConfig',
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '配置薪资模板项目',
      templateInfo: {},
      templateItems: [],
      selectedItems: [],
      itemsLoading: false,
      saveLoading: false,
      
      // 添加项目相关
      addItemDialogVisible: false,
      availableSalaryItems: [],
      selectedSalaryItems: [],
      salaryItemsLoading: false,
      
      // 验证规则相关
      validationDialogVisible: false,
      validationForm: {
        min: '',
        max: '',
        required: false,
        pattern: ''
      },
      currentValidationItem: null,
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
    }),
  },
  methods: {
    showConfig(template) {
      this.templateInfo = { ...template };
      this.dialogTitle = `配置薪资模板项目 - ${template.name}`;
      this.dialogVisible = true;
      this.loadTemplateItems();
    },

    async loadTemplateItems() {
      if (!this.templateInfo.id) return;
      
      this.itemsLoading = true;
      try {
        const { data } = await findByTemplate(
          this.tenantId,
          this.templateInfo.dataMonth,
          this.templateInfo.id
        );
        this.templateItems = data || [];
        // 按显示顺序排序
        this.templateItems.sort((a, b) => a.displayOrder - b.displayOrder);
      } catch (error) {
        this.$baseMessage('加载模板项目失败', 'error');
      } finally {
        this.itemsLoading = false;
      }
    },

    async loadAvailableSalaryItems() {
      this.salaryItemsLoading = true;
      try {
        const { data } = await findEnabled(this.tenantId);
        // 过滤掉已经添加的项目
        const existingItemIds = this.templateItems.map(item => item.salaryItemId);
        this.availableSalaryItems = (data || []).filter(
          item => !existingItemIds.includes(item.id)
        );
      } catch (error) {
        this.$baseMessage('加载薪资项目失败', 'error');
      } finally {
        this.salaryItemsLoading = false;
      }
    },

    handleAddItem() {
      this.addItemDialogVisible = true;
      this.loadAvailableSalaryItems();
    },

    handleSalaryItemSelection(selection) {
      this.selectedSalaryItems = selection;
    },

    handleConfirmAddItems() {
      if (this.selectedSalaryItems.length === 0) {
        this.$baseMessage('请选择要添加的薪资项目', 'warning');
        return;
      }

      // 获取当前最大显示顺序
      const maxOrder = Math.max(...this.templateItems.map(item => item.displayOrder || 0), 0);
      
      // 添加选中的项目
      this.selectedSalaryItems.forEach((salaryItem, index) => {
        const newItem = {
          id: null, // 新项目没有ID
          tenantId: this.tenantId,
          dataMonth: this.templateInfo.dataMonth,
          templateId: this.templateInfo.id,
          salaryItemId: salaryItem.id,
          salaryItemName: salaryItem.name,
          salaryItemCategory: salaryItem.category,
          displayOrder: maxOrder + index + 1,
          isRequired: true,
          isEditable: true,
          defaultValue: '',
          validationRules: '',
          status: true
        };
        this.templateItems.push(newItem);
      });

      const addedCount = this.selectedSalaryItems.length;
      this.addItemDialogVisible = false;
      this.selectedSalaryItems = [];
      this.$baseMessage(`成功添加 ${addedCount} 个薪资项目`, 'success');
    },

    handleDeleteItem(item, index) {
      this.$baseConfirm('确定要删除这个薪资项目吗？', null, () => {
        this.templateItems.splice(index, 1);
        this.$baseMessage('删除成功', 'success');
      });
    },

    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    handleBatchEnable() {
      this.selectedItems.forEach(item => {
        item.status = true;
      });
      this.$baseMessage('批量启用成功', 'success');
    },

    handleBatchDisable() {
      this.selectedItems.forEach(item => {
        item.status = false;
      });
      this.$baseMessage('批量禁用成功', 'success');
    },

    handleBatchDelete() {
      this.$baseConfirm(`确定要删除选中的 ${this.selectedItems.length} 个项目吗？`, null, () => {
        const selectedIds = this.selectedItems.map(item => item.id || item.salaryItemId);
        this.templateItems = this.templateItems.filter(
          item => !selectedIds.includes(item.id || item.salaryItemId)
        );
        this.$baseMessage('批量删除成功', 'success');
      });
    },

    handleOrderChange() {
      // 重新排序
      this.templateItems.sort((a, b) => a.displayOrder - b.displayOrder);
    },

    handleEditValidation(item) {
      this.currentValidationItem = item;
      
      // 解析现有的验证规则
      try {
        const rules = item.validationRules ? JSON.parse(item.validationRules) : {};
        this.validationForm = {
          min: rules.min || '',
          max: rules.max || '',
          required: rules.required || false,
          pattern: rules.pattern || ''
        };
      } catch (error) {
        this.validationForm = {
          min: '',
          max: '',
          required: false,
          pattern: ''
        };
      }
      
      this.validationDialogVisible = true;
    },

    handleSaveValidation() {
      if (!this.currentValidationItem) return;
      
      // 构建验证规则JSON
      const rules = {};
      if (this.validationForm.min) rules.min = this.validationForm.min;
      if (this.validationForm.max) rules.max = this.validationForm.max;
      if (this.validationForm.required) rules.required = this.validationForm.required;
      if (this.validationForm.pattern) rules.pattern = this.validationForm.pattern;
      
      this.currentValidationItem.validationRules = Object.keys(rules).length > 0 ? JSON.stringify(rules) : '';
      this.validationDialogVisible = false;
      this.$baseMessage('验证规则保存成功', 'success');
    },

    async handleSave() {
      if (this.templateItems.length === 0) {
        this.$baseMessage('请至少添加一个薪资项目', 'warning');
        return;
      }

      this.saveLoading = true;
      try {
        // 使用批量保存API
        const { msg } = await batchSave(
          this.templateInfo.id,
          this.tenantId,
          this.templateInfo.dataMonth,
          this.templateItems
        );
        
        this.$baseMessage(msg || '保存成功', 'success');
        this.handleClose();
        this.$emit('fetchData'); // 通知父组件刷新数据
      } catch (error) {
        this.$baseMessage('保存失败', 'error');
      } finally {
        this.saveLoading = false;
      }
    },

    handleClose() {
      this.dialogVisible = false;
      this.templateInfo = {};
      this.templateItems = [];
      this.selectedItems = [];
    },

    formatMonth(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      return `${year}-${month}`;
    },

    getCategoryTagType(category) {
      const typeMap = {
        '基本工资': 'primary',
        '补贴': 'success',
        '提成': 'warning',
        '奖金': 'danger',
        '扣除': 'info'
      };
      return typeMap[category] || 'default';
    },
  },
};
</script>

<style lang="scss" scoped>
.template-config-container {
  .template-info-card {
    margin-bottom: 20px;
  }
  
  .template-items-card {
    .toolbar {
      margin-bottom: 15px;
      
      .el-button {
        margin-right: 10px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
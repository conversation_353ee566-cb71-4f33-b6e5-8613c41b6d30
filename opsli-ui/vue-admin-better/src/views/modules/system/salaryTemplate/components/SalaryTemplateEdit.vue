<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="templateForm"
      :model="templateForm"
      :rules="templateRules"
      label-width="120px"
    >
      <el-form-item label="模板名称" prop="name">
        <el-input
          v-model="templateForm.name"
          placeholder="请输入模板名称"
          maxlength="255"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="数据月份" prop="dataMonth">
        <el-date-picker
          v-model="templateForm.dataMonth"
          type="month"
          placeholder="选择数据月份"
          format="yyyy-MM"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>



      <el-form-item label="模板状态">
        <el-switch
          v-model="templateForm.status"
          active-color="#13ce66"
          inactive-color="#ff4949"
        ></el-switch>
      </el-form-item>

      <el-form-item label="模板描述">
        <el-input
          v-model="templateForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入模板描述"
          maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saveLoading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { doInsert, doUpdate, checkNameUnique } from '@/api/system/salary/salaryTemplate';

export default {
  name: 'SalaryTemplateEdit',
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '新建薪资模板',
      isEdit: false,
      saveLoading: false,
      originalName: '', // 用于验证名称唯一性
      templateForm: {
        id: null,
        tenantId: null,
        name: '',
        dataMonth: '',
        status: true,
        description: ''
      },
      templateRules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' },
          { validator: this.validateNameUnique, trigger: 'blur' }
        ],
        dataMonth: [
          { required: true, message: '请选择数据月份', trigger: 'change' }
        ],

      }
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
    }),
  },
  methods: {
    showEdit(template, defaultDataMonth = null) {
      this.isEdit = !!template;
      this.dialogTitle = this.isEdit ? '编辑薪资模板' : '新建薪资模板';
      
      if (this.isEdit) {
        this.templateForm = { ...template };
        this.originalName = template.name; // 保存原始名称用于验证
      } else {
        this.templateForm = {
          id: null,
          tenantId: this.tenantId,
          name: '',
          dataMonth: defaultDataMonth || '',
          status: true,
          description: ''
        };
        this.originalName = ''; // 新建时清空原始名称
      }
      
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.templateForm.clearValidate();
      });
    },

    async validateNameUnique(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      // 如果是编辑模式且名称没有改变，跳过验证
      if (this.isEdit && this.originalName === value) {
        callback();
        return;
      }

      try {
        const { data } = await checkNameUnique({
          id: this.templateForm.id,
          tenantId: this.tenantId,
          dataMonth: this.templateForm.dataMonth,
          name: value
        });
        
        if (!data) {
          callback(new Error('模板名称已存在'));
        } else {
          callback();
        }
      } catch (error) {
        console.error('验证模板名称唯一性失败:', error);
        // 验证失败时不阻止用户继续操作，但记录错误
        callback();
      }
    },

    handleSave() {
      this.$refs.templateForm.validate(async (valid) => {
        if (!valid) return;

        this.saveLoading = true;
        try {
          const formData = { ...this.templateForm };
          formData.tenantId = this.tenantId;

          let result;
          if (this.isEdit) {
            result = await doUpdate(formData);
          } else {
            result = await doInsert(formData);
          }

          this.$baseMessage(result.msg || `${this.isEdit ? '更新' : '创建'}成功`, 'success');
          this.handleClose();
          this.$emit('fetchData');
        } catch (error) {
          this.$baseMessage(`${this.isEdit ? '更新' : '创建'}失败`, 'error');
        } finally {
          this.saveLoading = false;
        }
      });
    },

    handleClose() {
      this.dialogVisible = false;
      this.templateForm = {
        id: null,
        tenantId: null,
        name: '',
        dataMonth: '',

        status: true,
        description: ''
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
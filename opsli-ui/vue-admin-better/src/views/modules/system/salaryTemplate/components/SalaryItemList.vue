<template>
  <div class="salary-item-container">
    <!-- 查询表单 -->
    <vab-query-form>
      <vab-query-form-left-panel :span="6">
        <el-button icon="el-icon-plus" type="primary" @click="handleInsertItem">
          新建薪资项目
        </el-button>
        <el-button icon="el-icon-setting" type="success" @click="handleInitSystemItems">
          初始化系统项目
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="18">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input v-model.trim="queryForm.name_LIKE" placeholder="请输入项目名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-select v-model="queryForm.category_EQ" placeholder="请选择分类" clearable style="width: 150px">
              <el-option label="基本工资" value="基本工资"></el-option>
              <el-option label="补贴" value="补贴"></el-option>
              <el-option label="提成" value="提成"></el-option>
              <el-option label="奖金" value="奖金"></el-option>
              <el-option label="扣除" value="扣除"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="queryForm.status_EQ" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="启用" :value="true"></el-option>
              <el-option label="禁用" :value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <!-- 薪资项目列表 -->
    <el-table ref="itemTable" v-loading="listLoading" :data="itemData" :element-loading-text="elementLoadingText"
      :highlight-current-row="true" @selection-change="setSelectItems">
      <el-table-column type="selection" width="55"></el-table-column>

      <el-table-column show-overflow-tooltip prop="name" label="项目名称" min-width="150"></el-table-column>

      <el-table-column show-overflow-tooltip prop="category" label="分类" min-width="100"
        align="center"></el-table-column>

      <el-table-column show-overflow-tooltip prop="dataType" label="数据类型" min-width="100" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getDataTypeTagType(scope.row.dataType)">
            {{ getDataTypeLabel(scope.row.dataType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip prop="unit" label="单位" min-width="80" align="center"></el-table-column>

      <el-table-column show-overflow-tooltip prop="status" label="状态" min-width="80" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip prop="isSystemItem" label="系统项目" min-width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isSystemItem ? 'warning' : 'info'" size="mini">
            {{ scope.row.isSystemItem ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip prop="calculationFormula" label="计算公式" min-width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.calculationFormula" class="formula-text">
            {{ scope.row.calculationFormula }}
          </span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdateItem(scope.row)">
            编辑
          </el-button>
          <el-button v-if="!scope.row.isSystemItem" type="text" @click="handleDeleteItem(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination background :current-page="queryForm.pageNo" :page-size="queryForm.pageSize" :layout="layout"
      :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>

    <!-- 批量操作 -->
    <div v-if="selectedItems.length > 0" class="batch-operations">
      <el-alert :title="`已选择 ${selectedItems.length} 项`" type="info" show-icon :closable="false">
        <template slot="default">
          <el-button type="danger" size="mini" @click="handleBatchDelete">
            批量删除
          </el-button>
        </template>
      </el-alert>
    </div>

    <!-- 薪资项目编辑对话框 -->
    <salary-item-edit ref="itemEdit" @fetchData="fetchItemData"></salary-item-edit>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  getList as getItemList,
  doDelete as deleteItem,
  doDeleteAll as deleteAllItems,
  batchUpdateStatus,
  initSystemItems
} from '@/api/system/salary/salaryItem';
import SalaryItemEdit from '../../salaryEditor/components/SalaryItemEdit';

export default {
  name: 'SalaryItemList',
  components: {
    SalaryItemEdit
  },
  data() {
    return {
      itemList: null,
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      selectedItems: [],
      elementLoadingText: "正在加载...",
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        name_LIKE: "",
        category_EQ: "",
        status_EQ: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
    }),
    itemData() {
      return this.itemList || [];
    },
    readyToFetch() {
      return this.tenantId;
    },
  },
  watch: {
    readyToFetch: {
      handler(newVal) {
        if (newVal) {
          this.fetchItemData();
        }
      },
      immediate: true,
    },
  },

  methods: {
    setSelectItems(val) {
      this.selectedItems = val;
    },
    handleInsertItem() {
      this.$nextTick(() => {
        if (this.$refs["itemEdit"]) {
          this.$refs["itemEdit"].showEdit(null);
        } else {
          this.$baseMessage("编辑组件未加载，请稍后重试", "error");
        }
      });
    },

    handleUpdateItem(row) {
      if (row.id) {
        this.$nextTick(() => {
          if (this.$refs["itemEdit"]) {
            this.$refs["itemEdit"].showEdit(row);
          } else {
            this.$baseMessage("编辑组件未加载，请稍后重试", "error");
          }
        });
      }
    },
    handleDeleteItem(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前薪资项目吗", null, async () => {
          const { msg } = await deleteItem({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchItemData();
        });
      }
    },
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$baseMessage("请选择要删除的项目", "warning");
        return;
      }

      // 检查是否包含系统项目
      const systemItems = this.selectedItems.filter(item => item.isSystemItem);
      if (systemItems.length > 0) {
        this.$baseMessage("系统项目不能删除", "warning");
        return;
      }

      this.$baseConfirm(`你确定要删除选中的 ${this.selectedItems.length} 个薪资项目吗`, null, async () => {
        const ids = this.selectedItems.map(item => item.id);
        const { msg } = await deleteAllItems({ ids: ids.join(',') });
        this.$baseMessage(msg, "success");
        await this.fetchItemData();
      });
    },
    async handleStatusChange(row) {
      try {
        const { msg } = await batchUpdateStatus(row.id, row.status);
        this.$baseMessage(msg, "success");
      } catch (error) {
        // 恢复原状态
        row.status = !row.status;
        this.$baseMessage("状态更新失败", "error");
      }
    },
    async handleInitSystemItems() {
      this.$baseConfirm("确定要初始化系统薪资项目吗？这将创建一些基础的薪资项目", null, async () => {
        try {
          const { msg } = await initSystemItems(this.tenantId);
          this.$baseMessage(msg, "success");
          await this.fetchItemData();
        } catch (error) {
          this.$baseMessage("初始化系统项目失败", "error");
        }
      });
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchItemData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchItemData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchItemData();
    },
    async fetchItemData() {
      if (!this.readyToFetch) {
        return;
      }

      this.listLoading = true;
      try {
        const queryParams = {
          ...this.queryForm,
          tenantId: this.tenantId,
        };
        const { data } = await getItemList(queryParams);
        this.itemList = data.rows;
        this.total = parseInt(data.total);
      } catch (error) {
        this.$baseMessage("获取数据失败", "error");
      } finally {
        this.listLoading = false;
      }
    },
    getDataTypeLabel(dataType) {
      const typeMap = {
        'decimal': '小数',
        'integer': '整数',
        'percentage': '百分比',
        'text': '文本',
        'boolean': '布尔值'
      };
      return typeMap[dataType] || dataType;
    },
    getDataTypeTagType(dataType) {
      const typeMap = {
        'decimal': 'success',
        'integer': 'primary',
        'percentage': 'warning',
        'text': 'info',
        'boolean': 'danger'
      };
      return typeMap[dataType] || 'info';
    },
  },
};
</script>

<style lang="scss" scoped>
.salary-item-container {
  .formula-text {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #606266;
  }

  .text-muted {
    color: #999;
  }

  .batch-operations {
    margin-top: 15px;
  }
}
</style>
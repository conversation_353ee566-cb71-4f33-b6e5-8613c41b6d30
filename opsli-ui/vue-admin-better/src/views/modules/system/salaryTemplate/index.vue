<template>
  <div class="salary-template-container">
    <!-- Tab 切换 -->
    <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="薪资模板管理" name="template">
        <el-row :gutter="15">
          <el-col>
            <!-- 查询表单 -->
            <vab-query-form>
              <vab-query-form-left-panel :span="6">
                <el-button v-if="$perms('system_salary_template_insert')" icon="el-icon-plus" type="primary"
                  @click="handleInsertTemplate"> 新建薪资模板 </el-button>
              </vab-query-form-left-panel>
              <vab-query-form-right-panel :span="18">
                <el-form :inline="true" :model="queryForm" @submit.native.prevent>
                  <el-form-item>
                    <el-input v-model.trim="queryForm.name_LIKE" placeholder="请输入模板名称" clearable />
                  </el-form-item>

                  <el-form-item>
                    <el-button icon="el-icon-search" type="primary" @click="queryData">
                      查询
                    </el-button>
                  </el-form-item>
                </el-form>
              </vab-query-form-right-panel>
            </vab-query-form>

            <!-- 薪资模板列表 -->
            <el-table ref="templateTable" v-loading="listLoading" :data="templateData"
              :element-loading-text="elementLoadingText" :highlight-current-row="true"
              @current-change="setSelectTemplate">
              <el-table-column show-overflow-tooltip prop="name" label="模板名称" min-width="150"></el-table-column>

              <el-table-column show-overflow-tooltip prop="dataMonth" label="数据月份" min-width="120" align="center">
                <template slot-scope="scope">
                  <span>{{ formatMonth(scope.row.dataMonth) }}</span>
                </template>
              </el-table-column>

              <el-table-column show-overflow-tooltip prop="status" label="状态" min-width="80" align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status ? 'success' : 'danger'">
                    {{ scope.row.status ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column show-overflow-tooltip prop="description" label="描述" min-width="200"></el-table-column>

              <el-table-column label="操作" min-width="280" align="center">
                <template slot-scope="scope">
                  <el-button v-if="$perms('system_salary_template_select')" type="text"
                    @click="handleConfigTemplate(scope.row)">
                    配置项目
                  </el-button>
                  <el-button v-if="$perms('system_salary_template_update')" type="text"
                    @click="handleUpdateTemplate(scope.row)">
                    编辑
                  </el-button>

                  <el-button v-if="$perms('system_salary_template_delete')" type="text"
                    @click="handleDeleteTemplate(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination background :current-page="queryForm.pageNo" :page-size="queryForm.pageSize" :layout="layout"
              :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="薪资项目管理" name="item">
        <salary-item-list v-if="activeTab === 'item'" ref="itemList"></salary-item-list>
      </el-tab-pane>
    </el-tabs>

    <!-- 薪资模板编辑对话框 -->
    <salary-template-edit ref="templateEdit" @fetchData="fetchTemplateData"></salary-template-edit>

    <!-- 薪资项目配置对话框 -->
    <salary-template-config ref="templateConfig" @fetchData="fetchTemplateData"></salary-template-config>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getList as getTemplateList, doDelete as deleteTemplate } from '@/api/system/salary/salaryTemplate';
import SalaryTemplateEdit from './components/SalaryTemplateEdit'
import SalaryTemplateConfig from './components/SalaryTemplateConfig'
import SalaryItemList from './components/SalaryItemList'

export default {
  name: 'SalaryTemplate',
  components: {
    SalaryTemplateEdit,
    SalaryTemplateConfig,
    SalaryItemList
  },
  data() {
    return {
      activeTab: 'template',
      templateList: null,
      listLoading: true,
      layout: "total, sizes, prev, pager, next, jumper",
      total: 0,
      currentTemplate: null,
      elementLoadingText: "正在加载...",
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        name_LIKE: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
    templateData() {
      return this.templateList || [];
    },
    readyToFetch() {
      return this.tenantId && this.selectedMonth;
    },
  },
  watch: {
    readyToFetch: {
      handler(newVal) {
        if (newVal) {
          this.fetchTemplateData();
        }
      },
      immediate: true,
    },
  },
  created() {
    // 通过watch监听来获取数据
  },
  methods: {
    setSelectTemplate(val) {
      this.currentTemplate = val;
    },
    handleInsertTemplate() {
      this.$refs["templateEdit"].showEdit(null, this.selectedMonth);
    },
    handleUpdateTemplate(row) {
      if (row.id) {
        this.$refs["templateEdit"].showEdit(row);
      }
    },
    handleConfigTemplate(row) {
      if (row.id) {
        this.$refs["templateConfig"].showConfig(row);
      }
    },
    handleDeleteTemplate(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前薪资模板吗", null, async () => {
          const { msg } = await deleteTemplate({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchTemplateData();
        });
      }
    },


    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchTemplateData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchTemplateData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchTemplateData();
    },
    async fetchTemplateData() {
      if (!this.readyToFetch) {
        return;
      }

      this.listLoading = true;
      try {
        // 添加租户ID和数据月份到查询参数
        const queryParams = {
          ...this.queryForm,
          tenantId: this.tenantId,
          dataMonth: this.selectedMonth,
        };
        const { data } = await getTemplateList(queryParams);
        this.templateList = data.rows;
        this.total = parseInt(data.total);
      } catch (error) {
        this.$baseMessage("获取数据失败", "error");
      } finally {
        this.listLoading = false;
      }
    },

    formatMonth(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      return `${year}-${month}`;
    },

    handleTabClick(tab) {
      // Tab切换时可以执行一些逻辑，比如刷新数据
      if (tab.name === 'item' && this.$refs.itemList) {
        // 切换到薪资项目管理时，可以刷新数据
        this.$nextTick(() => {
          this.$refs.itemList.fetchItemData();
        });
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.salary-template-container {
  padding: 20px;
  background-color: #f0f2f5;

  ::v-deep .el-tabs__header {
    margin-bottom: 20px;
  }

  ::v-deep .el-tabs__content {
    padding: 0;
  }

  ::v-deep .el-tab-pane {
    background: transparent;
  }
}
</style>
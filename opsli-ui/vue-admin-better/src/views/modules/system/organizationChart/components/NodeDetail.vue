<template>
  <div class="node-detail">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="名称">
            {{ nodeData.nodeName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="编码">
            {{ nodeData.nodeCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="getNodeTypeColor(nodeData.nodeType)">
              {{ getNodeTypeLabel(nodeData.nodeType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="nodeData.status === 1 ? 'success' : 'danger'">
              {{ nodeData.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          
          <!-- 部门特有信息 -->
          <template v-if="nodeData.nodeType === 'department'">
            <el-descriptions-item label="部门描述" :span="2">
              {{ nodeData.departmentDescription || '-' }}
            </el-descriptions-item>
          </template>
          
          <!-- 岗位特有信息 -->
          <template v-if="nodeData.nodeType === 'position'">
            <el-descriptions-item label="岗位级别">
              {{ nodeData.positionLevel || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="编制人数">
              {{ nodeData.headcount || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="岗位描述" :span="2">
              {{ nodeData.positionDescription || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="岗位要求" :span="2">
              {{ nodeData.positionRequirements || '-' }}
            </el-descriptions-item>
          </template>
          
          <!-- 员工特有信息 -->
          <template v-if="nodeData.nodeType === 'employee'">
            <el-descriptions-item label="员工编号">
              {{ nodeData.employeeNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="性别">
              {{ nodeData.gender || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号码">
              {{ nodeData.phoneNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="入职日期">
              {{ nodeData.hireDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="员工状态" :span="2">
              {{ nodeData.employeeStatus || '-' }}
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </el-tab-pane>
      
      <!-- 统计信息 -->
      <el-tab-pane label="统计信息" name="stats" v-if="nodeData.nodeType !== 'employee'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="stats-card">
              <div class="stats-item">
                <div class="stats-icon">
                  <i class="el-icon-user"></i>
                </div>
                <div class="stats-content">
                  <div class="stats-value">{{ getDirectCount() }}</div>
                  <div class="stats-label">直接下属</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="stats-card">
              <div class="stats-item">
                <div class="stats-icon">
                  <i class="el-icon-s-data"></i>
                </div>
                <div class="stats-content">
                  <div class="stats-value">{{ getTotalCount() }}</div>
                  <div class="stats-label">总下属数</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-divider></el-divider>
        
        <!-- 下属列表 -->
        <div v-if="nodeData.children && nodeData.children.length > 0">
          <h4>下属列表</h4>
          <el-table :data="nodeData.children" style="width: 100%">
            <el-table-column prop="nodeName" label="名称" width="150"></el-table-column>
            <el-table-column prop="nodeType" label="类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="getNodeTypeColor(scope.row.nodeType)" size="small">
                  {{ getNodeTypeLabel(scope.row.nodeType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="nodeCode" label="编码" width="120"></el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                  {{ scope.row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无下属数据"></el-empty>
        </div>
      </el-tab-pane>
      
      <!-- 层级路径 -->
      <el-tab-pane label="层级路径" name="path">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item v-for="(item, index) in getNodePath()" :key="index">
            <span class="path-item">
              <i :class="getNodeIcon(item.nodeType)"></i>
              {{ item.nodeName }}
            </span>
          </el-breadcrumb-item>
        </el-breadcrumb>
        
        <el-divider></el-divider>
        
        <div class="path-info">
          <p><strong>当前层级：</strong>{{ nodeData.level || 0 }}</p>
          <p><strong>节点类型：</strong>{{ getNodeTypeLabel(nodeData.nodeType) }}</p>
          <p><strong>父节点类型：</strong>{{ getNodeTypeLabel(nodeData.parentType) || '-' }}</p>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <div class="dialog-footer" style="text-align: right; margin-top: 20px;">
      <el-button @click="$emit('close')">关闭</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "NodeDetail",
  props: {
    nodeData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      activeTab: "basic",
    };
  },
  methods: {
    // 获取节点类型标签
    getNodeTypeLabel(nodeType) {
      const labelMap = {
        department: "部门",
        position: "岗位",
        employee: "员工",
      };
      return labelMap[nodeType] || "未知";
    },

    // 获取节点类型颜色
    getNodeTypeColor(nodeType) {
      const colorMap = {
        department: "primary",
        position: "success",
        employee: "warning",
      };
      return colorMap[nodeType] || "info";
    },

    // 获取节点图标
    getNodeIcon(nodeType) {
      const iconMap = {
        department: "el-icon-office-building",
        position: "el-icon-suitcase",
        employee: "el-icon-user",
      };
      return iconMap[nodeType] || "el-icon-folder";
    },

    // 获取直接下属数量
    getDirectCount() {
      return this.nodeData.children ? this.nodeData.children.length : 0;
    },

    // 获取总下属数量
    getTotalCount() {
      if (!this.nodeData.children) return 0;
      
      let total = this.nodeData.children.length;
      const countChildren = (children) => {
        children.forEach(child => {
          if (child.children) {
            total += child.children.length;
            countChildren(child.children);
          }
        });
      };
      
      countChildren(this.nodeData.children);
      return total;
    },

    // 获取节点路径（这里简化处理，实际应该从根节点开始构建）
    getNodePath() {
      // 这里简化处理，只显示当前节点
      // 实际应该根据parentId递归构建完整路径
      return [
        {
          nodeName: this.nodeData.nodeName,
          nodeType: this.nodeData.nodeType,
        },
      ];
    },
  },
};
</script>

<style lang="scss" scoped>
.node-detail {
  .stats-card {
    margin-bottom: 10px;
    ::v-deep .el-card__body {
      padding: 15px;
    }
    .stats-item {
      display: flex;
      align-items: center;
      .stats-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        i {
          font-size: 18px;
          color: white;
        }
      }
      .stats-content {
        .stats-value {
          font-size: 20px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }
        .stats-label {
          font-size: 12px;
          color: #909399;
          margin-top: 3px;
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 40px 0;
  }

  .path-item {
    display: inline-flex;
    align-items: center;
    i {
      margin-right: 5px;
      color: #409eff;
    }
  }

  .path-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    p {
      margin: 8px 0;
      color: #606266;
    }
  }
}
</style>

<template>
  <div class="test-container">
    <h2>组织架构图测试页面</h2>
    <p>这个页面用于测试组织架构图的功能</p>
    
    <el-card>
      <div slot="header">
        <span>测试功能</span>
      </div>
      
      <el-button @click="loadTestData" type="primary">加载测试数据</el-button>
      <el-button @click="testExpandCollapse" type="success">测试展开/折叠</el-button>
      <el-button @click="testHeadcount" type="warning">测试人员统计</el-button>
      
      <div style="margin-top: 20px;">
        <h3>测试结果：</h3>
        <ul>
          <li v-for="result in testResults" :key="result.id">
            <strong>{{ result.test }}:</strong> 
            <span :style="{ color: result.success ? 'green' : 'red' }">
              {{ result.success ? '通过' : '失败' }}
            </span>
            <span v-if="result.message"> - {{ result.message }}</span>
          </li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script>
import { testOrganizationData, testStats } from './test-data.js';

export default {
  name: 'OrganizationChartTest',
  data() {
    return {
      testResults: []
    };
  },
  methods: {
    loadTestData() {
      this.addTestResult('加载测试数据', true, '测试数据已加载');
      console.log('测试数据:', testOrganizationData);
      console.log('统计数据:', testStats);
    },
    
    testExpandCollapse() {
      // 测试展开/折叠功能
      const hasExpandMethod = typeof this.expandAll === 'function';
      const hasCollapseMethod = typeof this.collapseAll === 'function';
      
      this.addTestResult(
        '展开/折叠功能', 
        hasExpandMethod && hasCollapseMethod,
        hasExpandMethod && hasCollapseMethod ? '方法存在' : '方法缺失'
      );
    },
    
    testHeadcount() {
      // 测试人员统计功能
      const testNode = testOrganizationData[0];
      const hasHeadcount = testNode.headcount !== undefined;
      const hasChildren = testNode.children && testNode.children.length > 0;
      
      this.addTestResult(
        '人员统计功能',
        hasHeadcount && hasChildren,
        `编制人数: ${testNode.headcount}, 当前人数: ${testNode.children ? testNode.children.length : 0}`
      );
    },
    
    addTestResult(test, success, message = '') {
      this.testResults.push({
        id: Date.now(),
        test,
        success,
        message
      });
    }
  }
};
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.test-container h2 {
  color: #303133;
  margin-bottom: 10px;
}

.test-container p {
  color: #606266;
  margin-bottom: 20px;
}

.test-container ul {
  list-style-type: none;
  padding: 0;
}

.test-container li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}
</style>

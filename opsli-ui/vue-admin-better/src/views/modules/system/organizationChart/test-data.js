// 测试数据，用于验证组织架构图功能
export const testOrganizationData = [
  {
    id: '1',
    nodeId: '1',
    nodeName: '创始家',
    nodeCode: 'CEO001',
    nodeType: 'department',
    headcount: 5,
    status: 1,
    departmentDescription: '公司最高管理层',
    children: [
      {
        id: '2',
        nodeId: '2',
        nodeName: '人事部',
        nodeCode: 'HR001',
        nodeType: 'department',
        headcount: 3,
        status: 1,
        departmentDescription: '负责人力资源管理',
        children: [
          {
            id: '21',
            nodeId: '21',
            nodeName: '人事总监',
            nodeCode: 'HR_DIR001',
            nodeType: 'position',
            headcount: 1,
            status: 1,
            positionLevel: '高级',
            positionDescription: '负责人事部门整体管理',
            children: [
              {
                id: '211',
                nodeId: '211',
                nodeName: '张三',
                nodeCode: 'EMP001',
                nodeType: 'employee',
                status: 1,
                employeeNumber: 'E001',
                gender: '男',
                phoneNumber: '13800138001',
                hireDate: '2020-01-01',
                employeeStatus: '在职'
              }
            ]
          }
        ]
      },
      {
        id: '3',
        nodeId: '3',
        nodeName: '运营部',
        nodeCode: 'OPS001',
        nodeType: 'department',
        headcount: 4,
        status: 1,
        departmentDescription: '负责公司日常运营',
        children: [
          {
            id: '31',
            nodeId: '31',
            nodeName: '运营经理',
            nodeCode: 'OPS_MGR001',
            nodeType: 'position',
            headcount: 2,
            status: 1,
            positionLevel: '中级',
            positionDescription: '负责运营部门管理',
            children: [
              {
                id: '311',
                nodeId: '311',
                nodeName: '李四',
                nodeCode: 'EMP002',
                nodeType: 'employee',
                status: 1,
                employeeNumber: 'E002',
                gender: '女',
                phoneNumber: '13800138002',
                hireDate: '2020-03-01',
                employeeStatus: '在职'
              },
              {
                id: '312',
                nodeId: '312',
                nodeName: '王五',
                nodeCode: 'EMP003',
                nodeType: 'employee',
                status: 1,
                employeeNumber: 'E003',
                gender: '男',
                phoneNumber: '13800138003',
                hireDate: '2020-05-01',
                employeeStatus: '在职'
              }
            ]
          }
        ]
      },
      {
        id: '4',
        nodeId: '4',
        nodeName: '产品部',
        nodeCode: 'PROD001',
        nodeType: 'department',
        headcount: 6,
        status: 1,
        departmentDescription: '负责产品设计和开发',
        children: [
          {
            id: '41',
            nodeId: '41',
            nodeName: '产品经理',
            nodeCode: 'PROD_MGR001',
            nodeType: 'position',
            headcount: 3,
            status: 1,
            positionLevel: '中级',
            positionDescription: '负责产品规划和管理'
          },
          {
            id: '42',
            nodeId: '42',
            nodeName: 'UI设计师',
            nodeCode: 'UI_DES001',
            nodeType: 'position',
            headcount: 2,
            status: 1,
            positionLevel: '初级',
            positionDescription: '负责用户界面设计'
          }
        ]
      },
      {
        id: '5',
        nodeId: '5',
        nodeName: '新业务部',
        nodeCode: 'NEW001',
        nodeType: 'department',
        headcount: 8,
        status: 1,
        departmentDescription: '负责新业务拓展',
        children: []
      }
    ]
  }
];

export const testStats = {
  departmentCount: 4,
  positionCount: 4,
  employeeCount: 3,
  totalCount: 11
};

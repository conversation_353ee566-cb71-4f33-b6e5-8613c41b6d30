<template>
  <div class="organization-chart-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="title">组织架构图</h1>
      <p class="subtitle">可视化展示组织层级结构，包含部门、岗位、员工信息</p>
    </div>


    <!-- 统计信息卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon department">
              <i class="el-icon-office-building"></i>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.departmentCount || 0 }}</div>
              <div class="stats-label">部门总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon position">
              <i class="el-icon-suitcase"></i>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.positionCount || 0 }}</div>
              <div class="stats-label">岗位总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon employee">
              <i class="el-icon-user"></i>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.employeeCount || 0 }}</div>
              <div class="stats-label">员工总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-icon total">
              <i class="el-icon-s-data"></i>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.totalCount || 0 }}</div>
              <div class="stats-label">总节点数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主内容区域 -->
    <el-card class="main-content" shadow="never" v-loading="loading">
      <!-- 树形视图 -->
      <div v-if="viewMode === 'tree'" class="tree-view">
        <el-tree
          ref="organizationTree"
          :data="treeData"
          :props="treeProps"
          :default-expand-all="false"
          :expand-on-click-node="false"
          node-key="nodeId"
          class="organization-tree"
        >
          <span class="tree-node" slot-scope="{ node, data }">
            <span class="node-icon">
              <i :class="getNodeIcon(data.nodeType)"></i>
            </span>
            <span class="node-label">{{ node.label }}</span>
            <span class="node-type">{{ getNodeTypeLabel(data.nodeType) }}</span>
            <span class="node-actions">
              <el-button
                type="text"
                size="mini"
                @click="showNodeDetail(data)"
              >
                详情
              </el-button>
            </span>
          </span>
        </el-tree>
      </div>

      <!-- 图形视图 -->
      <div v-if="viewMode === 'chart'" class="chart-view">
        <div class="orgchart-toolbar">
          
          <el-button-group style="margin-left: 10px;">
            <el-button size="small" @click="zoomIn">
              <i class="el-icon-zoom-in"></i> 放大
            </el-button>
            <el-button size="small" @click="zoomOut">
              <i class="el-icon-zoom-out"></i> 缩小
            </el-button>
            <el-button size="small" @click="resetZoom">
              <i class="el-icon-refresh"></i> 重置
            </el-button>
          </el-button-group>
          
          <el-button-group style="margin-left: 10px;">
            <el-button size="small" @click="expandAllNodes">
              <i class="el-icon-plus"></i> 全部展开
            </el-button>
            <el-button size="small" @click="collapseAllNodes">
              <i class="el-icon-minus"></i> 全部折叠
            </el-button>
          </el-button-group>

          <el-button-group style="margin-right: 10px;">
              <el-button icon="el-icon-refresh" @click="handleRefresh" size="small">刷新</el-button>
              <el-button icon="el-icon-full-screen" @click="toggleFullscreen" size="small">全屏</el-button>
              <el-button icon="el-icon-download" @click="handleExport" size="small">导出</el-button>
          </el-button-group>
        </div>

        <div class="orgchart-container" :key="chartKey">
          <div ref="orgChart" class="orgchart-wrapper"></div>
          <div v-if="!treeData || treeData.length === 0" class="empty-chart">
            <el-empty description="暂无组织架构数据"></el-empty>
          </div>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0" class="search-results">
        <h3>搜索结果 ({{ searchResults.length }})</h3>
        <el-table :data="searchResults" style="width: 100%">
          <el-table-column prop="nodeName" label="名称" width="200"></el-table-column>
          <el-table-column prop="nodeType" label="类型" width="100">
            <template slot-scope="scope">
              <el-tag :type="getNodeTypeColor(scope.row.nodeType)">
                {{ getNodeTypeLabel(scope.row.nodeType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nodeCode" label="编码" width="150"></el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="showNodeDetail(scope.row)"
              >
                查看详情
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="highlightSearchResult(scope.row)"
                v-if="viewMode === 'tree'"
              >
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 节点详情弹窗 -->
    <el-dialog
      :title="nodeDetailTitle"
      :visible.sync="nodeDetailVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <node-detail
        v-if="nodeDetailVisible"
        :node-data="selectedNode"
        @close="nodeDetailVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getOrganizationTree,
  getDepartmentTree,
  getOrganizationStats,
  searchNodes,
} from "@/api/system/organizationChart/organizationChart";
import NodeDetail from "./components/NodeDetail";
import $ from 'jquery';

export default {
  name: "OrganizationChart",
  components: {
    NodeDetail,
  },
  data() {
    return {
      loading: false,
      chartKey: 0,
      viewMode: "chart", // tree | chart
      queryForm: {
        keyword: "",
        nodeType: "",
      },
      treeData: [],
      searchResults: [],
      stats: {
        departmentCount: 0,
        positionCount: 0,
        employeeCount: 0,
        totalCount: 0,
      },
      treeProps: {
        children: "children",
        label: "nodeName",
      },
      nodeDetailVisible: false,
      selectedNode: null,
      orgChart: null, // OrgChart.js 实例
      chartDirection: 'top', // 图表方向
      zoomLevel: 1, // 缩放级别
    };
  },
  computed: {
    ...mapGetters({
      tenantId: "user/tenantId",
      currentMonth: "user/currentMonth",
    }),
    nodeDetailTitle() {
      return this.selectedNode ? `${this.selectedNode.nodeName} - 详细信息` : "节点详情";
    },
    
    // 组织图表数据按层级排列
    organizedChartData() {
      if (!this.treeData || this.treeData.length === 0) return [];
      
      const levels = [];
      
      const organizeByLevel = (nodes, level = 0) => {
        if (!levels[level]) {
          levels[level] = [];
        }
        
        nodes.forEach(node => {
          levels[level].push(node);
          if (node.children && node.children.length > 0) {
            organizeByLevel(node.children, level + 1);
          }
        });
      };
      
      organizeByLevel(this.treeData);
      return levels;
    },
  },
  async mounted() {
    // 动态加载orgchart库
    await this.loadOrgChartLibrary();
    
    this.fetchData();
    this.fetchStats();
    
    // 监听月份变化
    this.$baseEventBus.$on("month-changed", this.handleMonthChange);
  },
  beforeDestroy() {
    // 清理事件监听
    this.$baseEventBus.$off("month-changed", this.handleMonthChange);
    
    // 清理OrgChart实例
    if (this.chartInstance) {
      this.chartInstance = null;
    }
  },
  methods: {
    // 获取组织架构数据
    async fetchData() {
      this.loading = true;
      try {
        const params = {
          tenantId: this.tenantId,
          dataMonth: this.currentMonth,
        };

        const { data } = await getOrganizationTree(params);
        this.treeData = data || [];

        // 如果是图形视图，初始化图表
        if (this.viewMode === "chart") {
          this.$nextTick(() => {
            const chartData = this.transformDataForChart(this.treeData);
            this.initOrgChart(chartData);
          });
        }
      } catch (error) {
        this.$baseMessage("获取组织架构数据失败", "error");
        console.error("获取组织架构数据失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 获取统计信息
    async fetchStats() {
      try {
        const params = {
          tenantId: this.tenantId,
          dataMonth: this.currentMonth,
        };

        const { data } = await getOrganizationStats(params);
        if (data) {
          this.stats = {
            departmentCount: data.directSubordinates || 0,
            positionCount: data.positionEmployeeCount || 0,
            employeeCount: data.departmentEmployeeCount || 0,
            totalCount: data.totalSubordinates || 0,
          };
        }
      } catch (error) {
        console.error("获取统计信息失败:", error);
      }
    },

    // 搜索处理
    async handleSearch() {
      if (!this.queryForm.keyword.trim()) {
        this.searchResults = [];
        return;
      }

      this.loading = true;
      try {
        const params = {
          tenantId: this.tenantId,
          dataMonth: this.currentMonth,
          keyword: this.queryForm.keyword,
          nodeType: this.queryForm.nodeType,
        };

        const { data } = await searchNodes(params);
        this.searchResults = data || [];
      } catch (error) {
        this.$baseMessage("搜索失败", "error");
        console.error("搜索失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 显示节点详情
    showNodeDetail(nodeData) {
      this.selectedNode = nodeData;
      this.nodeDetailVisible = true;
    },

    // 获取节点图标
    getNodeIcon(nodeType) {
      const iconMap = {
        department: "el-icon-office-building",
        position: "el-icon-suitcase",
        employee: "el-icon-user",
      };
      return iconMap[nodeType] || "el-icon-folder";
    },

    // 获取节点类型标签
    getNodeTypeLabel(nodeType) {
      const labelMap = {
        department: "部门",
        position: "岗位",
        employee: "员工",
      };
      return labelMap[nodeType] || "未知";
    },

    // 刷新数据
    handleRefresh() {
      this.chartKey += 1;
      this.fetchData();
      this.fetchStats();
      this.searchResults = [];
    },

    // 切换视图模式
    setViewMode(mode) {
      this.viewMode = mode;
      if (mode === "chart") {
        this.$nextTick(() => {
          const chartData = this.transformDataForChart(this.treeData);
          this.initOrgChart(chartData);
        });
      }
    },

    // 导出功能
    handleExport() {
      // 导出树形数据为JSON
      const dataStr = JSON.stringify(this.treeData, null, 2);
      const blob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.download = `组织架构数据_${this.currentMonth}.json`;
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.$baseMessage("数据导出成功", "success");
    },

    // 缩放功能
    zoomIn() {
      if (this.chartInstance) {
        const $chart = $(this.$refs.orgChart).find('.orgchart');
        const currentScale = parseFloat($chart.css('transform').split('scale(')[1]) || 1;
        const newScale = Math.min(currentScale * 1.2, 3);
        $chart.css('transform', `scale(${newScale})`);
      }
    },

    zoomOut() {
      if (this.chartInstance) {
        const $chart = $(this.$refs.orgChart).find('.orgchart');
        const currentScale = parseFloat($chart.css('transform').split('scale(')[1]) || 1;
        const newScale = Math.max(currentScale * 0.8, 0.3);
        $chart.css('transform', `scale(${newScale})`);
      }
    },

    resetZoom() {
      if (this.chartInstance) {
        const $chart = $(this.$refs.orgChart).find('.orgchart');
        $chart.css('transform', 'scale(1)');
      }
    },

    // 展开/折叠节点
    expandAllNodes() {
      if (this.chartInstance) {
        const $chart = $(this.$refs.orgChart);
        $chart.find('.node').removeClass('collapsed');
      }
    },

    collapseAllNodes() {
      if (this.chartInstance) {
        const $chart = $(this.$refs.orgChart);
        $chart.find('.node').not(':first').addClass('collapsed');
      }
    },

    // 全屏切换
    toggleFullscreen() {
      const element = this.$el;
      if (!document.fullscreenElement) {
        element.requestFullscreen().catch((err) => {
          this.$baseMessage(`无法进入全屏模式: ${err.message}`, "error");
        });
      } else {
        document.exitFullscreen();
      }
    },

    // 动态加载OrgChart库
    async loadOrgChartLibrary() {
      // 确保jQuery已经加载
      if (!window.jQuery) {
        window.jQuery = $;
        window.$ = $;
      }
      
      // 加载CSS
      if (!document.querySelector('link[href*="orgchart"]')) {
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://cdn.jsdelivr.net/npm/orgchart@5.0.0/dist/css/jquery.orgchart.min.css';
        document.head.appendChild(cssLink);
      }
      
      // 加载JS
      if (!window.jQuery.fn.orgchart) {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = 'https://cdn.jsdelivr.net/npm/orgchart@5.0.0/dist/js/jquery.orgchart.min.js';
          script.onload = () => {
            if (window.jQuery && window.jQuery.fn.orgchart) {
              resolve();
            } else {
              reject(new Error('OrgChart.js 加载失败'));
            }
          };
          script.onerror = () => reject(new Error('OrgChart.js 加载失败'));
          document.head.appendChild(script);
        });
      }
    },

    transformDataForChart(nodes) {
      if (!nodes || nodes.length === 0) {
        return null;
      }

      const transformNode = (node) => {
        console.log(node);
        let className = '';
        let subtitle = '';

        switch (node.nodeType) {
          case 'company':
          case 'department':
            className = 'department';
            subtitle = `${node.employeeCount || 0}/${node.headcount || 0}`;
            break;
          case 'position':
            className = 'position';
            subtitle = `${node.employeeCount || 0}/${node.headcount || 1}`;
            break;
          case 'employee':
            className = 'employee';
            subtitle = `入职: ${node.hireDate || 'N/A'}`;
            break;
        }

        const newNode = {
          id: node.nodeId,
          name: node.nodeName,
          // title: node.nodeName, // orgchart uses 'name' and 'title'
          className: className,
          subtitle: subtitle,
        };

        if (node.children && node.children.length > 0) {
          newNode.children = node.children.map(transformNode);
        }

        return newNode;
      };
      
      // Assuming the API returns a tree structure with a single root
      return transformNode(nodes[0]);
    },

    createOrgChartNode($node, data) {
      // 自定义节点样式和内容
      $node.addClass(data.className);

      // 使用 title 作为主要显示内容
      $node.find('.title').text(data.name);

      // 添加 subtitle 作为 headcount
      if (data.subtitle) {
        let headcount = $('<div class="headcount"></div>').text(data.subtitle);
        $node.append(headcount);
      }
    },

    initOrgChart(data) {
      if (!data) {
        return;
      }

      const $chartContainer = $(this.$refs.orgChart);
      // 清空旧图表
      $chartContainer.empty();

      this.chartInstance = $chartContainer.orgchart({
        data: data,
        nodeContent: 'name',
        nodeId: 'id',
        pan: true,
        zoom: true,
        createNode: ($node, data) => {
          this.createOrgChartNode($node, data);
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.organization-chart-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 84px);

  .page-header {
    margin-bottom: 20px;
    .title {
      font-size: 24px;
      font-weight: bold;
      margin: 0 0 8px;
      color: #303133;
    }
    .subtitle {
      font-size: 14px;
      color: #909399;
      margin: 0;
    }
  }

  .stats-row {
    margin-bottom: 20px;
    .stats-card {
      ::v-deep .el-card__body {
        padding: 20px;
      }
      .stats-item {
        display: flex;
        align-items: center;
        .stats-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          i {
            font-size: 24px;
            color: white;
          }
          &.department {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          &.position {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          &.employee {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          &.total {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        .stats-content {
          .stats-value {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
          }
          .stats-label {
            font-size: 14px;
            color: #909399;
            margin-top: 5px;
          }
        }
      }
    }
  }

  .main-content {
    min-height: 600px;
    ::v-deep .el-card__body {
      padding: 20px;
    }

    .tree-view {
      .organization-tree {
        ::v-deep .el-tree-node__content {
          height: 40px;
          line-height: 40px;
          &:hover {
            background-color: #f5f7fa;
          }
        }
        .tree-node {
          display: flex;
          align-items: center;
          width: 100%;
          .node-icon {
            margin-right: 8px;
            i {
              font-size: 16px;
              color: #409eff;
            }
          }
          .node-label {
            flex: 1;
            font-weight: 500;
          }
          .node-type {
            margin-left: 10px;
            padding: 2px 8px;
            background-color: #f0f2f5;
            border-radius: 12px;
            font-size: 12px;
            color: #606266;
          }
          .node-actions {
            margin-left: 10px;
          }
        }
      }
    }

    .chart-view {
      .orgchart-toolbar {
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
      }
      
      .orgchart-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        min-height: 600px;
        position: relative;
        overflow: hidden;
        
        .orgchart-wrapper {
          width: 100%;
          height: calc(100vh - 400px);
          position: relative;
          
          ::v-deep .orgchart {
            background-image: none !important;
            .node {
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
              transition: all 0.3s ease;
              &.department {
                background-color: #e6f3ff;
                border: 1px solid #b8d8ff;
              }
              &.position {
                background-color: #fff0e6;
                border: 1px solid #ffd7b8;
              }
              &.employee {
                background-color: #e6ffe6;
                border: 1px solid #b8ffb8;
              }
              .title {
                font-weight: bold;
                font-size: 14px;
                padding: 5px;
                word-break: break-all;
                background-color: transparent;
                color: #333;
              }
              .content {
                font-size: 12px;
                padding: 3px 5px;
                border-top: none;
              }
              .headcount {
                font-size: 11px;
                color: #666;
                background-color: rgba(255,255,255,0.5);
                padding: 2px 5px;
                border-top: 1px dashed #ccc;
              }
            }
          }
        }
        
        .empty-chart {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
        }
      }
    }

    .search-results {
      h3 {
        margin-bottom: 15px;
        color: #303133;
      }
    }
  }
}

@media (max-width: 768px) {
  .organization-chart-container {
    padding: 10px;

    .page-header {
      text-align: center;
      .title {
        font-size: 20px;
      }
    }

    .stats-row {
      .el-col {
        margin-bottom: 10px;
      }
    }

    .main-content {
      .chart-view {
        .orgchart-toolbar {
          padding: 10px;
          flex-direction: column;
          gap: 5px;
        }
        
        .orgchart-container {
          .orgchart-wrapper {
            height: 400px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .organization-chart-container {
    .stats-row {
      .el-col {
        span: 12;
      }
    }
  }
}
</style>
<template>
  <div class="deptManagement-container">
    <el-row :gutter="15">
      <!-- 部门管理主表 -->
      <el-col>
        <vab-query-form>
          <vab-query-form-left-panel :span="6">
            <el-button
              v-if="$perms('system_dept_insert')"
              icon="el-icon-plus"
              type="primary"
              @click="handleInsert"
            > 添加 </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="18">
            <el-form :inline="true" :model="queryForm" @submit.native.prevent>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.name_LIKE"
                  placeholder="请输入部门名称"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm.deptCode_LIKE"
                  placeholder="请输入部门编码"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="queryData">
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <el-table
          ref="deptTable"
          v-loading="listLoading"
          :data="tableData"
          :element-loading-text="elementLoadingText"
          :highlight-current-row="true"
          @current-change="setSelectRows"
        >
          <el-table-column
            show-overflow-tooltip
            prop="name"
            label="部门名称"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="deptCode"
            label="部门编码"
            min-width="120"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="parentDepartmentId"
            label="上级部门"
            min-width="120"
            :formatter="formatParentDept"
          ></el-table-column>

          <el-table-column show-overflow-tooltip label="状态" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="headcount"
            label="编制人数"
            align="center"
            min-width="80"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="responsibilities"
            label="职责描述"
            min-width="150"
          ></el-table-column>

          <el-table-column
            fixed="right"
            show-overflow-tooltip
            label="操作"
            width="160"
            v-if="$perms('system_dept_update') || $perms('system_dept_delete')"
          >
            <template v-slot="scope">
              <el-button
                v-if="$perms('system_dept_update')"
                type="text"
                @click="handleUpdate(scope.row)"
              > 编辑 </el-button>

              <el-divider direction="vertical"></el-divider>

              <el-dropdown trigger="click">
                <span class="el-dropdown-link">
                  更多
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-if="$perms('system_dept_insert')"
                    @click.native="handleAddChild(scope.row)"
                  >添加子部门</el-dropdown-item>

                  <el-dropdown-item
                    v-if="$perms('system_dept_delete')"
                    @click.native="handleDelete(scope.row)"
                  >删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>

    <!-- 编辑 -->
    <dept-edit ref="edit" :dept-list="deptList" @fetchData="fetchData"></dept-edit>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getList, doDelete, doDeleteAll, findByParentId, getAll, doUpdate } from '@/api/system/department/deptManagement';
import { isNull } from "@/utils/validate";
import DeptEdit from './components/DeptManagementEdit'

export default {
  name: "DeptManagement",
  components: { DeptEdit },
  data() {
    return {
      // 当前行
      currentRow: null,
      // 字典
      dict: {},
      // 查询表单
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        name_LIKE: "",
        tenantId: null,
        dataMonth: null
      },
      // 返回数据
      tableData: [],
      // 部门列表
      deptList: [],
      // list 加载状态
      listLoading: true,
      // 总条数
      total: 0,
      elementLoadingText: "正在加载...",
      layout: "total, sizes, prev, pager, next, jumper",
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
    readyToFetch() {
      return this.tenantId && this.selectedMonth;
    },
  },
  watch: {
    readyToFetch: {
      handler(isReady) {
        if (isReady) {
          this.fetchData();
          this.fetchAllDepts();
        }
      },
      immediate: true,
    },
  },
  created() {
    this.dict.common_status = this.$getDictList("common_status");
  },
  methods: {
    setSelectRows(val) {
      this.currentRow = val;
    },
    handleInsert() {
      this.$refs["edit"].showEdit(null, this.queryForm.dataMonth);
    },
    handleAddChild(row) {
      if (row.id) {
        this.$refs["edit"].showEdit(null, this.queryForm.dataMonth, row);
      }
    },
    handleUpdate(row) {
      if (row.id) {
        this.$refs["edit"].showEdit(row);
      }
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm("你确定要删除当前部门吗", null, async () => {
          const { msg } = await doDelete({ id: row.id });
          this.$baseMessage(msg, "success");
          await this.fetchData();
        });
      }
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val;
      this.fetchData();
    },
    queryData() {
      this.queryForm.pageNo = 1;
      this.fetchData();
    },
    async fetchAllDepts() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      const { data } = await getAll({
        tenantId: this.tenantId,
        dataMonth: this.selectedMonth,
      });
      this.deptList = data;
    },

    // 格式化上级部门
    formatParentDept(row, column, cellValue) {
      if (cellValue) {
        const parentDept = this.deptList.find((dept) => dept.id === cellValue);
        return parentDept ? parentDept.name : '';
      }
      return '无';
    },

    async fetchData() {
      if (!this.tenantId || !this.selectedMonth) {
        return;
      }
      this.queryForm.tenantId = this.tenantId;
      this.queryForm.dataMonth = this.selectedMonth;
      this.listLoading = true;
      const { data } = await getList(this.queryForm);
      console.log("data", data);
      this.tableData = data.rows;
      console.log("tableData", this.tableData);
      this.total = Number(data.total);
      console.log("total", this.total);
      this.listLoading = false;
    },

    // 状态切换
    async handleStatusChange(row) {
      try {
        const newStatus = row.status;
        await this.$baseConfirm(
          `你确定要${
            newStatus === 1 ? '启用' : '禁用'
          } "${row.name}" 部门吗？`,
          null,
          async () => {
            const { msg } = await doUpdate({ ...row, status: newStatus });
            this.$baseMessage(msg, 'success');
            // 无需手动刷新，v-model已更新视图
          },
          async () => {
            // 如果用户取消，则将开关状态恢复原状
            row.status = newStatus === 1 ? 0 : 1;
          }
        );
      } catch (error) {
        console.error('状态更新失败:', error);
        this.$baseMessage('状态更新失败', 'error');
        // 发生错误时也恢复状态
        row.status = row.status === 1 ? 0 : 1;
      }
      return cellValue;
    },
  },
};
</script>

<style lang="scss" scoped>
.deptManagement-container {
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
}
</style>
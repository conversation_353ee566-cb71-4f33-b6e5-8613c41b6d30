# OPSLI薪酬管理模块实现文档

## 概述

本文档描述了为OPSLI系统实现的完整薪酬管理模块，包含前端Vue组件和后端Spring Boot API。该模块严格遵循OPSLI框架的标准架构模式和代码规范。

## 核心业务流程

1. **薪资项目管理（工资表编制）** - 定义基本工资、补贴、提成、奖金等薪资项目及其字段属性
2. **薪资结构设置** - 按部门和岗位配置各薪资项目的标准值、最小值、最大值
3. **员工薪资录入** - 根据员工岗位自动预填充标准值，支持手动调整实际薪资数据

## 数据库设计

基于提供的6张表结构：

### 核心表结构
- `salary_items` - 薪资项目基础定义表
- `salary_templates_monthly` - 薪资模板月度数据表
- `salary_template_items_monthly` - 薪资模板项目关联表
- `position_salary_standards_monthly` - 岗位薪资标准月度数据表
- `employee_salaries_monthly` - 员工月度薪资汇总表
- `employee_salary_details_monthly` - 员工薪资明细月度数据表

### 关键特性
- 支持多租户数据隔离
- 按月份进行数据分区管理
- 软删除和乐观锁机制
- 动态字段配置和验证规则
- 薪资计算公式引擎支持

## 后端实现

### 架构层次
```
API层 (opsli-api)
├── SalaryItemApi.java - 薪资项目接口定义
├── SalaryTemplateMonthlyApi.java - 薪资模板接口定义
├── EmployeeSalaryMonthlyApi.java - 员工薪资接口定义
└── Model类 - 数据传输对象

Web层 (opsli-modulars-system)
├── SalaryItemRestController.java - 薪资项目控制器
├── SalaryTemplateMonthlyRestController.java - 薪资模板控制器
└── EmployeeSalaryMonthlyRestController.java - 员工薪资控制器

Service层
├── ISalaryItemService.java - 薪资项目服务接口
├── SalaryItemServiceImpl.java - 薪资项目服务实现
└── 其他服务接口和实现类

Mapper层
├── SalaryItemMapper.java - 薪资项目数据访问
├── SalaryItemMapper.xml - MyBatis映射文件
└── 其他Mapper接口和XML文件

Entity层
├── SalaryItem.java - 薪资项目实体
├── SalaryTemplateMonthly.java - 薪资模板实体
└── 其他实体类
```

### 核心功能实现

#### 薪资项目管理
- 支持5种薪资分类：基本工资、补贴、提成、奖金、扣除
- 支持5种数据类型：decimal、integer、percentage、text、boolean
- 动态计算公式支持，使用${项目名称}引用其他项目值
- 系统内置项目初始化功能

#### 薪资模板管理
- 按月份管理薪资模板
- 支持4种适用范围：全部、部门、岗位、员工
- 优先级排序和默认模板设置
- 模板复制到新月份功能

#### 员工薪资管理
- 自动预填充岗位标准值
- 支持手动调整和批量计算
- 完整的审批流程：草稿→待审核→已审核→已发放→已归档
- 薪资历史记录查询

## 前端实现

### 页面结构
```
src/views/modules/system/
├── salaryEditor/ - 薪资项目编制
│   ├── index.vue - 主页面
│   └── components/SalaryItemEdit.vue - 编辑组件
├── salaryStructure/ - 薪资结构设置
│   ├── index.vue - 主页面
│   ├── components/SalaryTemplateEdit.vue - 模板编辑
│   └── components/SalaryTemplateConfig.vue - 项目配置
└── salaryRecorder/ - 员工薪资录入
    ├── index.vue - 主页面
    ├── components/EmployeeSalaryEdit.vue - 薪资编辑
    └── components/EmployeeSalaryDetail.vue - 明细查看
```

### API调用层
```
src/api/system/salary/
├── salaryItem.js - 薪资项目API
├── salaryTemplate.js - 薪资模板API
└── employeeSalary.js - 员工薪资API
```

### 核心功能特性

#### 薪资项目编制页面
- 项目列表展示和分页查询
- 按分类和数据类型筛选
- 项目新增、编辑、删除操作
- 系统内置项目初始化
- 计算公式验证功能

#### 薪资结构设置页面
- 薪资模板管理
- 按月份查询和管理
- 模板项目配置
- 岗位标准值设置
- 默认模板设置

#### 员工薪资录入页面
- 员工薪资列表展示
- 按月份和状态筛选
- 薪资明细录入和编辑
- 批量计算和审批功能
- 薪资历史记录查看

## 路由配置

在现有的HR模块中添加了3个新路由：

```javascript
{
  path: 'salaryEditor',
  name: 'SalaryEditor',
  component: () => import('@/views/modules/system/salaryEditor/index.vue'),
  meta: { title: '薪资项目编制', icon: 'el-icon-edit-outline' }
},
{
  path: 'salaryStructure', 
  name: 'SalaryStructure',
  component: () => import('@/views/modules/system/salaryStructure/index.vue'),
  meta: { title: '薪资结构设置', icon: 'el-icon-setting' }
},
{
  path: 'salaryRecorder',
  name: 'SalaryRecorder', 
  component: () => import('@/views/modules/system/salaryRecorder/index.vue'),
  meta: { title: '员工薪资录入', icon: 'el-icon-money' }
}
```

## 权限控制

### 后端权限
- `system_salary_item_*` - 薪资项目相关权限
- `system_salary_template_*` - 薪资模板相关权限  
- `system_salary_employee_*` - 员工薪资相关权限

### 前端权限控制
使用`$perms()`方法进行权限验证，控制按钮和操作的显示。

## 技术特点

### 遵循OPSLI规范
- 继承BaseRestController和CrudServiceInterface
- 使用统一的ResultWrapper返回格式
- 集成操作日志和权限控制注解
- 遵循多租户和软删除规范

### 前端设计模式
- 使用Element Plus组件库
- 遵循Vue 3 Composition API规范
- 统一的表格和表单设计风格
- 响应式布局和移动端适配

### 数据验证
- 前后端双重数据验证
- 自定义验证规则和错误提示
- 异步验证唯一性约束

## 扩展说明

该实现可以作为其他模块开发的参考模板。如需扩展功能，可以：

1. 添加更多的薪资项目类型和数据类型
2. 实现更复杂的计算公式引擎
3. 添加薪资统计报表功能
4. 实现薪资导入导出功能
5. 添加薪资变更历史追踪

所有扩展都应遵循现有的架构模式和代码规范。

## 部署说明

1. 确保数据库中已执行`salary.sql`建表脚本
2. 后端代码已集成到OPSLI主项目中
3. 前端路由已配置完成
4. 需要在系统中配置相应的菜单权限

该薪酬管理模块为OPSLI系统提供了完整的薪资管理解决方案，支持灵活的薪资结构配置和高效的薪资处理流程。

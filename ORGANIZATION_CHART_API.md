# 组织架构图 API 文档

## 概述

组织架构图API提供了完整的组织层级结构数据查询功能，整合部门、岗位、员工三类数据，支持树形结构构建、统计信息获取、节点搜索等功能。

## 基础信息

- **Base URL**: `/api/v1/system/organizationChart`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## API接口列表

### 1. 获取完整组织架构树

#### 接口信息
- **URL**: `/getOrganizationTree`
- **Method**: `GET`
- **权限**: `system_organization_chart_select`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenantId | Long | 是 | 租户ID |
| dataMonth | String | 是 | 数据月份，格式：yyyy-MM |

#### 请求示例
```http
GET /api/v1/system/organizationChart/getOrganizationTree?tenantId=1&dataMonth=2025-01
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "nodeType": "department",
      "nodeId": 1,
      "nodeName": "总公司",
      "nodeCode": "HQ",
      "parentId": null,
      "parentType": null,
      "level": 0,
      "status": 1,
      "sort": 0,
      "departmentDescription": "公司总部",
      "directSubordinates": 2,
      "totalSubordinates": 10,
      "children": [
        {
          "nodeType": "department",
          "nodeId": 2,
          "nodeName": "技术部",
          "nodeCode": "TECH",
          "parentId": 1,
          "parentType": "department",
          "level": 1,
          "status": 1,
          "children": [
            {
              "nodeType": "position",
              "nodeId": 1,
              "nodeName": "技术经理",
              "nodeCode": "TECH_MGR",
              "parentId": 2,
              "parentType": "department",
              "level": 2,
              "status": 1,
              "positionLevel": "中级",
              "headcount": 1,
              "children": [
                {
                  "nodeType": "employee",
                  "nodeId": 1,
                  "nodeName": "张三",
                  "nodeCode": "EMP001",
                  "parentId": 1,
                  "parentType": "position",
                  "level": 3,
                  "status": 1,
                  "employeeNumber": "EMP001",
                  "gender": "男",
                  "phoneNumber": "13800138000",
                  "hireDate": "2023-01-15"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 2. 获取指定部门组织架构子树

#### 接口信息
- **URL**: `/getDepartmentTree`
- **Method**: `GET`
- **权限**: `system_organization_chart_select`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenantId | Long | 是 | 租户ID |
| dataMonth | String | 是 | 数据月份，格式：yyyy-MM |
| departmentId | Long | 是 | 部门ID |

#### 请求示例
```http
GET /api/v1/system/organizationChart/getDepartmentTree?tenantId=1&dataMonth=2025-01&departmentId=2
```

### 3. 获取组织架构统计信息

#### 接口信息
- **URL**: `/getOrganizationStats`
- **Method**: `GET`
- **权限**: `system_organization_chart_select`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenantId | Long | 是 | 租户ID |
| dataMonth | String | 是 | 数据月份，格式：yyyy-MM |

#### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "nodeType": "stats",
    "nodeName": "组织架构统计",
    "directSubordinates": 5,
    "totalSubordinates": 25,
    "departmentEmployeeCount": 20,
    "positionEmployeeCount": 8
  }
}
```

### 4. 搜索组织架构节点

#### 接口信息
- **URL**: `/searchNodes`
- **Method**: `GET`
- **权限**: `system_organization_chart_select`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenantId | Long | 是 | 租户ID |
| dataMonth | String | 是 | 数据月份，格式：yyyy-MM |
| keyword | String | 是 | 搜索关键词 |
| nodeType | String | 否 | 节点类型：department/position/employee |

#### 请求示例
```http
GET /api/v1/system/organizationChart/searchNodes?tenantId=1&dataMonth=2025-01&keyword=技术&nodeType=department
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "nodeType": "department",
      "nodeId": 2,
      "nodeName": "技术部",
      "nodeCode": "TECH",
      "parentId": 1,
      "parentType": "department",
      "status": 1,
      "departmentDescription": "负责技术研发工作"
    }
  ]
}
```

## 数据模型

### OrganizationChartModel

| 字段名 | 类型 | 说明 |
|--------|------|------|
| nodeType | String | 节点类型：department/position/employee |
| nodeId | Long | 节点ID |
| nodeName | String | 节点名称 |
| nodeCode | String | 节点编码 |
| parentId | Long | 父节点ID |
| parentType | String | 父节点类型 |
| level | Integer | 节点层级 |
| status | Integer | 状态：1-启用，0-禁用 |
| sort | Integer | 排序 |
| children | List | 子节点列表 |

#### 部门特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| departmentDescription | String | 部门描述 |

#### 岗位特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| positionDescription | String | 岗位描述 |
| positionRequirements | String | 岗位要求 |
| positionLevel | String | 岗位级别 |
| headcount | Integer | 编制人数 |

#### 员工特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| employeeName | String | 员工姓名 |
| employeeNumber | String | 员工编号 |
| gender | String | 性别 |
| phoneNumber | String | 手机号码 |
| employeeStatus | String | 员工状态 |
| hireDate | String | 入职日期 |

#### 统计字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| directSubordinates | Integer | 直接下属数量 |
| totalSubordinates | Integer | 总下属数量 |
| departmentEmployeeCount | Integer | 部门员工总数 |
| positionEmployeeCount | Integer | 岗位员工数 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript/Ajax调用示例

```javascript
// 获取组织架构树
function getOrganizationTree(tenantId, dataMonth) {
  return axios.get('/api/v1/system/organizationChart/getOrganizationTree', {
    params: {
      tenantId: tenantId,
      dataMonth: dataMonth
    },
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
}

// 搜索节点
function searchNodes(tenantId, dataMonth, keyword, nodeType) {
  return axios.get('/api/v1/system/organizationChart/searchNodes', {
    params: {
      tenantId: tenantId,
      dataMonth: dataMonth,
      keyword: keyword,
      nodeType: nodeType
    },
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
}
```

### Vue.js组件调用示例

```javascript
import { getOrganizationTree, searchNodes } from '@/api/system/organizationChart/organizationChart';

export default {
  data() {
    return {
      treeData: [],
      searchResults: []
    };
  },
  methods: {
    async fetchData() {
      try {
        const { data } = await getOrganizationTree({
          tenantId: this.tenantId,
          dataMonth: this.currentMonth
        });
        this.treeData = data || [];
      } catch (error) {
        console.error('获取组织架构数据失败:', error);
      }
    },
    
    async handleSearch(keyword, nodeType) {
      try {
        const { data } = await searchNodes({
          tenantId: this.tenantId,
          dataMonth: this.currentMonth,
          keyword: keyword,
          nodeType: nodeType
        });
        this.searchResults = data || [];
      } catch (error) {
        console.error('搜索失败:', error);
      }
    }
  }
};
```

## 注意事项

1. **权限验证**：所有接口都需要相应的权限，请确保用户具有 `system_organization_chart_select` 权限
2. **租户隔离**：系统会自动根据当前用户的租户ID过滤数据
3. **数据月份**：dataMonth参数必须是有效的年月格式（yyyy-MM）
4. **性能考虑**：对于大型组织架构，建议使用部门树接口分批获取数据
5. **缓存策略**：建议在前端实现适当的缓存机制以提高用户体验

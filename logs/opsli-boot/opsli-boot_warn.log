2025-08-03 12:00:34.138 5979 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 12:55:36.083 5891 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:21:36.736 4068 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:22:18.465 3685 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:25:52.601 3849 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:29:47.540 3657 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:43:09.833 4158 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:49:33.926 3648 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:01:28.264 3759 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:04:18.199 3782 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:09:30.161 3816 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:11:57.068 3820 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:15:07.678 3691 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:23:17.664 3790 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:24:35.500 3879 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:29:07.346 3774 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:46:39.318 1055746 [http-nio-7001-exec-9] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 14:46:45.739 1062167 [http-nio-7001-exec-1] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 14:47:08.242 1084670 [http-nio-7001-exec-5] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 14:54:12.316 4003 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:05:00.202 3947 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:05:56.598 60343 [http-nio-7001-exec-3] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'salary_item_name' in 'field list'
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time, salary_item_name, salary_item_category )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'salary_item_name' in 'field list'
; bad SQL grammar []
2025-08-03 15:11:38.373 4004 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:11:56.963 22594 [http-nio-7001-exec-2] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:12:26.020 51651 [http-nio-7001-exec-4] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:18:42.338 3745 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:19:06.914 28321 [http-nio-7001-exec-2] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:19:47.709 69116 [http-nio-7001-exec-4] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:20:46.472 127879 [http-nio-7001-exec-7] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:22:09.729 211136 [http-nio-7001-exec-6] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:22:51.829 253236 [http-nio-7001-exec-4] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:32:54.064 3817 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:38:10.388 320141 [http-nio-7001-exec-10] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:43:17.262 627015 [http-nio-7001-exec-4] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:43:36.095 3953 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:45:04.509 92367 [http-nio-7001-exec-7] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:45:20.962 108820 [http-nio-7001-exec-2] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:45:24.983 112841 [http-nio-7001-exec-5] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:45:29.102 116960 [http-nio-7001-exec-7] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:50:34.843 3829 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:51:05.308 34294 [http-nio-7001-exec-10] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:21.177 50163 [http-nio-7001-exec-4] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:23.641 52627 [http-nio-7001-exec-8] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:32.010 60996 [http-nio-7001-exec-9] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:45.859 74845 [http-nio-7001-exec-1] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:55:45.344 6518 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:56:49.112 70286 [http-nio-7001-exec-8] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1944596258485932033-2025-07-01-1951910380204666882-1951897765856' for key 'salary_template_items_monthly.uk_template_item'
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable,   status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?,   ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1944596258485932033-2025-07-01-1951910380204666882-1951897765856' for key 'salary_template_items_monthly.uk_template_item'
; Duplicate entry '1944596258485932033-2025-07-01-1951910380204666882-1951897765856' for key 'salary_template_items_monthly.uk_template_item'
2025-08-03 16:19:06.790 4239 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 16:19:26.067 23516 [http-nio-7001-exec-2] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：模板项目配置冲突，请检查是否有重复的薪资项目
2025-08-03 16:19:42.579 40028 [http-nio-7001-exec-8] WARN  o.o.c.h.GlobalExceptionHandler - 业务异常 - 异常编号：500 - 异常信息：模板项目配置冲突，请检查是否有重复的薪资项目
2025-08-03 16:30:54.078 4220 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'

2025-08-02 13:39:20.513 1111 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-08-02 13:39:20.513 1111 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-08-02 13:39:20.710 1308 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-08-02 13:39:20.710 1308 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/AttendanceMapper.class]
2025-08-02 13:39:20.710 1308 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-08-02 13:39:20.710 1308 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-08-02 13:39:20.710 1308 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-08-02 13:39:20.710 1308 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/EmployeeMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/PositionMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/salary/mapper/EmployeeSalaryDetailMonthlyMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/salary/mapper/EmployeeSalaryMonthlyMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/salary/mapper/PositionSalaryStandardMonthlyMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/salary/mapper/SalaryItemMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/salary/mapper/SalaryTemplateMonthlyMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-08-02 13:39:20.711 1309 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-08-02 13:39:20.712 1310 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-08-02 13:39:20.712 1310 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-08-02 13:39:20.712 1310 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-08-02 13:39:20.712 1310 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-08-02 13:39:20.713 1311 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-08-02 13:39:20.714 1312 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'attendanceMapper' and 'org.opsli.modulars.system.attendance.mapper.AttendanceMapper' mapperInterface
2025-08-02 13:39:20.714 1312 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-08-02 13:39:20.715 1313 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-08-02 13:39:20.715 1313 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-08-02 13:39:20.715 1313 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeMapper' and 'org.opsli.modulars.system.employee.mapper.EmployeeMapper' mapperInterface
2025-08-02 13:39:20.715 1313 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-08-02 13:39:20.716 1314 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-08-02 13:39:20.716 1314 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-08-02 13:39:20.716 1314 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-08-02 13:39:20.716 1314 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-08-02 13:39:20.716 1314 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-08-02 13:39:20.716 1314 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionMapper' and 'org.opsli.modulars.system.position.mapper.PositionMapper' mapperInterface
2025-08-02 13:39:20.718 1316 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-08-02 13:39:20.718 1316 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-08-02 13:39:20.718 1316 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeSalaryDetailMonthlyMapper' and 'org.opsli.modulars.system.salary.mapper.EmployeeSalaryDetailMonthlyMapper' mapperInterface
2025-08-02 13:39:20.718 1316 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeSalaryMonthlyMapper' and 'org.opsli.modulars.system.salary.mapper.EmployeeSalaryMonthlyMapper' mapperInterface
2025-08-02 13:39:20.718 1316 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionSalaryStandardMonthlyMapper' and 'org.opsli.modulars.system.salary.mapper.PositionSalaryStandardMonthlyMapper' mapperInterface
2025-08-02 13:39:20.719 1317 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'salaryItemMapper' and 'org.opsli.modulars.system.salary.mapper.SalaryItemMapper' mapperInterface
2025-08-02 13:39:20.719 1317 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'salaryTemplateItemMonthlyMapper' and 'org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper' mapperInterface
2025-08-02 13:39:20.719 1317 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'salaryTemplateMonthlyMapper' and 'org.opsli.modulars.system.salary.mapper.SalaryTemplateMonthlyMapper' mapperInterface
2025-08-02 13:39:20.719 1317 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-08-02 13:39:20.719 1317 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-08-02 13:39:20.719 1317 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-08-02 13:39:20.719 1317 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-08-02 13:39:20.720 1318 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-08-02 13:39:20.720 1318 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-08-02 13:39:20.720 1318 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-08-02 13:39:20.720 1318 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-08-02 13:39:20.720 1318 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-08-02 13:39:20.720 1318 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-08-02 13:39:20.720 1318 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-08-02 13:39:20.721 1319 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-08-02 13:39:20.721 1319 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-08-02 13:39:20.721 1319 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-08-02 13:39:24.989 5587 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@762db992'
2025-08-02 13:39:24.989 5587 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@15a591d9'
2025-08-02 13:39:24.989 5587 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@9e4388d]}'
2025-08-02 13:39:25.051 5649 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-08-02 13:39:25.064 5662 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/xml/AttendanceMapper.xml]'
2025-08-02 13:39:25.077 5675 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-08-02 13:39:25.086 5684 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-08-02 13:39:25.096 5694 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-08-02 13:39:25.107 5705 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/xml/EmployeeMapper.xml]'
2025-08-02 13:39:25.114 5712 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-08-02 13:39:25.120 5718 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-08-02 13:39:25.126 5724 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-08-02 13:39:25.133 5731 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-08-02 13:39:25.140 5738 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-08-02 13:39:25.149 5747 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-08-02 13:39:25.156 5754 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/xml/PositionMapper.xml]'
2025-08-02 13:39:25.162 5760 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-08-02 13:39:25.168 5766 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-08-02 13:39:25.174 5772 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/salary/mapper/xml/SalaryItemMapper.xml]'
2025-08-02 13:39:25.179 5777 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-08-02 13:39:25.185 5783 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-08-02 13:39:25.190 5788 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-08-02 13:39:25.196 5794 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-08-02 13:39:25.201 5799 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-08-02 13:39:25.202 5800 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-08-02 13:39:25.206 5804 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-08-02 13:39:25.211 5809 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-08-02 13:39:25.215 5813 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-08-02 13:39:25.219 5817 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-08-02 13:39:25.223 5821 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-08-02 13:39:25.230 5828 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-08-02 13:39:25.236 5834 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-08-02 13:39:29.951 10549 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.

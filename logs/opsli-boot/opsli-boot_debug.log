2025-07-31 15:58:06.609 1131 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 15:58:06.609 1131 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-31 15:58:06.800 1322 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-31 15:58:06.800 1322 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/AttendanceMapper.class]
2025-07-31 15:58:06.800 1322 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-31 15:58:06.800 1322 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-31 15:58:06.800 1322 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/EmployeeMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/PositionMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-31 15:58:06.801 1323 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-31 15:58:06.802 1324 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-31 15:58:06.802 1324 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-31 15:58:06.802 1324 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-31 15:58:06.802 1324 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-31 15:58:06.802 1324 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-31 15:58:06.802 1324 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-31 15:58:06.802 1324 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-31 15:58:06.803 1325 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-31 15:58:06.804 1326 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'attendanceMapper' and 'org.opsli.modulars.system.attendance.mapper.AttendanceMapper' mapperInterface
2025-07-31 15:58:06.804 1326 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-31 15:58:06.804 1326 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-31 15:58:06.804 1326 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-31 15:58:06.805 1327 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeMapper' and 'org.opsli.modulars.system.employee.mapper.EmployeeMapper' mapperInterface
2025-07-31 15:58:06.805 1327 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-31 15:58:06.805 1327 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-31 15:58:06.805 1327 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-31 15:58:06.805 1327 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-31 15:58:06.805 1327 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-31 15:58:06.805 1327 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-31 15:58:06.806 1328 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionMapper' and 'org.opsli.modulars.system.position.mapper.PositionMapper' mapperInterface
2025-07-31 15:58:06.806 1328 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-31 15:58:06.806 1328 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-31 15:58:06.807 1329 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-31 15:58:06.808 1330 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-31 15:58:06.808 1330 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-31 15:58:06.808 1330 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-31 15:58:06.808 1330 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-31 15:58:06.809 1331 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-31 15:58:11.042 5564 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@6d387e9b'
2025-07-31 15:58:11.042 5564 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@304704ae'
2025-07-31 15:58:11.043 5565 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@5978ba12]}'
2025-07-31 15:58:11.102 5624 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-31 15:58:11.115 5637 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/xml/AttendanceMapper.xml]'
2025-07-31 15:58:11.127 5649 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-31 15:58:11.135 5657 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-31 15:58:11.143 5665 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-31 15:58:11.154 5676 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/xml/EmployeeMapper.xml]'
2025-07-31 15:58:11.160 5682 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-31 15:58:11.167 5689 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-31 15:58:11.173 5695 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-31 15:58:11.180 5702 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-31 15:58:11.187 5709 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-31 15:58:11.194 5716 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-31 15:58:11.201 5723 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/xml/PositionMapper.xml]'
2025-07-31 15:58:11.209 5731 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-31 15:58:11.214 5736 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-31 15:58:11.220 5742 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-31 15:58:11.228 5750 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-31 15:58:11.232 5754 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-31 15:58:11.237 5759 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-31 15:58:11.242 5764 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-31 15:58:11.243 5765 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-31 15:58:11.249 5771 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-31 15:58:11.254 5776 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-31 15:58:11.258 5780 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-31 15:58:11.262 5784 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-31 15:58:11.266 5788 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-31 15:58:11.270 5792 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-31 15:58:11.274 5796 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-31 15:58:15.654 10176 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 16:20:20.279 1054 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 16:20:20.279 1054 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/AttendanceMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/EmployeeMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-31 16:20:20.466 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/PositionMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-31 16:20:20.467 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-31 16:20:20.468 1243 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-31 16:20:20.469 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'attendanceMapper' and 'org.opsli.modulars.system.attendance.mapper.AttendanceMapper' mapperInterface
2025-07-31 16:20:20.470 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-31 16:20:20.470 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-31 16:20:20.470 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-31 16:20:20.470 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeMapper' and 'org.opsli.modulars.system.employee.mapper.EmployeeMapper' mapperInterface
2025-07-31 16:20:20.471 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-31 16:20:20.471 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-31 16:20:20.471 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-31 16:20:20.471 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-31 16:20:20.471 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-31 16:20:20.472 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-31 16:20:20.472 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionMapper' and 'org.opsli.modulars.system.position.mapper.PositionMapper' mapperInterface
2025-07-31 16:20:20.472 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-31 16:20:20.476 1251 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-31 16:20:20.476 1251 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-31 16:20:20.476 1251 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-31 16:20:20.477 1252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-31 16:20:20.477 1252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-31 16:20:20.477 1252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-31 16:20:20.477 1252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-31 16:20:20.477 1252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-31 16:20:20.477 1252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-31 16:20:20.478 1253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-31 16:20:20.478 1253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-31 16:20:20.478 1253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-31 16:20:20.478 1253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-31 16:20:20.478 1253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-31 16:20:20.478 1253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-31 16:20:24.692 5467 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@75b25ec3'
2025-07-31 16:20:24.692 5467 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@69e736bf'
2025-07-31 16:20:24.692 5467 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@2ef63b6d]}'
2025-07-31 16:20:24.751 5526 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-31 16:20:24.764 5539 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/xml/AttendanceMapper.xml]'
2025-07-31 16:20:24.777 5552 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-31 16:20:24.785 5560 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-31 16:20:24.796 5571 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-31 16:20:24.805 5580 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/xml/EmployeeMapper.xml]'
2025-07-31 16:20:24.812 5587 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-31 16:20:24.818 5593 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-31 16:20:24.824 5599 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-31 16:20:24.831 5606 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-31 16:20:24.838 5613 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-31 16:20:24.845 5620 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-31 16:20:24.852 5627 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/xml/PositionMapper.xml]'
2025-07-31 16:20:24.858 5633 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-31 16:20:24.863 5638 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-31 16:20:24.868 5643 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-31 16:20:24.875 5650 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-31 16:20:24.879 5654 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-31 16:20:24.884 5659 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-31 16:20:24.890 5665 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-31 16:20:24.892 5667 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-31 16:20:24.897 5672 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-31 16:20:24.902 5677 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-31 16:20:24.906 5681 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-31 16:20:24.910 5685 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-31 16:20:24.914 5689 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-31 16:20:24.919 5694 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-31 16:20:24.923 5698 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-31 16:20:29.552 10327 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 16:22:34.050 977  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 16:22:34.050 977  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-31 16:22:34.235 1162 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/AttendanceMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/EmployeeMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/PositionMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-31 16:22:34.236 1163 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-31 16:22:34.237 1164 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-31 16:22:34.237 1164 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-31 16:22:34.237 1164 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-31 16:22:34.237 1164 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-31 16:22:34.237 1164 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-31 16:22:34.237 1164 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-31 16:22:34.238 1165 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'attendanceMapper' and 'org.opsli.modulars.system.attendance.mapper.AttendanceMapper' mapperInterface
2025-07-31 16:22:34.238 1165 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-31 16:22:34.239 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-31 16:22:34.239 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-31 16:22:34.239 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeMapper' and 'org.opsli.modulars.system.employee.mapper.EmployeeMapper' mapperInterface
2025-07-31 16:22:34.239 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-31 16:22:34.239 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-31 16:22:34.239 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-31 16:22:34.239 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionMapper' and 'org.opsli.modulars.system.position.mapper.PositionMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-31 16:22:34.240 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-31 16:22:34.241 1168 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-31 16:22:34.241 1168 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-31 16:22:34.241 1168 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-31 16:22:34.242 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-31 16:22:34.242 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-31 16:22:34.242 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-31 16:22:34.242 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-31 16:22:34.242 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-31 16:22:34.243 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-31 16:22:34.243 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-31 16:22:34.243 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-31 16:22:36.402 3329 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@1ee126de'
2025-07-31 16:22:36.402 3329 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@22cce199'
2025-07-31 16:22:36.402 3329 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@2480acc3]}'
2025-07-31 16:22:36.457 3384 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-31 16:22:36.471 3398 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/attendance/mapper/xml/AttendanceMapper.xml]'
2025-07-31 16:22:36.484 3411 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-31 16:22:36.492 3419 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-31 16:22:36.501 3428 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-31 16:22:36.511 3438 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/xml/EmployeeMapper.xml]'
2025-07-31 16:22:36.520 3447 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-31 16:22:36.526 3453 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-31 16:22:36.532 3459 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-31 16:22:36.540 3467 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-31 16:22:36.548 3475 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-31 16:22:36.592 3519 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-31 16:22:36.606 3533 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/xml/PositionMapper.xml]'
2025-07-31 16:22:36.611 3538 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-31 16:22:36.617 3544 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-31 16:22:36.622 3549 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-31 16:22:36.632 3559 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-31 16:22:36.636 3563 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-31 16:22:36.641 3568 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-31 16:22:36.647 3574 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-31 16:22:36.649 3576 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-31 16:22:36.653 3580 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-31 16:22:36.659 3586 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-31 16:22:36.663 3590 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-31 16:22:36.667 3594 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-31 16:22:36.672 3599 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-31 16:22:36.678 3605 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-31 16:22:36.683 3610 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-31 16:22:41.258 8185 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.

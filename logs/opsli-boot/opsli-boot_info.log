2025-08-02 13:39:19:835 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 58689 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-02 13:39:19:837 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-02 13:39:21:228 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-02 13:39:21:228 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-02 13:39:21:259 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-02 13:39:24:857 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-02 13:39:24:858 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-02 13:39:24:859 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 13:39:25:428 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-02 13:39:26:848 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-02 13:39:26:879 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-02 13:39:27:022 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-02 13:39:28:019 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-02 13:39:28:063 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-02 13:39:28:065 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-02 13:39:30:260 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 10.764 seconds (process running for 11.335)
2025-08-02 13:47:04:109 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 13:47:59:243 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-02 13:47:59:243 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-02 13:47:59:243 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:47:59:243 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-02 13:47:59:247 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754113679167.265"]
}
2025-08-02 13:47:59:331 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 89ms -------------
2025-08-02 13:47:59:348 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-02 13:47:59:348 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-02 13:47:59:348 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: OPTIONS
2025-08-02 13:47:59:348 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 0ms -------------
2025-08-02 13:47:59:354 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-02 13:47:59:354 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-02 13:47:59:354 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:47:59:354 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-02 13:48:00:571 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1217ms -------------
2025-08-02 13:48:00:795 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:48:00:795 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:48:00:795 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:48:00:795 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:48:00:996 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 201ms -------------
2025-08-02 13:48:01:125 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-02 13:48:01:126 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-02 13:48:01:126 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:48:01:126 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-02 13:48:01:295 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 170ms -------------
2025-08-02 13:48:01:623 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:48:01:623 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:48:01:623 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:48:01:623 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:48:01:885 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 262ms -------------
2025-08-02 13:48:28:631 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:48:28:637 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:48:28:637 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:48:28:637 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:48:28:638 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求开始 -------------
2025-08-02 13:48:28:638 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/employee/findPage
2025-08-02 13:48:28:638 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:48:28:638 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"dataMonth":["2025-08-01"],
	"employeeName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"status_EQ":[""]
}
2025-08-02 13:48:28:649 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求结束 => 耗时: 11ms -------------
2025-08-02 13:48:28:827 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 198ms -------------
2025-08-02 13:49:28:239 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-02 13:49:28:243 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-02 13:49:28:243 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:49:28:243 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-02 13:49:28:974 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 735ms -------------
2025-08-02 13:49:29:119 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:49:29:120 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:49:29:120 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:49:29:120 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:49:29:745 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 626ms -------------
2025-08-02 13:49:29:967 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-02 13:49:29:968 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-02 13:49:29:968 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:49:29:968 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-02 13:49:30:531 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 564ms -------------
2025-08-02 13:49:30:836 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:49:30:836 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:49:30:836 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:49:30:836 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:49:30:932 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求开始 -------------
2025-08-02 13:49:30:932 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/employee/findPage
2025-08-02 13:49:30:932 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:49:30:932 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"dataMonth":["2025-08-01"],
	"employeeName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"status_EQ":[""]
}
2025-08-02 13:49:30:935 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求结束 => 耗时: 3ms -------------
2025-08-02 13:49:31:056 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 220ms -------------
2025-08-02 13:49:51:775 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-02 13:49:51:775 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-02 13:49:51:775 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:49:51:775 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-02 13:49:52:448 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 673ms -------------
2025-08-02 13:49:52:596 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:49:52:596 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:49:52:596 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:49:52:596 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:49:52:745 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 149ms -------------
2025-08-02 13:49:52:950 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-02 13:49:52:950 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-02 13:49:52:950 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:49:52:950 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-02 13:49:53:047 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 97ms -------------
2025-08-02 13:49:53:378 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:49:53:379 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:49:53:379 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:49:53:379 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:49:53:603 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 225ms -------------
2025-08-02 13:50:00:153 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-08-02 13:50:00:154 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-08-02 13:50:00:154 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:00:154 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-08-02 13:50:00:154 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754113799999.135"],
	"typeCode":["menu_type"]
}
2025-08-02 13:50:00:206 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 53ms -------------
2025-08-02 13:50:00:406 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-08-02 13:50:00:406 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-08-02 13:50:00:406 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:00:406 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-08-02 13:50:00:406 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754113800208.1606"],
	"typeCode":["no_yes"]
}
2025-08-02 13:50:00:430 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 24ms -------------
2025-08-02 13:50:00:572 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-08-02 13:50:00:572 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-08-02 13:50:00:572 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:00:572 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-08-02 13:50:00:572 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754113800439.8748"],
	"typeCode":["menu_role_label"]
}
2025-08-02 13:50:00:595 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 23ms -------------
2025-08-02 13:50:00:836 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:50:00:836 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求开始 -------------
2025-08-02 13:50:00:836 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:50:00:836 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:00:837 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:50:00:836 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy
2025-08-02 13:50:00:837 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:00:837 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTreePageByLazy
2025-08-02 13:50:00:837 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["0"]
}
2025-08-02 13:50:00:983 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 147ms -------------
2025-08-02 13:50:01:122 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求结束 => 耗时: 286ms -------------
2025-08-02 13:50:07:739 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求开始 -------------
2025-08-02 13:50:07:739 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy
2025-08-02 13:50:07:739 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:07:739 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTreePageByLazy
2025-08-02 13:50:07:739 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944613334869053441"]
}
2025-08-02 13:50:07:999 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求结束 => 耗时: 260ms -------------
2025-08-02 13:50:10:513 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求开始 -------------
2025-08-02 13:50:10:513 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy
2025-08-02 13:50:10:513 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:10:513 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTreePageByLazy
2025-08-02 13:50:10:513 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944614742926266370"]
}
2025-08-02 13:50:10:859 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求结束 => 耗时: 346ms -------------
2025-08-02 13:50:23:930 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求开始 -------------
2025-08-02 13:50:23:930 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy
2025-08-02 13:50:23:930 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:50:23:930 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTreePageByLazy
2025-08-02 13:50:23:930 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944614584046030849"]
}
2025-08-02 13:50:24:187 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求结束 => 耗时: 257ms -------------
2025-08-02 13:51:30:214 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getParent" 请求开始 -------------
2025-08-02 13:51:30:215 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/getParent
2025-08-02 13:51:30:215 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:51:30:215 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.getParent
2025-08-02 13:51:30:215 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944614742926266370"]
}
2025-08-02 13:51:30:471 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getParent" 请求结束 => 耗时: 257ms -------------
2025-08-02 13:51:45:691 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getParent" 请求开始 -------------
2025-08-02 13:51:45:691 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/getParent
2025-08-02 13:51:45:691 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:51:45:691 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.getParent
2025-08-02 13:51:45:691 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944613334869053441"]
}
2025-08-02 13:51:45:819 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getParent" 请求结束 => 耗时: 128ms -------------
2025-08-02 13:53:57:923 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求开始 -------------
2025-08-02 13:53:57:924 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/employee/findPage
2025-08-02 13:53:57:925 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:53:57:925 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"dataMonth":["2025-08-01"],
	"employeeName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"status_EQ":[""]
}
2025-08-02 13:53:57:932 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求结束 => 耗时: 9ms -------------
2025-08-02 13:54:44:032 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-02 13:54:44:032 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-02 13:54:44:032 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:54:44:032 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-02 13:54:44:032 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754114084028.771"]
}
2025-08-02 13:54:44:059 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 27ms -------------
2025-08-02 13:54:44:076 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-02 13:54:44:077 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-02 13:54:44:077 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:54:44:077 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-02 13:54:44:785 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 709ms -------------
2025-08-02 13:54:45:019 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:54:45:020 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:54:45:020 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:54:45:020 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:54:45:608 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 589ms -------------
2025-08-02 13:54:45:750 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-02 13:54:45:750 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-02 13:54:45:750 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:54:45:751 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-02 13:54:45:828 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 78ms -------------
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求开始 -------------
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/employee/findPage
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:54:46:172 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"dataMonth":["2025-08-01"],
	"employeeName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"status_EQ":[""]
}
2025-08-02 13:54:46:177 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求结束 => 耗时: 5ms -------------
2025-08-02 13:54:46:326 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 154ms -------------
2025-08-02 13:55:01:212 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-02 13:55:01:212 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-02 13:55:01:212 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:55:01:213 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-02 13:55:01:496 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 284ms -------------
2025-08-02 13:55:01:502 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/slipCount" 请求开始 -------------
2025-08-02 13:55:01:502 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/slipCount
2025-08-02 13:55:01:502 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:01:502 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginCommonRestController.slipCount
2025-08-02 13:55:01:502 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"username":["system"]
}
2025-08-02 13:55:01:525 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/slipCount" 请求结束 => 耗时: 23ms -------------
2025-08-02 13:55:07:887 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-02 13:55:07:887 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-02 13:55:07:887 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:55:07:887 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-02 13:55:08:493 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 606ms -------------
2025-08-02 13:55:08:721 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:55:08:721 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:55:08:721 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:08:721 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:55:08:868 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 147ms -------------
2025-08-02 13:55:09:009 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-02 13:55:09:010 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-02 13:55:09:010 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-02 13:55:09:010 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-02 13:55:09:168 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 159ms -------------
2025-08-02 13:55:09:490 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:55:09:490 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:55:09:491 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:09:491 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:55:09:724 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 234ms -------------
2025-08-02 13:55:27:070 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-08-02 13:55:27:071 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-08-02 13:55:27:071 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:27:071 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-08-02 13:55:27:071 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754114126910.453"],
	"typeCode":["role_data_scope"]
}
2025-08-02 13:55:27:111 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 41ms -------------
2025-08-02 13:55:27:300 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:55:27:300 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:55:27:300 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:27:300 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:55:27:303 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/role/findPage" 请求开始 -------------
2025-08-02 13:55:27:303 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/role/findPage
2025-08-02 13:55:27:303 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:27:303 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: RoleRestController.findPage
2025-08-02 13:55:27:303 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"roleCode_EQ":[""],
	"roleName_LIKE":[""],
	"label_EQ":["1"]
}
2025-08-02 13:55:27:530 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 230ms -------------
2025-08-02 13:55:27:627 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/role/findPage" 请求结束 => 耗时: 323ms -------------
2025-08-02 13:55:27:655 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:55:27:655 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:55:27:655 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:27:655 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:55:27:801 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 146ms -------------
2025-08-02 13:55:28:017 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-02 13:55:28:017 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-02 13:55:28:017 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:28:017 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-02 13:55:28:163 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 146ms -------------
2025-08-02 13:55:28:296 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-08-02 13:55:28:296 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-08-02 13:55:28:296 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:28:296 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-08-02 13:55:28:296 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754114128165.9995"],
	"typeCode":["menu_role_label"]
}
2025-08-02 13:55:28:323 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 27ms -------------
2025-08-02 13:55:38:502 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getMenuAndPermsTree" 请求开始 -------------
2025-08-02 13:55:38:503 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/getMenuAndPermsTree
2025-08-02 13:55:38:503 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:38:503 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.getMenuAndPermsTree
2025-08-02 13:55:38:503 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"label":["1"]
}
2025-08-02 13:55:38:823 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getMenuAndPermsTree" 请求结束 => 耗时: 321ms -------------
2025-08-02 13:55:39:043 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/role/perms/getPerms" 请求开始 -------------
2025-08-02 13:55:39:043 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/role/perms/getPerms
2025-08-02 13:55:39:044 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:39:044 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: RoleMenuRefRestController.getPerms
2025-08-02 13:55:39:044 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"roleId":["1944598272972394497"]
}
2025-08-02 13:55:39:086 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/role/perms/getPerms" 请求结束 => 耗时: 43ms -------------
2025-08-02 13:55:51:865 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getMenuAndPermsTree" 请求开始 -------------
2025-08-02 13:55:51:865 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/getMenuAndPermsTree
2025-08-02 13:55:51:865 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:51:865 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.getMenuAndPermsTree
2025-08-02 13:55:51:865 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"label":["1"]
}
2025-08-02 13:55:52:232 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getMenuAndPermsTree" 请求结束 => 耗时: 367ms -------------
2025-08-02 13:55:52:365 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/role/perms/getPerms" 请求开始 -------------
2025-08-02 13:55:52:365 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/role/perms/getPerms
2025-08-02 13:55:52:365 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:55:52:365 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: RoleMenuRefRestController.getPerms
2025-08-02 13:55:52:366 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"roleId":["1944601156816441346"]
}
2025-08-02 13:55:52:389 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/role/perms/getPerms" 请求结束 => 耗时: 24ms -------------
2025-08-02 13:56:23:353 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-08-02 13:56:23:353 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-08-02 13:56:23:353 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:56:23:353 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-08-02 13:56:23:353 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754114183214.104"],
	"typeCode":["menu_type"]
}
2025-08-02 13:56:23:377 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 24ms -------------
2025-08-02 13:56:23:596 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-08-02 13:56:23:596 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-08-02 13:56:23:596 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:56:23:596 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-08-02 13:56:23:596 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754114183380.6848"],
	"typeCode":["no_yes"]
}
2025-08-02 13:56:23:621 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 25ms -------------
2025-08-02 13:56:23:773 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求开始 -------------
2025-08-02 13:56:23:774 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy
2025-08-02 13:56:23:774 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:56:23:774 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTreePageByLazy
2025-08-02 13:56:23:774 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["0"]
}
2025-08-02 13:56:24:113 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求结束 => 耗时: 340ms -------------
2025-08-02 13:56:27:764 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求开始 -------------
2025-08-02 13:56:27:765 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy
2025-08-02 13:56:27:765 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:56:27:765 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTreePageByLazy
2025-08-02 13:56:27:765 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944613334869053441"]
}
2025-08-02 13:56:28:023 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求结束 => 耗时: 259ms -------------
2025-08-02 13:56:30:508 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求开始 -------------
2025-08-02 13:56:30:508 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy
2025-08-02 13:56:30:508 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:56:30:508 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTreePageByLazy
2025-08-02 13:56:30:508 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944613700432007170"]
}
2025-08-02 13:56:30:763 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTreePageByLazy" 请求结束 => 耗时: 255ms -------------
2025-08-02 13:56:32:605 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getParent" 请求开始 -------------
2025-08-02 13:56:32:605 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/getParent
2025-08-02 13:56:32:605 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-02 13:56:32:605 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.getParent
2025-08-02 13:56:32:605 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"parentId":["1944613700432007170"]
}
2025-08-02 13:56:32:748 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/getParent" 请求结束 => 耗时: 143ms -------------

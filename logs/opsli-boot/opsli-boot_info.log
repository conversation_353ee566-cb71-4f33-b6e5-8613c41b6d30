2025-08-03 10:51:34:526 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 10:51:34:528 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 10:51:34:531 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 10:51:34:533 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 10:51:34:534 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754189494510.0146"]
}
2025-08-03 10:51:34:628 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 102ms -------------
2025-08-03 10:51:34:644 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 10:51:34:645 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 10:51:34:645 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: OPTIONS
2025-08-03 10:51:34:645 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1ms -------------
2025-08-03 10:51:34:668 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 10:51:34:668 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 10:51:34:668 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 10:51:34:669 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 10:51:38:347 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 3679ms -------------
2025-08-03 10:51:38:502 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 10:51:38:503 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 10:51:38:503 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 10:51:38:503 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 10:51:38:801 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 299ms -------------
2025-08-03 10:51:38:944 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 10:51:38:945 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 10:51:38:945 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 10:51:38:945 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 10:51:39:018 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 74ms -------------
2025-08-03 10:51:39:420 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 10:51:39:421 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 10:51:39:421 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 10:51:39:421 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 10:51:39:630 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 210ms -------------
2025-08-03 10:52:08:287 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 10:52:08:287 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 10:52:08:287 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 10:52:08:288 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 10:52:08:288 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 10:52:08:288 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 10:52:08:288 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 10:52:08:288 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 10:52:08:288 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 10:52:08:424 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 137ms -------------
2025-08-03 10:52:08:495 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 208ms -------------
2025-08-03 10:56:35:402 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 10:56:35:403 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 10:56:35:403 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 10:56:35:403 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 10:56:35:404 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":["部门"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 10:56:35:640 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 238ms -------------
2025-08-03 11:55:21:417 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 11:55:21:418 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 11:55:21:418 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:55:21:418 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 11:55:21:641 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 223ms -------------
2025-08-03 11:55:21:871 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 11:55:21:872 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 11:55:21:872 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 11:55:21:872 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 11:55:22:013 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 142ms -------------
2025-08-03 11:55:22:532 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 11:55:22:532 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 11:55:22:532 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 11:55:22:532 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 11:55:22:532 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:55:22:532 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:55:22:533 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 11:55:22:533 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 11:55:22:533 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 11:55:22:693 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 161ms -------------
2025-08-03 11:55:22:741 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 209ms -------------
2025-08-03 11:55:44:566 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 11:55:44:569 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 11:55:44:571 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:55:44:572 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 11:55:44:828 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 262ms -------------
2025-08-03 11:55:45:052 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 11:55:45:053 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 11:55:45:053 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 11:55:45:053 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 11:55:45:126 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 74ms -------------
2025-08-03 11:55:45:543 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 11:55:45:544 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 11:55:45:544 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:55:45:544 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 11:55:45:564 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 11:55:45:565 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 11:55:45:565 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:55:45:565 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 11:55:45:565 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 11:55:45:719 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 155ms -------------
2025-08-03 11:55:45:759 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 216ms -------------
2025-08-03 11:56:15:446 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 11:56:15:447 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 11:56:15:448 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:56:15:448 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 11:56:15:663 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 217ms -------------
2025-08-03 11:56:15:916 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 11:56:15:916 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 11:56:15:917 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 11:56:15:917 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 11:56:15:994 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 78ms -------------
2025-08-03 11:56:16:443 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 11:56:16:443 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 11:56:16:443 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:56:16:443 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 11:56:16:444 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 11:56:16:444 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 11:56:16:444 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:56:16:444 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 11:56:16:444 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 11:56:16:580 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 137ms -------------
2025-08-03 11:56:16:648 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 205ms -------------
2025-08-03 11:58:18:416 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 11:58:18:417 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 11:58:18:417 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:58:18:418 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 11:58:18:418 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 11:58:18:668 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 252ms -------------
2025-08-03 11:59:02:891 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 11:59:02:891 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 11:59:02:891 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 11:59:02:891 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 11:59:02:892 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 11:59:03:137 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 247ms -------------
2025-08-03 12:00:28:578 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 65346 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 12:00:28:579 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 12:00:29:943 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 12:00:29:943 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 12:00:29:981 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 12:00:33:486 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 12:00:33:487 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 12:00:33:487 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 12:00:34:138 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 12:00:35:613 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 12:00:35:640 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 12:00:35:749 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 12:00:36:775 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 12:00:36:817 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 12:00:36:819 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 12:00:38:710 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 10.456 seconds (process running for 10.86)
2025-08-03 12:02:45:960 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 12:02:46:560 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:02:46:560 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:02:46:560 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:02:46:561 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:02:46:783 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 223ms -------------
2025-08-03 12:02:46:898 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 12:02:46:899 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 12:02:46:899 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:02:46:899 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 12:02:47:054 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 156ms -------------
2025-08-03 12:02:47:373 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:02:47:373 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:02:47:373 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:02:47:373 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:02:47:621 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 248ms -------------
2025-08-03 12:02:55:397 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:02:55:397 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 12:02:55:397 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:02:55:397 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 12:02:55:398 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:02:55:398 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:02:55:398 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:02:55:398 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 12:02:55:401 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:02:55:558 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 161ms -------------
2025-08-03 12:02:55:814 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 417ms -------------
2025-08-03 12:10:57:869 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 12:10:57:873 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 12:10:57:873 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:10:57:873 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 12:10:57:874 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:10:58:066 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 198ms -------------
2025-08-03 12:45:16:789 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 12:45:16:790 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 12:45:16:790 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:45:16:790 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 12:45:16:791 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:45:16:953 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 164ms -------------
2025-08-03 12:47:43:459 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:47:43:460 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:47:43:460 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:47:43:460 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:47:43:638 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 179ms -------------
2025-08-03 12:47:43:768 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 12:47:43:768 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 12:47:43:768 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:47:43:768 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 12:47:43:827 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 59ms -------------
2025-08-03 12:47:44:131 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:47:44:131 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:47:44:132 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:47:44:132 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:47:44:132 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 12:47:44:132 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 12:47:44:132 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:47:44:132 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 12:47:44:132 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:47:44:281 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 149ms -------------
2025-08-03 12:47:44:297 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 166ms -------------
2025-08-03 12:48:01:359 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:48:01:360 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:48:01:360 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:48:01:360 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:48:01:533 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 174ms -------------
2025-08-03 12:48:01:653 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 12:48:01:653 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 12:48:01:653 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:48:01:654 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 12:48:01:788 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 135ms -------------
2025-08-03 12:48:02:120 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:48:02:121 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:48:02:121 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:48:02:121 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:48:02:138 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 12:48:02:138 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 12:48:02:138 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:48:02:139 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 12:48:02:139 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:48:02:361 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 223ms -------------
2025-08-03 12:48:02:371 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 251ms -------------
2025-08-03 12:49:05:226 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 12:49:05:227 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 12:49:05:227 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:49:05:227 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 12:49:05:228 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:49:05:381 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 155ms -------------
2025-08-03 12:55:30:601 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 72137 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 12:55:30:602 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 12:55:31:915 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 12:55:31:915 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 12:55:31:954 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 12:55:35:431 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 12:55:35:433 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 12:55:35:433 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 12:55:36:083 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 12:55:37:467 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 12:55:37:492 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 12:55:37:616 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 12:55:38:623 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 12:55:38:653 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 12:55:38:655 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 12:55:40:598 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 10.31 seconds (process running for 10.738)
2025-08-03 12:58:53:459 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 12:58:53:510 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 12:58:53:510 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 12:58:53:511 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:58:53:511 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 12:58:53:514 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754197133397.2495"]
}
2025-08-03 12:58:53:586 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 76ms -------------
2025-08-03 12:58:53:609 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 12:58:53:609 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 12:58:53:609 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: OPTIONS
2025-08-03 12:58:53:610 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 0ms -------------
2025-08-03 12:58:53:618 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 12:58:53:618 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 12:58:53:618 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:58:53:618 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 12:58:54:837 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1219ms -------------
2025-08-03 12:58:55:045 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:58:55:045 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:58:55:045 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:58:55:045 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:58:55:191 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 146ms -------------
2025-08-03 12:58:55:289 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 12:58:55:289 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 12:58:55:289 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:58:55:289 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 12:58:55:346 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 57ms -------------
2025-08-03 12:58:55:657 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:58:55:658 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:58:55:658 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:58:55:658 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:58:55:800 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 143ms -------------
2025-08-03 12:59:01:951 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求开始 -------------
2025-08-03 12:59:01:951 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 12:59:01:952 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/employee/findPage
2025-08-03 12:59:01:952 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 12:59:01:952 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:59:01:952 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:59:01:952 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: EmployeeSalaryMonthlyRestController.findPage
2025-08-03 12:59:01:952 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 12:59:01:952 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"employeeName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:59:01:970 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求结束 => 耗时: 19ms -------------
2025-08-03 12:59:02:091 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 140ms -------------
2025-08-03 12:59:03:976 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 12:59:03:977 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 12:59:03:977 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 12:59:03:977 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 12:59:03:977 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"scopeType_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 12:59:04:191 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 215ms -------------
2025-08-03 12:59:34:981 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 12:59:34:986 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 12:59:34:987 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:59:34:987 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 12:59:35:103 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 122ms -------------
2025-08-03 12:59:56:300 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 12:59:56:302 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 12:59:56:302 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:59:56:302 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 12:59:56:494 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 194ms -------------
2025-08-03 12:59:56:610 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/insert" 请求开始 -------------
2025-08-03 12:59:56:611 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/insert
2025-08-03 12:59:56:611 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 12:59:56:611 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.insert
2025-08-03 12:59:57:114 INFO  [http-nio-7001-exec-6] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=31588775-ad0e-4eb6-a58e-bb1b84f1e84f, createTime=1754197197112, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateMonthlyRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"所有员工","scopeType":"全部","scopeConfig":"","isDefault":false,"priority":100,"status":true,"description":"适用于所有员工","izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增薪资模板数据, operationType=insert, runTime=494, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 12:59:57:120 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/insert" 请求结束 => 耗时: 510ms -------------
2025-08-03 13:17:17:874 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:17:17:877 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:17:17:878 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:17:17:878 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:17:17:878 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:17:18:034 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 161ms -------------
2025-08-03 13:19:29:082 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:19:29:083 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:19:29:083 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:19:29:083 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:19:29:083 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:19:29:311 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 229ms -------------
2025-08-03 13:20:00:885 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:20:00:886 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:20:00:886 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:20:00:886 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:20:00:886 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:20:01:056 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 171ms -------------
2025-08-03 13:21:33:142 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 75266 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 13:21:33:142 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 13:21:34:462 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 13:21:34:462 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 13:21:34:495 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 13:21:36:015 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 13:21:36:016 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 13:21:36:017 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 13:21:36:736 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:21:38:164 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 13:21:38:200 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 13:21:38:317 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 13:21:39:335 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 13:21:39:376 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 13:21:39:377 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 13:21:39:405 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource start closing ....
2025-08-03 13:21:39:407 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1} closing ...
2025-08-03 13:21:39:411 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1} closed
2025-08-03 13:21:39:411 INFO  [main] c.b.d.d.destroyer.DefaultDataSourceDestroyer-? dynamic-datasource close the datasource named [master] success,
2025-08-03 13:21:39:411 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource all closed success,bye
2025-08-03 13:21:39:414 INFO  [main] org.apache.catalina.core.StandardService-? Stopping service [Tomcat]
2025-08-03 13:22:15:168 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 75836 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 13:22:15:169 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 13:22:16:441 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 13:22:16:441 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 13:22:16:469 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 13:22:17:926 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 13:22:17:927 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 13:22:17:927 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 13:22:18:465 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:22:19:909 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 13:22:19:941 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 13:22:20:137 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 13:22:21:064 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 13:22:21:095 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 13:22:21:097 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 13:22:21:121 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource start closing ....
2025-08-03 13:22:21:122 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1} closing ...
2025-08-03 13:22:21:125 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1} closed
2025-08-03 13:22:21:125 INFO  [main] c.b.d.d.destroyer.DefaultDataSourceDestroyer-? dynamic-datasource close the datasource named [master] success,
2025-08-03 13:22:21:125 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource all closed success,bye
2025-08-03 13:22:21:127 INFO  [main] org.apache.catalina.core.StandardService-? Stopping service [Tomcat]
2025-08-03 13:25:49:118 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 77646 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 13:25:49:119 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 13:25:50:488 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 13:25:50:488 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 13:25:50:524 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 13:25:52:050 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 13:25:52:052 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 13:25:52:052 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 13:25:52:601 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:25:53:995 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 13:25:54:021 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 13:25:54:126 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 13:25:55:218 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 13:25:55:258 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 13:25:55:260 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 13:25:57:136 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.309 seconds (process running for 8.802)
2025-08-03 13:26:46:886 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 13:26:47:379 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:26:47:380 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:26:47:380 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:26:47:380 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:26:47:681 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 302ms -------------
2025-08-03 13:26:47:793 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 13:26:47:793 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 13:26:47:793 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:26:47:793 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 13:26:47:868 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 75ms -------------
2025-08-03 13:26:48:169 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:26:48:170 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:26:48:170 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:26:48:170 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:26:48:317 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 148ms -------------
2025-08-03 13:26:53:566 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:26:53:566 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:26:53:567 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:26:53:567 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:26:53:567 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:26:53:567 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:26:53:567 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:26:53:567 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:26:53:569 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:26:53:722 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 156ms -------------
2025-08-03 13:26:53:934 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 368ms -------------
2025-08-03 13:27:12:793 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 13:27:12:793 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 13:27:12:793 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:27:12:793 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 13:27:12:837 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 44ms -------------
2025-08-03 13:29:44:257 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 78707 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 13:29:44:258 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 13:29:45:479 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 13:29:45:479 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 13:29:45:515 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 13:29:46:924 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 13:29:46:925 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 13:29:46:926 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 13:29:47:540 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:29:48:893 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 13:29:48:917 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 13:29:49:033 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 13:29:50:093 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 13:29:50:133 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 13:29:50:135 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 13:29:51:937 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 7.972 seconds (process running for 8.311)
2025-08-03 13:30:07:522 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 13:30:07:581 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 13:30:07:581 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 13:30:07:581 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:07:581 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 13:30:07:583 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754199007445.2324"]
}
2025-08-03 13:30:07:644 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 63ms -------------
2025-08-03 13:30:07:663 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 13:30:07:663 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 13:30:07:663 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:30:07:663 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 13:30:08:962 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1299ms -------------
2025-08-03 13:30:09:100 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:30:09:100 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:30:09:100 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:09:100 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:30:09:268 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 169ms -------------
2025-08-03 13:30:09:470 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 13:30:09:470 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 13:30:09:470 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:30:09:470 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 13:30:09:534 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 64ms -------------
2025-08-03 13:30:09:902 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:30:09:902 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:30:09:902 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:09:902 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:30:09:917 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:30:09:918 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:30:09:918 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:09:918 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:30:09:918 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:30:10:067 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 165ms -------------
2025-08-03 13:30:10:080 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 163ms -------------
2025-08-03 13:30:18:926 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 13:30:18:928 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 13:30:18:928 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:30:18:928 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 13:30:19:078 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 152ms -------------
2025-08-03 13:30:28:881 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 13:30:28:881 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 13:30:28:881 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:30:28:881 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 13:30:28:964 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 83ms -------------
2025-08-03 13:30:29:087 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/insert" 请求开始 -------------
2025-08-03 13:30:29:088 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/insert
2025-08-03 13:30:29:088 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:30:29:088 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.insert
2025-08-03 13:30:29:367 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=2582f5de-d493-4764-84c6-a2bc853b2cf2, createTime=1754199029364, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateMonthlyRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"人力部","status":true,"description":"人力","izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增薪资模板数据, operationType=insert, runTime=258, returnValue={"msg":"新增薪资模板数据成功","code":0,"timestamp":1754199029349}, tenantId=1944596258485932033, logType=2)
2025-08-03 13:30:29:374 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/insert" 请求结束 => 耗时: 287ms -------------
2025-08-03 13:30:29:518 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:30:29:518 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:30:29:519 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:29:519 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:30:29:519 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:30:29:658 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 140ms -------------
2025-08-03 13:30:35:709 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:30:35:709 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:30:35:709 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:35:709 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:30:35:888 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 179ms -------------
2025-08-03 13:30:36:014 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 13:30:36:014 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 13:30:36:014 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:30:36:014 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 13:30:36:172 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 158ms -------------
2025-08-03 13:30:36:482 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:30:36:483 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:30:36:483 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:36:483 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:30:36:493 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:30:36:493 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:30:36:493 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:30:36:493 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:30:36:493 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:30:36:705 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 212ms -------------
2025-08-03 13:30:36:731 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 249ms -------------
2025-08-03 13:43:06:136 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 80230 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 13:43:06:136 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 13:43:07:671 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 13:43:07:672 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 13:43:07:705 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 13:43:09:261 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 13:43:09:263 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 13:43:09:263 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 13:43:09:833 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:43:11:256 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 13:43:11:285 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 13:43:11:416 INFO  [redisson-netty-5-8] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 13:43:12:436 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 13:43:12:479 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 13:43:12:480 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 13:43:14:347 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.576 seconds (process running for 9.075)
2025-08-03 13:43:25:951 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 13:43:26:415 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:43:26:415 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:43:26:415 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:43:26:415 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:43:26:608 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 193ms -------------
2025-08-03 13:43:26:712 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 13:43:26:713 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 13:43:26:713 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:43:26:713 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 13:43:26:863 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 151ms -------------
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:43:27:212 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:43:27:214 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:43:27:445 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 233ms -------------
2025-08-03 13:43:27:695 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 483ms -------------
2025-08-03 13:44:06:798 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:44:06:800 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:44:06:800 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:44:06:800 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:44:06:963 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 165ms -------------
2025-08-03 13:44:07:078 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 13:44:07:078 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 13:44:07:079 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:44:07:079 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 13:44:07:216 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 138ms -------------
2025-08-03 13:44:07:780 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:44:07:780 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:44:07:780 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:44:07:780 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:44:07:781 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:44:07:781 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:44:07:781 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:44:07:781 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:44:07:781 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:44:07:923 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 143ms -------------
2025-08-03 13:44:07:955 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 175ms -------------
2025-08-03 13:45:05:584 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:45:05:585 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:45:05:585 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:45:05:585 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:45:05:585 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 13:45:05:776 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 192ms -------------
2025-08-03 13:46:32:743 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:46:32:743 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:46:32:744 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:46:32:744 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:46:32:744 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 13:46:33:032 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 289ms -------------
2025-08-03 13:46:51:385 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:46:51:387 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:46:51:387 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:46:51:387 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:46:51:387 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:46:51:655 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 270ms -------------
2025-08-03 13:47:04:604 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:47:04:605 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:47:04:605 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:47:04:605 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:47:04:752 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 148ms -------------
2025-08-03 13:47:04:952 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 13:47:04:952 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 13:47:04:952 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:47:04:952 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 13:47:05:008 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 56ms -------------
2025-08-03 13:47:05:529 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:47:05:529 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:47:05:529 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:47:05:529 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:47:05:542 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:47:05:542 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:47:05:542 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:47:05:543 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:47:05:543 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:47:05:674 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 145ms -------------
2025-08-03 13:47:05:709 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 167ms -------------
2025-08-03 13:49:30:639 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 81137 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 13:49:30:640 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 13:49:31:902 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 13:49:31:902 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 13:49:31:931 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 13:49:33:384 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 13:49:33:386 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 13:49:33:386 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 13:49:33:926 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 13:49:35:317 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 13:49:35:341 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 13:49:35:466 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 13:49:36:530 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 13:49:36:573 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 13:49:36:574 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 13:49:38:399 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.043 seconds (process running for 8.395)
2025-08-03 13:50:06:505 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 13:50:07:041 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:50:07:041 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:50:07:041 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:50:07:041 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:50:07:240 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 199ms -------------
2025-08-03 13:50:07:439 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 13:50:07:439 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 13:50:07:439 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 13:50:07:439 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 13:50:07:500 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 61ms -------------
2025-08-03 13:50:07:995 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 13:50:07:995 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 13:50:07:996 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 13:50:07:996 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 13:50:07:996 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:50:07:996 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:50:07:996 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 13:50:07:996 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 13:50:07:998 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 13:50:08:142 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 147ms -------------
2025-08-03 13:50:08:458 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 463ms -------------
2025-08-03 13:50:21:793 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 13:50:21:794 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 13:50:21:794 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 13:50:21:794 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 13:50:21:794 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 13:50:21:873 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 79ms -------------
2025-08-03 14:01:24:913 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 84568 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:01:24:914 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:01:26:204 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:01:26:204 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:01:26:240 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:01:27:698 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:01:27:699 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:01:27:699 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:01:28:264 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:01:29:697 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:01:29:721 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:01:29:839 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:01:30:889 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:01:30:931 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:01:30:932 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:01:32:790 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.195 seconds (process running for 8.545)
2025-08-03 14:02:09:234 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:02:09:309 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 14:02:09:310 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 14:02:09:310 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:02:09:310 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 14:02:09:312 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754200929144.3958"]
}
2025-08-03 14:02:09:388 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 79ms -------------
2025-08-03 14:02:09:416 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 14:02:09:416 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 14:02:09:416 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:02:09:416 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 14:02:10:779 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1363ms -------------
2025-08-03 14:02:11:003 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:02:11:003 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:02:11:003 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:02:11:003 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:02:11:181 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 178ms -------------
2025-08-03 14:02:11:310 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:02:11:310 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:02:11:310 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:02:11:310 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:02:11:470 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 160ms -------------
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:02:12:060 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:02:12:061 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:02:12:082 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 22ms -------------
2025-08-03 14:02:12:242 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 182ms -------------
2025-08-03 14:04:14:812 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 85212 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:04:14:813 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:04:16:083 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:04:16:083 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:04:16:113 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:04:17:584 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:04:17:585 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:04:17:585 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:04:18:199 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:04:19:607 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:04:19:638 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:04:19:763 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:04:20:808 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:04:20:850 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:04:20:851 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:04:22:753 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.247 seconds (process running for 8.613)
2025-08-03 14:04:31:467 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:04:32:075 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:04:32:076 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:04:32:076 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:04:32:076 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:04:32:285 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 210ms -------------
2025-08-03 14:04:32:490 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:04:32:490 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:04:32:490 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:04:32:490 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:04:32:558 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 68ms -------------
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:04:33:096 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:04:33:099 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:04:33:118 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 22ms -------------
2025-08-03 14:04:33:277 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 181ms -------------
2025-08-03 14:06:11:881 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:06:11:882 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:06:11:882 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:06:11:882 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:06:11:883 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:06:11:902 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 21ms -------------
2025-08-03 14:09:26:752 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 86098 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:09:26:753 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:09:28:123 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:09:28:123 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:09:28:154 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:09:29:553 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:09:29:554 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:09:29:555 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:09:30:161 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:09:31:560 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:09:31:584 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:09:31:777 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:09:32:834 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:09:32:865 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:09:32:866 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:09:34:786 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.351 seconds (process running for 8.707)
2025-08-03 14:09:43:917 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:09:44:455 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:09:44:455 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:09:44:455 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:09:44:456 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:09:44:647 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 192ms -------------
2025-08-03 14:09:44:756 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:09:44:756 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:09:44:756 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:09:44:756 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:09:44:895 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 139ms -------------
2025-08-03 14:09:45:423 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:09:45:424 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:09:45:424 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:09:45:424 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:09:45:439 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:09:45:440 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:09:45:440 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:09:45:440 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:09:45:441 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:09:45:461 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 22ms -------------
2025-08-03 14:09:45:558 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 135ms -------------
2025-08-03 14:11:53:628 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 86661 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:11:53:629 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:11:54:947 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:11:54:948 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:11:54:976 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:11:56:511 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:11:56:512 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:11:56:512 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:11:57:068 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:11:58:469 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:11:58:494 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:11:58:636 INFO  [redisson-netty-5-7] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:11:59:686 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:11:59:709 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:11:59:710 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:12:01:625 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.296 seconds (process running for 8.628)
2025-08-03 14:12:15:944 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:12:16:512 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:12:16:512 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:12:16:512 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:12:16:512 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:12:16:737 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 225ms -------------
2025-08-03 14:12:16:948 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:12:16:948 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:12:16:948 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:12:16:948 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:12:17:016 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 68ms -------------
2025-08-03 14:12:17:529 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:12:17:529 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:12:17:529 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:12:17:529 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:12:17:530 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:12:17:530 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:12:17:530 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:12:17:530 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:12:17:531 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:12:17:699 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 169ms -------------
2025-08-03 14:12:18:031 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 502ms -------------
2025-08-03 14:15:04:369 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 87269 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:15:04:370 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:15:05:632 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:15:05:632 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:15:05:661 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:15:07:102 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:15:07:104 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:15:07:104 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:15:07:678 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:15:09:153 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:15:09:177 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:15:09:293 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:15:10:330 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:15:10:372 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:15:10:373 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:15:12:321 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.25 seconds (process running for 8.609)
2025-08-03 14:15:18:987 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:15:19:524 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:15:19:525 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:15:19:525 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:15:19:525 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:15:19:764 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 240ms -------------
2025-08-03 14:15:19:993 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:15:19:993 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:15:19:993 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:15:19:993 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:15:20:070 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 77ms -------------
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:15:20:538 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:15:20:539 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:15:20:729 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 191ms -------------
2025-08-03 14:15:20:999 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 461ms -------------
2025-08-03 14:15:31:057 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:15:31:057 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:15:31:058 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:15:31:058 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:15:31:058 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:15:31:251 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 194ms -------------
2025-08-03 14:15:32:826 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:15:32:826 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:15:32:826 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:15:32:826 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:15:32:827 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:15:32:848 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 22ms -------------
2025-08-03 14:23:14:278 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 89012 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:23:14:279 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:23:15:603 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:23:15:603 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:23:15:634 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:23:17:102 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:23:17:103 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:23:17:103 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:23:17:664 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:23:19:179 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:23:19:207 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:23:19:324 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:23:20:383 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:23:20:416 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:23:20:418 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:23:20:459 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource start closing ....
2025-08-03 14:23:20:462 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1} closing ...
2025-08-03 14:23:20:467 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1} closed
2025-08-03 14:23:20:467 INFO  [main] c.b.d.d.destroyer.DefaultDataSourceDestroyer-? dynamic-datasource close the datasource named [master] success,
2025-08-03 14:23:20:467 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource all closed success,bye
2025-08-03 14:23:20:469 INFO  [main] org.apache.catalina.core.StandardService-? Stopping service [Tomcat]
2025-08-03 14:24:32:026 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 90447 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:24:32:027 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:24:33:367 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:24:33:367 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:24:33:398 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:24:34:898 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:24:34:900 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:24:34:900 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:24:35:500 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:24:36:862 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:24:36:886 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:24:37:025 INFO  [redisson-netty-5-8] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:24:38:064 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:24:38:090 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:24:38:091 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:24:40:175 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.464 seconds (process running for 8.947)
2025-08-03 14:25:24:460 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:25:24:546 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 14:25:24:546 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 14:25:24:547 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:25:24:547 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 14:25:24:549 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754202324367.3962"]
}
2025-08-03 14:25:24:636 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 90ms -------------
2025-08-03 14:25:24:656 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 14:25:24:657 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 14:25:24:657 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:25:24:657 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 14:25:26:312 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1656ms -------------
2025-08-03 14:25:26:543 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:25:26:543 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:25:26:543 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:25:26:543 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:25:26:716 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 173ms -------------
2025-08-03 14:25:26:829 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:25:26:829 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:25:26:830 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:25:26:830 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:25:26:903 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 74ms -------------
2025-08-03 14:25:27:229 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:25:27:229 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:25:27:229 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:25:27:229 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:25:27:383 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 154ms -------------
2025-08-03 14:25:30:538 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:25:30:538 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:25:30:538 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:25:30:538 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:25:30:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:25:30:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:25:30:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:25:30:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:25:30:557 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:25:30:771 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 233ms -------------
2025-08-03 14:25:30:806 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 250ms -------------
2025-08-03 14:25:35:024 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:25:35:025 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:25:35:025 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:25:35:025 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:25:35:025 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:25:35:181 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 157ms -------------
2025-08-03 14:25:48:628 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:25:48:629 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:25:48:629 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:25:48:629 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:25:48:629 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:25:48:759 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 131ms -------------
2025-08-03 14:26:04:861 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:26:04:861 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:26:04:861 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:26:04:862 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:26:04:862 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:26:04:871 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 10ms -------------
2025-08-03 14:29:03:971 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 91447 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:29:03:971 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:29:05:235 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:29:05:235 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:29:05:264 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:29:06:735 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:29:06:736 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:29:06:736 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:29:07:346 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:29:08:710 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:29:08:739 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:29:08:864 INFO  [redisson-netty-5-8] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:29:09:931 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:29:09:973 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:29:09:975 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:29:11:941 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.27 seconds (process running for 8.712)
2025-08-03 14:29:25:237 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:29:25:708 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:29:25:708 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:29:25:708 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:25:708 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:29:25:904 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 196ms -------------
2025-08-03 14:29:26:098 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:29:26:099 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:29:26:099 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:29:26:099 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:29:26:161 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 63ms -------------
2025-08-03 14:29:26:467 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:29:26:467 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:29:26:467 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:26:467 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:29:26:470 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:29:26:470 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:29:26:470 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:26:470 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:29:26:472 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:29:26:705 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 238ms -------------
2025-08-03 14:29:26:939 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 470ms -------------
2025-08-03 14:29:30:892 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:29:30:892 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:29:30:892 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:30:892 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:29:30:892 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:29:31:051 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 159ms -------------
2025-08-03 14:29:32:250 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:29:32:250 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:29:32:250 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:32:250 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:29:32:250 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:29:32:479 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 229ms -------------
2025-08-03 14:29:34:043 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:29:34:043 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:29:34:043 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:34:043 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:29:34:043 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:29:34:157 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 114ms -------------
2025-08-03 14:29:46:279 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:29:46:279 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:29:46:279 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:46:280 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:29:46:280 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:29:46:388 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 109ms -------------
2025-08-03 14:29:54:303 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:29:54:304 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:29:54:305 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:29:54:305 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:29:54:305 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:29:54:502 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 199ms -------------
2025-08-03 14:30:13:139 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:30:13:140 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:30:13:140 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:30:13:140 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:30:13:140 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:30:13:339 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 200ms -------------
2025-08-03 14:31:00:755 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:31:00:756 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:31:00:756 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:31:00:756 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:31:00:757 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:31:00:974 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 219ms -------------
2025-08-03 14:33:09:530 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:33:09:532 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:33:09:532 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:33:09:532 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:33:09:532 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:33:09:686 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 156ms -------------
2025-08-03 14:33:35:642 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:33:35:643 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:33:35:643 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:33:35:643 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:33:35:643 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:33:35:757 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 115ms -------------
2025-08-03 14:34:39:182 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:34:39:183 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:34:39:184 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:34:39:184 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:34:39:184 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:34:39:336 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 154ms -------------
2025-08-03 14:34:46:944 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:34:46:944 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:34:46:944 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:34:46:944 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:34:46:945 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:34:47:057 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 113ms -------------
2025-08-03 14:39:13:894 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:39:13:897 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:39:13:897 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:39:13:897 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:39:14:052 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 158ms -------------
2025-08-03 14:39:14:164 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:39:14:164 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:39:14:164 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:39:14:164 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:39:14:303 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 139ms -------------
2025-08-03 14:39:14:887 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:39:14:888 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:39:15:032 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 145ms -------------
2025-08-03 14:39:15:032 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 144ms -------------
2025-08-03 14:40:11:530 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:40:11:535 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:40:11:535 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:11:535 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:40:11:536 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:40:11:687 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 158ms -------------
2025-08-03 14:40:19:386 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:40:19:391 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:40:19:391 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:19:391 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:40:19:544 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 158ms -------------
2025-08-03 14:40:19:653 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:40:19:653 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:40:19:654 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:40:19:654 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:40:19:706 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 53ms -------------
2025-08-03 14:40:20:194 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:40:20:194 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:40:20:194 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:20:194 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:40:20:201 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:40:20:201 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:40:20:201 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:20:201 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:40:20:202 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:40:20:459 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 265ms -------------
2025-08-03 14:40:20:462 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 261ms -------------
2025-08-03 14:40:24:408 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:40:24:408 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:40:24:428 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:40:24:448 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:24:448 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:40:24:449 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:40:24:449 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:40:24:449 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:24:449 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:40:24:450 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:40:24:668 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 261ms -------------
2025-08-03 14:40:24:693 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 286ms -------------
2025-08-03 14:40:31:204 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:40:31:209 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:40:31:209 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:31:209 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:40:31:210 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:40:31:222 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:40:31:223 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:40:31:223 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:31:223 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:40:31:223 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:40:31:479 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 257ms -------------
2025-08-03 14:40:31:480 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 276ms -------------
2025-08-03 14:40:38:556 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:40:38:556 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:40:38:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:40:38:783 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 227ms -------------
2025-08-03 14:40:38:783 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 227ms -------------
2025-08-03 14:40:59:854 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:40:59:853 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:40:59:856 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:40:59:856 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:59:856 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:40:59:856 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:40:59:856 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:40:59:856 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:40:59:857 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:40:59:857 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:41:00:000 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 147ms -------------
2025-08-03 14:41:00:000 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 147ms -------------
2025-08-03 14:41:51:645 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:41:51:649 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:41:51:649 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:41:51:649 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:41:51:650 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:41:51:789 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 145ms -------------
2025-08-03 14:44:05:114 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:05:118 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:05:118 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:05:118 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:05:118 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:05:351 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 239ms -------------
2025-08-03 14:44:11:127 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:11:128 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:11:128 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:11:128 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:11:129 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:11:260 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 134ms -------------
2025-08-03 14:44:16:537 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:16:538 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:16:538 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:16:538 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:16:538 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:16:684 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 147ms -------------
2025-08-03 14:44:22:076 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:22:077 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:22:077 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:22:077 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:22:077 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:22:213 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 137ms -------------
2025-08-03 14:44:28:645 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:28:646 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:28:646 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:28:646 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:28:646 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:28:860 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 215ms -------------
2025-08-03 14:44:42:391 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:42:392 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:42:392 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:42:392 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:42:393 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:42:605 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 214ms -------------
2025-08-03 14:44:50:427 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:50:433 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:50:433 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:50:433 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:50:434 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:50:606 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 180ms -------------
2025-08-03 14:44:57:687 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:44:57:692 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:44:57:692 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:44:57:692 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:44:57:693 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:44:57:823 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 138ms -------------
2025-08-03 14:45:16:963 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:45:16:964 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:45:16:964 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:45:16:964 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:45:16:965 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:45:17:184 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 221ms -------------
2025-08-03 14:45:22:929 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:45:22:930 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:45:22:930 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:45:22:930 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:45:22:933 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:45:23:070 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 141ms -------------
2025-08-03 14:45:30:015 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:45:30:016 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:45:30:016 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:45:30:016 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:45:30:017 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:45:30:245 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 230ms -------------
2025-08-03 14:45:36:253 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:45:36:254 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:45:36:254 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:45:36:254 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:45:36:254 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:45:36:383 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 130ms -------------
2025-08-03 14:45:43:959 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:45:43:959 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:45:43:959 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:45:43:960 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:45:43:961 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:45:44:098 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 139ms -------------
2025-08-03 14:45:51:189 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:45:51:190 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:45:51:190 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:45:51:190 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:45:51:191 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:45:51:328 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 139ms -------------
2025-08-03 14:46:05:456 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:46:05:459 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:46:05:459 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:46:05:459 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:46:05:460 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:46:05:616 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 160ms -------------
2025-08-03 14:46:39:216 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 14:46:39:217 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 14:46:39:217 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:46:39:217 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 14:46:39:318 WARN  [http-nio-7001-exec-9] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 14:46:39:320 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 104ms -------------
2025-08-03 14:46:45:734 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 14:46:45:734 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 14:46:45:734 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:46:45:734 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 14:46:45:739 WARN  [http-nio-7001-exec-1] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 14:46:45:741 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 8ms -------------
2025-08-03 14:47:08:228 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 14:47:08:229 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 14:47:08:229 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:47:08:229 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 14:47:08:242 WARN  [http-nio-7001-exec-5] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 14:47:08:245 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 16ms -------------
2025-08-03 14:47:46:195 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/initSystemItems" 请求开始 -------------
2025-08-03 14:47:46:195 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/initSystemItems
2025-08-03 14:47:46:196 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:47:46:196 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.initSystemItems
2025-08-03 14:47:46:196 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:47:48:169 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=22da39fd-d034-4e91-ac56-fa1de6cb826e, createTime=1754203668165, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryItemRestController.initSystemItems, args=[], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 初始化系统内置薪资项目, operationType=insert, runTime=1922, returnValue={"msg":"初始化系统内置薪资项目成功","code":0,"timestamp":1754203668159}, tenantId=1944596258485932033, logType=2)
2025-08-03 14:47:48:179 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/initSystemItems" 请求结束 => 耗时: 1984ms -------------
2025-08-03 14:47:48:293 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:47:48:293 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:47:48:293 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:47:48:293 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:47:48:293 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:47:48:479 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 186ms -------------
2025-08-03 14:47:57:818 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:47:57:818 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:47:57:818 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:47:57:818 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:47:57:818 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["2"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:47:58:050 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 232ms -------------
2025-08-03 14:48:02:079 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:48:02:079 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:48:02:079 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:48:02:079 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:48:02:079 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:48:02:293 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 214ms -------------
2025-08-03 14:48:48:212 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 14:48:48:213 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 14:48:48:213 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:48:48:213 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 14:48:48:215 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:48:48:475 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 263ms -------------
2025-08-03 14:49:28:848 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:49:28:848 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:49:28:848 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:49:28:849 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:49:28:849 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:49:29:033 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 186ms -------------
2025-08-03 14:49:31:972 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:49:31:972 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:49:31:972 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:49:31:972 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:49:31:972 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:49:32:080 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 108ms -------------
2025-08-03 14:49:34:561 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:49:34:561 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:49:34:561 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:49:34:561 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:49:34:562 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:49:34:671 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 110ms -------------
2025-08-03 14:50:43:983 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 14:50:43:985 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 14:50:43:985 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:50:43:985 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 14:50:43:986 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:50:44:026 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 43ms -------------
2025-08-03 14:54:08:743 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 94074 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 14:54:08:744 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 14:54:10:101 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 14:54:10:101 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 14:54:10:133 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 14:54:11:718 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 14:54:11:720 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 14:54:11:720 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 14:54:12:316 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 14:54:15:426 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 14:54:15:452 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 14:54:15:869 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 14:54:17:849 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 14:54:17:892 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 14:54:17:893 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 14:54:19:816 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 11.404 seconds (process running for 11.847)
2025-08-03 14:54:38:371 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 14:54:38:445 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 14:54:38:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 14:54:38:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:54:38:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 14:54:38:448 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754204078289.0022"]
}
2025-08-03 14:54:38:514 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 69ms -------------
2025-08-03 14:54:38:548 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 14:54:38:548 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 14:54:38:548 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:54:38:548 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 14:54:39:831 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1282ms -------------
2025-08-03 14:54:39:974 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:54:39:974 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:54:39:974 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:54:39:974 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:54:40:126 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 152ms -------------
2025-08-03 14:54:40:318 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:54:40:319 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:54:40:319 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:54:40:319 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:54:40:387 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 69ms -------------
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:54:40:875 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:54:41:020 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 145ms -------------
2025-08-03 14:54:41:062 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 187ms -------------
2025-08-03 14:54:45:780 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:54:45:786 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:54:45:786 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:54:45:786 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:54:45:786 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:54:46:060 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 281ms -------------
2025-08-03 14:54:47:697 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:54:47:698 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:54:47:698 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:54:47:698 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:54:47:698 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:54:47:834 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 137ms -------------
2025-08-03 14:54:52:330 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:54:52:331 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:54:52:331 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:54:52:331 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:54:52:331 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:54:52:454 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 124ms -------------
2025-08-03 14:55:06:572 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 14:55:06:572 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 14:55:06:572 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:55:06:572 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 14:55:06:572 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:55:06:694 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=653d4b4e-59da-48b0-86bd-af6462437447, createTime=1754204106692, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897766548344834,"salaryItemName":"级数","salaryItemCategory":"基本工资","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897768276398082,"salaryItemName":"通讯补贴","salaryItemCategory":"补贴","displayOrder":3,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897768976846849,"salaryItemName":"业绩提成","salaryItemCategory":"提成","displayOrder":4,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897770818146305,"salaryItemName":"专项奖金","salaryItemCategory":"奖金","displayOrder":5,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897771581509634,"salaryItemName":"公积金扣除","salaryItemCategory":"扣除","displayOrder":6,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=56, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 14:55:06:701 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 129ms -------------
2025-08-03 14:57:02:606 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 14:57:02:608 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 14:57:02:608 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:57:02:608 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 14:57:03:302 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 696ms -------------
2025-08-03 14:57:03:479 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:57:03:479 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:57:03:479 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:57:03:479 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:57:03:648 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 169ms -------------
2025-08-03 14:57:03:754 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 14:57:03:755 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 14:57:03:755 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:57:03:755 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 14:57:03:808 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 54ms -------------
2025-08-03 14:57:04:306 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 14:57:04:306 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 14:57:04:307 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:57:04:307 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 14:57:04:314 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:57:04:314 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:57:04:314 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:57:04:314 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:57:04:314 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 14:57:04:547 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 241ms -------------
2025-08-03 14:57:04:563 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 249ms -------------
2025-08-03 14:57:12:082 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 14:57:12:082 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 14:57:12:083 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:57:12:083 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 14:57:12:083 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:57:12:338 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 256ms -------------
2025-08-03 14:57:13:971 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 14:57:13:971 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 14:57:13:971 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:57:13:971 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 14:57:13:972 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 14:57:14:084 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 113ms -------------
2025-08-03 14:57:15:553 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 14:57:15:554 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 14:57:15:554 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 14:57:15:554 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 14:57:15:554 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 14:57:15:680 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 127ms -------------
2025-08-03 14:57:47:033 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 14:57:47:034 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 14:57:47:035 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 14:57:47:035 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 14:57:47:035 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 14:57:47:110 INFO  [http-nio-7001-exec-7] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=eedde301-c15c-4c0d-803a-a9da159960d4, createTime=1754204267110, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897767252987905,"salaryItemName":"交通补贴","salaryItemCategory":"补贴","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897768976846849,"salaryItemName":"业绩提成","salaryItemCategory":"提成","displayOrder":3,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897770453241858,"salaryItemName":"年终奖金","salaryItemCategory":"奖金","displayOrder":4,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897772328095745,"salaryItemName":"个税扣除","salaryItemCategory":"扣除","displayOrder":5,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=64, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 14:57:47:125 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 92ms -------------
2025-08-03 15:04:56:670 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 95651 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 15:04:56:672 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 15:04:58:006 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 15:04:58:007 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 15:04:58:038 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 15:04:59:581 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 15:04:59:582 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 15:04:59:582 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 15:05:00:202 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:05:01:599 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 15:05:01:627 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 15:05:01:747 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 15:05:02:769 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 15:05:02:794 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 15:05:02:796 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 15:05:04:769 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.418 seconds (process running for 8.831)
2025-08-03 15:05:32:356 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 15:05:32:405 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 15:05:32:406 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 15:05:32:406 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:05:32:406 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 15:05:32:408 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754204732263.9739"]
}
2025-08-03 15:05:32:524 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 119ms -------------
2025-08-03 15:05:32:550 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 15:05:32:550 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 15:05:32:550 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: OPTIONS
2025-08-03 15:05:32:550 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 0ms -------------
2025-08-03 15:05:32:559 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 15:05:32:559 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 15:05:32:559 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:05:32:560 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 15:05:34:125 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1566ms -------------
2025-08-03 15:05:34:318 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:05:34:318 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:05:34:318 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:05:34:318 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:05:34:597 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 279ms -------------
2025-08-03 15:05:34:733 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:05:34:733 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:05:34:734 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:05:34:734 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:05:34:808 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 75ms -------------
2025-08-03 15:05:35:321 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:05:35:323 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:05:35:323 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:05:35:323 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:05:35:341 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:05:35:342 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:05:35:342 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:05:35:342 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:05:35:342 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:05:35:512 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 191ms -------------
2025-08-03 15:05:35:668 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 327ms -------------
2025-08-03 15:05:38:927 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:05:38:927 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:05:38:928 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:05:38:928 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:05:38:932 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:05:39:165 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 238ms -------------
2025-08-03 15:05:41:190 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:05:41:191 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:05:41:191 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:05:41:191 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:05:41:191 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 15:05:41:458 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 268ms -------------
2025-08-03 15:05:43:360 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:05:43:361 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:05:43:361 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:05:43:361 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:05:43:361 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:05:43:600 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 240ms -------------
2025-08-03 15:05:55:746 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:05:55:747 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:05:55:748 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:05:55:748 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:05:55:749 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:05:56:591 INFO  [http-nio-7001-exec-3] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=8536685c-5c43-4201-ba87-34cecd19340c, createTime=1754204756589, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897767928270849,"salaryItemName":"餐饮补贴","salaryItemCategory":"补贴","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897769333362690,"salaryItemName":"团队提成","salaryItemCategory":"提成","displayOrder":3,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897771216605186,"salaryItemName":"社保扣除","salaryItemCategory":"扣除","displayOrder":4,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897770453241858,"salaryItemName":"年终奖金","salaryItemCategory":"奖金","displayOrder":5,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=809, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:05:56:598 WARN  [http-nio-7001-exec-3] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'salary_item_name' in 'field list'
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time, salary_item_name, salary_item_category )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'salary_item_name' in 'field list'
; bad SQL grammar []
2025-08-03 15:05:56:602 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 856ms -------------
2025-08-03 15:11:34:802 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 96662 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 15:11:34:802 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 15:11:36:127 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 15:11:36:127 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 15:11:36:159 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 15:11:37:763 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 15:11:37:764 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 15:11:37:764 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 15:11:38:373 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:11:39:838 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 15:11:39:867 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 15:11:40:073 INFO  [redisson-netty-5-7] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 15:11:41:174 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 15:11:41:217 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 15:11:41:218 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 15:11:43:127 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.659 seconds (process running for 9.106)
2025-08-03 15:11:55:340 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 15:11:55:904 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:11:55:904 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:11:55:904 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:11:55:905 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:11:55:906 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:11:56:959 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=aef5bcac-7890-42d4-ad3d-1155792e6565, createTime=1754205116957, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897767928270849,"salaryItemName":"餐饮补贴","salaryItemCategory":"补贴","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897769333362690,"salaryItemName":"团队提成","salaryItemCategory":"提成","displayOrder":3,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897771216605186,"salaryItemName":"社保扣除","salaryItemCategory":"扣除","displayOrder":4,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897770453241858,"salaryItemName":"年终奖金","salaryItemCategory":"奖金","displayOrder":5,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=991, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:11:56:963 WARN  [http-nio-7001-exec-2] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:11:56:977 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 1073ms -------------
2025-08-03 15:12:02:126 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:12:02:127 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:12:02:127 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:12:02:127 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:12:02:298 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 172ms -------------
2025-08-03 15:12:02:422 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:12:02:422 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:12:02:422 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:12:02:422 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:12:02:490 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 68ms -------------
2025-08-03 15:12:02:951 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:12:02:953 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:12:02:952 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:12:02:953 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:12:02:953 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:12:02:953 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:12:02:953 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:12:02:953 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:12:02:953 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:12:03:196 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 245ms -------------
2025-08-03 15:12:03:206 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 254ms -------------
2025-08-03 15:12:14:473 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:12:14:476 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:12:14:476 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:12:14:476 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:12:14:476 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:12:14:773 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 301ms -------------
2025-08-03 15:12:16:364 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:12:16:364 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:12:16:364 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:12:16:365 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:12:16:365 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 15:12:16:486 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 122ms -------------
2025-08-03 15:12:17:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:12:17:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:12:17:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:12:17:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:12:17:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:12:17:850 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 207ms -------------
2025-08-03 15:12:25:762 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:12:25:763 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:12:25:763 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:12:25:763 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:12:25:763 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:12:26:017 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=5f131853-42a3-4356-826d-4212a70d782f, createTime=1754205146017, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=250, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:12:26:020 WARN  [http-nio-7001-exec-4] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:12:26:022 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 260ms -------------
2025-08-03 15:18:38:977 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 97649 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 15:18:38:977 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 15:18:40:246 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 15:18:40:246 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 15:18:40:281 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 15:18:41:728 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 15:18:41:729 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 15:18:41:729 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 15:18:42:338 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:18:43:741 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 15:18:43:776 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 15:18:43:893 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 15:18:44:926 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 15:18:44:971 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 15:18:44:972 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 15:18:46:825 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.143 seconds (process running for 8.527)
2025-08-03 15:19:05:537 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 15:19:05:991 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:19:05:992 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:19:05:992 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:19:05:992 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:19:05:993 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:19:06:910 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=a1b348b0-f9fe-4882-8b28-dd6b5db5565e, createTime=1754205546908, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=863, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:19:06:914 WARN  [http-nio-7001-exec-2] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:19:06:928 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 937ms -------------
2025-08-03 15:19:15:164 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:19:15:164 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:19:15:164 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:19:15:164 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:19:15:323 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 159ms -------------
2025-08-03 15:19:15:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:19:15:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:19:15:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:19:15:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:19:15:493 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 64ms -------------
2025-08-03 15:19:15:927 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:19:15:927 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:19:15:927 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:19:15:927 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:19:15:933 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:19:15:933 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:19:15:933 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:19:15:933 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:19:15:933 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:19:16:073 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 146ms -------------
2025-08-03 15:19:16:188 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 255ms -------------
2025-08-03 15:19:19:027 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:19:19:027 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:19:19:027 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:19:19:027 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:19:19:027 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:19:19:183 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 156ms -------------
2025-08-03 15:19:20:639 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:19:20:639 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:19:20:639 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:19:20:639 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:19:20:639 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 15:19:20:755 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 116ms -------------
2025-08-03 15:19:21:986 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:19:21:986 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:19:21:986 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:19:21:986 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:19:21:986 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:19:22:105 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 119ms -------------
2025-08-03 15:19:47:377 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:19:47:378 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:19:47:378 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:19:47:379 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:19:47:379 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:19:47:706 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=6aa991ae-d8c5-42c0-a0ae-188969ed6a47, createTime=1754205587706, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=318, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:19:47:709 WARN  [http-nio-7001-exec-4] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:19:47:710 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 333ms -------------
2025-08-03 15:20:09:790 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:20:09:791 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:20:09:791 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:20:09:791 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:20:09:945 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 155ms -------------
2025-08-03 15:20:10:067 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:20:10:067 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:20:10:068 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:20:10:068 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:20:10:213 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 146ms -------------
2025-08-03 15:20:10:778 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:20:10:778 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:20:10:778 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:20:10:778 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:20:10:794 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:20:10:795 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:20:10:795 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:20:10:795 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:20:10:795 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:20:10:920 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 126ms -------------
2025-08-03 15:20:10:923 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 145ms -------------
2025-08-03 15:20:14:280 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:20:14:280 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:20:14:280 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:20:14:280 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:20:14:280 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:20:14:531 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 252ms -------------
2025-08-03 15:20:19:761 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:20:19:762 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:20:19:762 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:20:19:762 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:20:19:762 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 15:20:19:868 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 107ms -------------
2025-08-03 15:20:23:168 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:20:23:168 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:20:23:168 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:20:23:168 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:20:23:168 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:20:23:350 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 182ms -------------
2025-08-03 15:20:46:101 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:20:46:102 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:20:46:102 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:20:46:102 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:20:46:102 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:20:46:469 INFO  [http-nio-7001-exec-7] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=257a7d5e-59d4-4943-bda8-dc5f5d8ccd87, createTime=1754205646468, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"1","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=360, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:20:46:472 WARN  [http-nio-7001-exec-7] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:20:46:474 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 373ms -------------
2025-08-03 15:21:10:703 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:21:10:703 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:21:10:704 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:10:704 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:21:10:853 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 150ms -------------
2025-08-03 15:21:11:041 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:21:11:041 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:21:11:041 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:21:11:041 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:21:11:101 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 60ms -------------
2025-08-03 15:21:11:427 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:21:11:427 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:21:11:427 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:11:427 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:21:11:661 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 234ms -------------
2025-08-03 15:21:43:805 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:21:43:806 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:21:43:806 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:43:806 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:21:44:034 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 229ms -------------
2025-08-03 15:21:44:144 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:21:44:144 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:21:44:144 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:21:44:144 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:21:44:200 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 56ms -------------
2025-08-03 15:21:44:597 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:21:44:597 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:21:44:597 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:44:597 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:21:44:745 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 148ms -------------
2025-08-03 15:21:48:065 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:21:48:065 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:21:48:065 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:48:065 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:21:48:069 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:21:48:069 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:21:48:069 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:48:069 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:21:48:069 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:21:48:288 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 219ms -------------
2025-08-03 15:21:48:293 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 228ms -------------
2025-08-03 15:21:51:092 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:21:51:093 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:21:51:093 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:51:093 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:21:51:093 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:21:51:256 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 163ms -------------
2025-08-03 15:21:53:284 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:21:53:284 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:21:53:284 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:53:284 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:21:53:284 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 15:21:53:387 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 103ms -------------
2025-08-03 15:21:54:825 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:21:54:825 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:21:54:825 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:21:54:825 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:21:54:825 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:21:54:932 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 107ms -------------
2025-08-03 15:22:09:392 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:22:09:393 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:22:09:393 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:22:09:393 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:22:09:393 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:22:09:727 INFO  [http-nio-7001-exec-6] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=7b92ced1-1fbc-4c00-918f-90f28dad7ec3, createTime=1754205729727, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=316, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:22:09:729 WARN  [http-nio-7001-exec-6] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:22:09:730 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 338ms -------------
2025-08-03 15:22:43:089 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:22:43:089 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:22:43:089 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:22:43:089 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:22:43:090 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:22:43:299 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 211ms -------------
2025-08-03 15:22:51:507 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:22:51:507 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:22:51:507 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:22:51:507 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:22:51:508 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951878315367198721"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:22:51:828 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=b5e29b56-1095-4114-8c2a-994a9b0d0c6b, createTime=1754205771827, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951878315367198721,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951878315367198721,"salaryItemId":1951897766900666369,"salaryItemName":"绩效占比","salaryItemCategory":"基本工资","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=315, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:22:51:829 WARN  [http-nio-7001-exec-4] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:22:51:830 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 323ms -------------
2025-08-03 15:23:02:258 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 15:23:02:258 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 15:23:02:258 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:23:02:258 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 15:23:02:347 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 89ms -------------
2025-08-03 15:23:12:121 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 15:23:12:122 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 15:23:12:122 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:23:12:122 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 15:23:12:123 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754205792087.5417"]
}
2025-08-03 15:23:12:213 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 92ms -------------
2025-08-03 15:23:12:235 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 15:23:12:236 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 15:23:12:236 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:23:12:236 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 15:23:12:915 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 680ms -------------
2025-08-03 15:23:13:033 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:23:13:033 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:23:13:033 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:23:13:033 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:23:13:265 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 232ms -------------
2025-08-03 15:23:13:370 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:23:13:371 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:23:13:371 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:23:13:371 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:23:13:423 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 53ms -------------
2025-08-03 15:23:13:786 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:23:13:786 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:23:13:787 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:23:13:787 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:23:13:787 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:23:13:787 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:23:13:787 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:23:13:787 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:23:13:787 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:23:13:913 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 127ms -------------
2025-08-03 15:23:13:933 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 147ms -------------
2025-08-03 15:23:17:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:23:17:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:23:17:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:23:17:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:23:17:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:23:17:231 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 140ms -------------
2025-08-03 15:23:27:420 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 15:23:27:421 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 15:23:27:421 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:23:27:421 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 15:23:27:505 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 85ms -------------
2025-08-03 15:32:50:669 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 99364 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 15:32:50:670 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 15:32:51:992 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 15:32:51:992 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 15:32:52:024 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 15:32:53:456 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 15:32:53:457 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 15:32:53:457 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 15:32:54:064 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:32:55:489 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 15:32:55:515 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 15:32:55:703 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 15:32:56:723 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 15:32:56:766 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 15:32:56:767 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 15:32:58:725 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.378 seconds (process running for 8.891)
2025-08-03 15:37:23:025 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 15:37:23:084 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-08-03 15:37:23:084 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-08-03 15:37:23:084 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:37:23:084 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-08-03 15:37:23:086 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1754206642946.6938"]
}
2025-08-03 15:37:23:160 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 75ms -------------
2025-08-03 15:37:23:183 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-08-03 15:37:23:183 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-08-03 15:37:23:183 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:37:23:183 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-08-03 15:37:24:445 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1262ms -------------
2025-08-03 15:37:24:657 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:37:24:657 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:37:24:657 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:37:24:657 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:37:24:826 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 169ms -------------
2025-08-03 15:37:24:938 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:37:24:938 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:37:24:938 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:37:24:938 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:37:25:003 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 65ms -------------
2025-08-03 15:37:25:333 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:37:25:333 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:37:25:333 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:37:25:333 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:37:25:346 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:37:25:346 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:37:25:346 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:37:25:346 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:37:25:347 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:37:25:490 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 157ms -------------
2025-08-03 15:37:25:556 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 210ms -------------
2025-08-03 15:37:29:426 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:37:29:426 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:37:29:426 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:37:29:426 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:37:29:427 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:37:29:610 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 184ms -------------
2025-08-03 15:37:38:686 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 15:37:38:688 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 15:37:38:688 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:37:38:688 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 15:37:38:906 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 220ms -------------
2025-08-03 15:37:43:592 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 15:37:43:592 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 15:37:43:592 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:37:43:592 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 15:37:43:720 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 128ms -------------
2025-08-03 15:37:49:841 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 15:37:49:841 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 15:37:49:841 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:37:49:841 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 15:37:49:972 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 131ms -------------
2025-08-03 15:37:53:581 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求开始 -------------
2025-08-03 15:37:53:581 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/checkNameUnique
2025-08-03 15:37:53:581 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:37:53:581 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.checkNameUnique
2025-08-03 15:37:53:712 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/checkNameUnique" 请求结束 => 耗时: 131ms -------------
2025-08-03 15:37:53:854 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/insert" 请求开始 -------------
2025-08-03 15:37:53:854 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/insert
2025-08-03 15:37:53:854 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:37:53:854 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.insert
2025-08-03 15:37:54:150 INFO  [http-nio-7001-exec-6] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=53825b11-296c-4621-9869-bc086f91490c, createTime=1754206674144, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateMonthlyRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"测试部","status":true,"description":"测试","izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增薪资模板数据, operationType=insert, runTime=284, returnValue={"msg":"新增薪资模板数据成功","code":0,"timestamp":1754206674140}, tenantId=1944596258485932033, logType=2)
2025-08-03 15:37:54:158 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/insert" 请求结束 => 耗时: 304ms -------------
2025-08-03 15:37:54:285 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:37:54:285 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:37:54:285 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:37:54:285 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:37:54:285 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:37:54:505 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 220ms -------------
2025-08-03 15:37:57:005 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:37:57:006 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:37:57:006 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:37:57:006 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:37:57:006 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951878315367198721"]
}
2025-08-03 15:37:57:229 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 224ms -------------
2025-08-03 15:38:00:907 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:38:00:907 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:38:00:907 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:38:00:907 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:38:00:908 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 15:38:01:022 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 115ms -------------
2025-08-03 15:38:02:563 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:38:02:563 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:38:02:563 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:38:02:564 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:38:02:564 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:38:02:692 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 129ms -------------
2025-08-03 15:38:09:854 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:38:09:855 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:38:09:855 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:38:09:855 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:38:09:855 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951910380204666882"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:38:10:387 INFO  [http-nio-7001-exec-10] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=141e8d7e-57f0-4fdd-8c42-573d292b3b23, createTime=1754206690386, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951910380204666882,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=520, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:38:10:388 WARN  [http-nio-7001-exec-10] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:38:10:389 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 535ms -------------
2025-08-03 15:43:16:833 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:43:16:836 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:43:16:836 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:43:16:836 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:43:16:837 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951910380204666882"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:43:17:257 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=eb5ab92b-3333-4b99-a68e-7636f4b2d6b4, createTime=1754206997256, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951910380204666882,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=407, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:43:17:262 WARN  [http-nio-7001-exec-4] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable, default_value, validation_rules, status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
; Data truncation: Invalid JSON text: "The document is empty." at position 0 in value for column 'salary_template_items_monthly.validation_rules'.
2025-08-03 15:43:17:272 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 439ms -------------
2025-08-03 15:43:32:550 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 1150 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 15:43:32:551 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 15:43:33:872 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 15:43:33:872 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 15:43:33:909 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 15:43:35:455 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 15:43:35:457 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 15:43:35:457 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 15:43:36:095 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:43:37:487 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 15:43:37:517 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 15:43:37:629 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 15:43:38:672 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 15:43:38:716 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 15:43:38:718 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 15:43:40:553 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.308 seconds (process running for 8.717)
2025-08-03 15:43:48:585 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 15:43:49:042 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:43:49:042 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:43:49:042 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:43:49:042 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:43:49:043 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 15:43:49:379 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 336ms -------------
2025-08-03 15:43:54:758 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:43:54:758 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:43:54:758 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:43:54:758 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:43:54:758 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 15:43:54:892 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 134ms -------------
2025-08-03 15:43:56:459 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:43:56:460 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:43:56:460 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:43:56:460 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:43:56:460 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:43:56:611 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 152ms -------------
2025-08-03 15:44:01:644 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:44:01:644 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:44:01:644 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:44:01:644 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:44:01:644 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951910380204666882"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:44:02:121 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=69e7dda6-caab-4472-8d96-61877e2aa592, createTime=1754207042119, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951910380204666882,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=424, returnValue={"msg":"批量保存模板项目配置成功","code":0,"timestamp":1754207042116}, tenantId=1944596258485932033, logType=2)
2025-08-03 15:44:02:126 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 482ms -------------
2025-08-03 15:44:02:240 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:44:02:240 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:44:02:240 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:44:02:240 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:44:02:240 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:44:02:421 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 181ms -------------
2025-08-03 15:44:04:567 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:44:04:567 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:44:04:567 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:44:04:567 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:44:04:568 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 15:44:04:763 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 196ms -------------
2025-08-03 15:44:18:383 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求开始 -------------
2025-08-03 15:44:18:384 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/employee/findPage
2025-08-03 15:44:18:384 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:44:18:384 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: EmployeeSalaryMonthlyRestController.findPage
2025-08-03 15:44:18:384 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"employeeName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:44:18:387 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求结束 => 耗时: 4ms -------------
2025-08-03 15:44:31:546 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:44:31:547 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:44:31:547 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:44:31:547 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:44:31:548 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:44:31:726 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 180ms -------------
2025-08-03 15:44:50:341 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:44:50:342 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:44:50:342 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:44:50:342 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:44:50:342 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:44:50:503 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 162ms -------------
2025-08-03 15:44:56:220 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 15:44:56:220 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 15:44:56:220 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:44:56:220 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 15:44:56:221 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:44:56:388 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 168ms -------------
2025-08-03 15:45:04:475 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:45:04:475 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:45:04:476 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:45:04:476 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:45:04:509 WARN  [http-nio-7001-exec-7] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:45:04:515 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 40ms -------------
2025-08-03 15:45:20:958 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:45:20:958 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:45:20:958 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:45:20:958 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:45:20:962 WARN  [http-nio-7001-exec-2] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:45:20:964 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 6ms -------------
2025-08-03 15:45:24:980 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:45:24:981 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:45:24:981 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:45:24:981 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:45:24:983 WARN  [http-nio-7001-exec-5] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:45:24:984 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 4ms -------------
2025-08-03 15:45:29:101 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:45:29:101 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:45:29:101 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:45:29:101 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:45:29:102 WARN  [http-nio-7001-exec-7] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:45:29:103 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 2ms -------------
2025-08-03 15:50:31:385 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 2072 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 15:50:31:385 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 15:50:32:672 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 15:50:32:672 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 15:50:32:739 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 15:50:34:271 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 15:50:34:272 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 15:50:34:273 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 15:50:34:843 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:50:36:218 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 15:50:36:250 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 15:50:36:371 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 15:50:37:409 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 15:50:37:453 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 15:50:37:454 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 15:50:39:411 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.317 seconds (process running for 8.684)
2025-08-03 15:50:48:144 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 15:50:48:626 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:50:48:626 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:50:48:626 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:50:48:626 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:50:48:923 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 297ms -------------
2025-08-03 15:50:49:040 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:50:49:040 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:50:49:040 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:50:49:041 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:50:49:110 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 70ms -------------
2025-08-03 15:50:49:482 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:50:49:483 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:50:49:483 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:50:49:483 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:50:49:485 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:50:49:495 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:50:49:495 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:50:49:495 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:50:49:495 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:50:49:656 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 161ms -------------
2025-08-03 15:50:49:832 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 350ms -------------
2025-08-03 15:50:52:380 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:50:52:380 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:50:52:380 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:50:52:380 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:50:52:380 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:50:52:631 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 252ms -------------
2025-08-03 15:50:54:233 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:50:54:234 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:50:54:234 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:50:54:234 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:50:54:234 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 15:50:54:370 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 137ms -------------
2025-08-03 15:50:57:910 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 15:50:57:910 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 15:50:57:910 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:50:57:910 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 15:50:57:911 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:50:58:058 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 148ms -------------
2025-08-03 15:51:05:237 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:51:05:237 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:51:05:237 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:51:05:237 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:51:05:308 WARN  [http-nio-7001-exec-10] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:05:310 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 73ms -------------
2025-08-03 15:51:21:171 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:51:21:171 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:51:21:171 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:51:21:171 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:51:21:177 WARN  [http-nio-7001-exec-4] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:21:178 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 8ms -------------
2025-08-03 15:51:23:638 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:51:23:638 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:51:23:638 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:51:23:638 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:51:23:641 WARN  [http-nio-7001-exec-8] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:23:644 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 6ms -------------
2025-08-03 15:51:32:008 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:51:32:008 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:51:32:008 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:51:32:008 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:51:32:010 WARN  [http-nio-7001-exec-9] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:32:011 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 4ms -------------
2025-08-03 15:51:45:855 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:51:45:856 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:51:45:856 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:51:45:856 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:51:45:859 WARN  [http-nio-7001-exec-1] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：9800 - 异常信息：参数验证错误: 薪资项目分类，不能为空! 
2025-08-03 15:51:45:861 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 6ms -------------
2025-08-03 15:54:57:556 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 15:54:57:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 15:54:57:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:54:57:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 15:54:57:557 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:54:57:740 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 184ms -------------
2025-08-03 15:55:39:237 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 3120 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 15:55:39:238 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 15:55:41:195 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 15:55:41:195 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 15:55:41:244 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 15:55:44:662 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 15:55:44:664 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 15:55:44:665 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 15:55:45:344 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 15:55:47:071 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 15:55:47:106 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 15:55:47:280 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 15:55:48:614 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 15:55:48:665 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 15:55:48:666 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 15:55:50:894 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 11.976 seconds (process running for 12.411)
2025-08-03 15:56:00:313 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 15:56:00:942 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:56:00:942 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:56:00:942 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:00:942 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:56:01:159 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 217ms -------------
2025-08-03 15:56:01:277 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-08-03 15:56:01:277 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-08-03 15:56:01:277 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:56:01:277 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-08-03 15:56:01:421 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 144ms -------------
2025-08-03 15:56:01:966 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-08-03 15:56:01:966 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-08-03 15:56:01:966 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:01:966 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-08-03 15:56:01:983 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:56:01:984 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:56:01:984 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:01:984 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:56:01:985 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-08"]
}
2025-08-03 15:56:02:118 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 152ms -------------
2025-08-03 15:56:02:449 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 466ms -------------
2025-08-03 15:56:05:129 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 15:56:05:129 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 15:56:05:129 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:05:129 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 15:56:05:129 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:56:05:305 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 176ms -------------
2025-08-03 15:56:07:517 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 15:56:07:517 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 15:56:07:518 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:07:518 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 15:56:07:518 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:56:07:768 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 251ms -------------
2025-08-03 15:56:13:673 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:56:13:674 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:56:13:674 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:56:13:674 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:56:13:674 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"name":["夜宵"]
}
2025-08-03 15:56:13:837 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 164ms -------------
2025-08-03 15:56:24:032 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求开始 -------------
2025-08-03 15:56:24:033 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/checkNameUnique
2025-08-03 15:56:24:033 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:56:24:033 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.checkNameUnique
2025-08-03 15:56:24:033 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"name":["夜宵"]
}
2025-08-03 15:56:24:170 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/checkNameUnique" 请求结束 => 耗时: 138ms -------------
2025-08-03 15:56:24:294 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/insert" 请求开始 -------------
2025-08-03 15:56:24:294 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/insert
2025-08-03 15:56:24:294 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:56:24:294 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.insert
2025-08-03 15:56:24:752 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=f11eb008-b27c-4386-a778-3ff6af5d968e, createTime=1754207784750, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryItemRestController.insert, args=[{"tenantId":1944596258485932033,"name":"夜宵","category":"补贴","dataType":"decimal","unit":"元","decimalPlaces":2,"calculationFormula":"","isSystemItem":false,"status":true,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增薪资项目数据, operationType=insert, runTime=404, returnValue={"msg":"新增薪资项目数据成功","code":0,"timestamp":1754207784747}, tenantId=1944596258485932033, logType=2)
2025-08-03 15:56:24:757 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/insert" 请求结束 => 耗时: 463ms -------------
2025-08-03 15:56:24:975 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 15:56:24:975 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 15:56:24:975 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:24:975 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 15:56:24:975 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:56:25:128 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 153ms -------------
2025-08-03 15:56:27:722 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 15:56:27:722 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 15:56:27:722 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:27:722 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 15:56:27:723 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["2"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:56:27:872 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 150ms -------------
2025-08-03 15:56:39:445 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 15:56:39:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 15:56:39:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:39:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 15:56:39:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 15:56:39:673 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 228ms -------------
2025-08-03 15:56:41:506 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 15:56:41:507 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 15:56:41:507 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 15:56:41:507 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 15:56:41:507 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 15:56:41:628 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 122ms -------------
2025-08-03 15:56:48:423 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 15:56:48:424 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 15:56:48:424 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 15:56:48:424 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 15:56:48:425 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951910380204666882"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 15:56:49:110 INFO  [http-nio-7001-exec-8] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=6de206d2-9d47-463c-b7eb-d6a7b25d666c, createTime=1754207809110, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951910380204666882,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"status":true,"version":0,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951915038172225537,"salaryItemName":"夜宵","salaryItemCategory":"补贴","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=661, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 15:56:49:112 WARN  [http-nio-7001-exec-8] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：批量保存模板项目失败: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1944596258485932033-2025-07-01-1951910380204666882-1951897765856' for key 'salary_template_items_monthly.uk_template_item'
### The error may exist in org/opsli/modulars/system/salary/mapper/SalaryTemplateItemMonthlyMapper.java (best guess)
### The error may involve org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO salary_template_items_monthly  ( id, tenant_id, data_month, template_id, salary_item_id, display_order, is_required, is_editable,   status, version, deleted, create_by, create_time, update_by, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?,   ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1944596258485932033-2025-07-01-1951910380204666882-1951897765856' for key 'salary_template_items_monthly.uk_template_item'
; Duplicate entry '1944596258485932033-2025-07-01-1951910380204666882-1951897765856' for key 'salary_template_items_monthly.uk_template_item'
2025-08-03 15:56:49:116 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 693ms -------------
2025-08-03 16:19:02:995 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 5409 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 16:19:02:996 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 16:19:04:514 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 16:19:04:514 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 16:19:04:558 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 16:19:06:191 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 16:19:06:192 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 16:19:06:192 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 16:19:06:790 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 16:19:08:198 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 16:19:08:225 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 16:19:08:346 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 16:19:09:371 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 16:19:09:391 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 16:19:09:391 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 16:19:11:134 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.474 seconds (process running for 8.988)
2025-08-03 16:19:24:518 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 16:19:25:013 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 16:19:25:013 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 16:19:25:013 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 16:19:25:013 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 16:19:25:014 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951910380204666882"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 16:19:26:063 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=8d443423-9cab-4d82-a060-15b26162f22f, createTime=1754209166061, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951910380204666882,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"status":true,"version":0,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951915038172225537,"salaryItemName":"夜宵","salaryItemCategory":"补贴","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=995, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 16:19:26:067 WARN  [http-nio-7001-exec-2] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：模板项目配置冲突，请检查是否有重复的薪资项目
2025-08-03 16:19:26:083 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 1070ms -------------
2025-08-03 16:19:31:741 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 16:19:31:741 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 16:19:31:741 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 16:19:31:741 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 16:19:31:742 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 16:19:31:861 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 120ms -------------
2025-08-03 16:19:33:946 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求开始 -------------
2025-08-03 16:19:33:946 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findEnabled
2025-08-03 16:19:33:946 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 16:19:33:946 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findEnabled
2025-08-03 16:19:33:946 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"]
}
2025-08-03 16:19:34:070 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findEnabled" 请求结束 => 耗时: 123ms -------------
2025-08-03 16:19:42:320 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 16:19:42:320 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 16:19:42:320 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 16:19:42:320 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 16:19:42:320 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951910380204666882"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 16:19:42:578 INFO  [http-nio-7001-exec-8] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=355414bb-fc6d-4998-8236-9cd5ef51bb0a, createTime=1754209182578, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951910380204666882,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"status":true,"version":0,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951915038172225537,"salaryItemName":"夜宵","salaryItemCategory":"补贴","displayOrder":2,"isRequired":true,"isEditable":true,"defaultValue":"","validationRules":"","status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=256, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-08-03 16:19:42:579 WARN  [http-nio-7001-exec-8] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：500 - 异常信息：模板项目配置冲突，请检查是否有重复的薪资项目
2025-08-03 16:19:42:580 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 260ms -------------
2025-08-03 16:30:50:323 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 6870 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-08-03 16:30:50:324 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-08-03 16:30:51:692 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-08-03 16:30:51:692 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-08-03 16:30:51:724 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-08-03 16:30:53:400 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-08-03 16:30:53:402 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-08-03 16:30:53:402 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-03 16:30:54:078 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-03 16:30:55:532 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-08-03 16:30:55:565 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-08-03 16:30:55:696 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-08-03 16:30:56:695 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-08-03 16:30:56:716 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-08-03 16:30:56:717 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-08-03 16:30:58:650 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.699 seconds (process running for 9.099)
2025-08-03 16:31:11:934 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 16:31:12:464 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求开始 -------------
2025-08-03 16:31:12:464 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/batchSave
2025-08-03 16:31:12:464 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-08-03 16:31:12:464 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.batchSave
2025-08-03 16:31:12:466 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"templateId":["1951910380204666882"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 16:31:12:752 INFO  [http-nio-7001-exec-2] o.o.m.s.s.s.i.SalaryTemplateItemMonthlyServiceImpl-? 物理删除模板项目成功, templateId: 1951910380204666882, tenantId: 1944596258485932033, dataMonth: Tue Jul 01 00:00:00 CST 2025, 删除数量: 1
2025-08-03 16:31:13:283 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=6c287832-7f8d-4b6a-8ead-415e3437bb69, createTime=1754209873281, level=0, moduleId=00, method=org.opsli.modulars.system.salary.web.SalaryTemplateItemMonthlyRestController.batchSave, args=[1951910380204666882,1944596258485932033,"2025-07",[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951897765856284674,"salaryItemName":"岗位工资","salaryItemCategory":"基本工资","displayOrder":1,"isRequired":true,"isEditable":true,"status":true,"version":0,"izApi":false,"izManual":false},{"tenantId":1944596258485932033,"dataMonth":1751299200000,"templateId":1951910380204666882,"salaryItemId":1951915038172225537,"salaryItemName":"夜宵","salaryItemCategory":"补贴","displayOrder":2,"isRequired":true,"isEditable":true,"status":true,"izApi":false,"izManual":false}]], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 批量保存模板项目配置, operationType=update, runTime=760, returnValue={"msg":"批量保存模板项目配置成功","code":0,"timestamp":1754209873275}, tenantId=1944596258485932033, logType=2)
2025-08-03 16:31:13:308 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/batchSave" 请求结束 => 耗时: 844ms -------------
2025-08-03 16:31:13:480 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求开始 -------------
2025-08-03 16:31:13:480 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/template/findPage
2025-08-03 16:31:13:480 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 16:31:13:480 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateMonthlyRestController.findPage
2025-08-03 16:31:13:480 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 16:31:13:785 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/template/findPage" 请求结束 => 耗时: 305ms -------------
2025-08-03 16:31:18:054 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求开始 -------------
2025-08-03 16:31:18:055 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/templateItem/findByTemplate
2025-08-03 16:31:18:055 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 16:31:18:055 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryTemplateItemMonthlyRestController.findByTemplate
2025-08-03 16:31:18:055 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"templateId":["1951910380204666882"]
}
2025-08-03 16:31:18:191 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/templateItem/findByTemplate" 请求结束 => 耗时: 137ms -------------
2025-08-03 16:31:40:717 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求开始 -------------
2025-08-03 16:31:40:718 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/item/findPage
2025-08-03 16:31:40:718 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 16:31:40:718 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: SalaryItemRestController.findPage
2025-08-03 16:31:40:719 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"category_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"]
}
2025-08-03 16:31:41:006 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/item/findPage" 请求结束 => 耗时: 289ms -------------
2025-08-03 16:31:45:060 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求开始 -------------
2025-08-03 16:31:45:060 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/salary/employee/findPage
2025-08-03 16:31:45:060 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-08-03 16:31:45:061 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: EmployeeSalaryMonthlyRestController.findPage
2025-08-03 16:31:45:061 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"employeeName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-08-03 16:31:45:073 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/salary/employee/findPage" 请求结束 => 耗时: 12ms -------------

2025-07-31 15:58:05:930 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 55636 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-31 15:58:05:932 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-31 15:58:07:325 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-31 15:58:07:325 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-31 15:58:07:368 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-31 15:58:10:942 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-31 15:58:10:943 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-31 15:58:10:944 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-31 15:58:11:462 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-31 15:58:12:913 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-31 15:58:12:936 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-31 15:58:13:050 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-31 15:58:14:126 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-31 15:58:14:148 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-31 15:58:14:149 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-31 15:58:15:886 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 10.304 seconds (process running for 10.951)
2025-07-31 16:00:19:724 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:00:28:831 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-07-31 16:00:28:831 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-07-31 16:00:28:831 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:00:28:831 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-07-31 16:00:28:835 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753948828791.1938"]
}
2025-07-31 16:00:28:902 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 71ms -------------
2025-07-31 16:00:28:917 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-31 16:00:28:918 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-31 16:00:28:918 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: OPTIONS
2025-07-31 16:00:28:918 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 1ms -------------
2025-07-31 16:00:28:926 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-31 16:00:28:926 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-31 16:00:28:926 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-31 16:00:28:926 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-07-31 16:00:29:801 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 875ms -------------
2025-07-31 16:00:29:922 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:00:29:922 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:00:29:922 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:00:29:922 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:00:30:178 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 256ms -------------
2025-07-31 16:00:30:287 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-31 16:00:30:287 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-31 16:00:30:287 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-31 16:00:30:287 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-31 16:00:30:351 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 64ms -------------
2025-07-31 16:00:30:657 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:00:30:657 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:00:30:657 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:00:30:657 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:00:30:810 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 153ms -------------
2025-07-31 16:00:36:923 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:00:36:923 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:00:36:923 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:00:36:924 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:00:37:076 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 153ms -------------
2025-07-31 16:00:39:326 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2403ms -------------
2025-07-31 16:01:02:238 INFO  [http-nio-7001-exec-10] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:01:05:099 INFO  [http-nio-7001-exec-10] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:01:07:024 INFO  [http-nio-7001-exec-10] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1947131263267311618, nodeName=创易家, nodeCode=cyj, parentId=null, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:02:05:124 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求结束 => 耗时: 88201ms -------------
2025-07-31 16:06:25:219 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:06:25:219 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:06:25:219 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:06:25:219 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:06:25:543 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 324ms -------------
2025-07-31 16:06:25:649 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-31 16:06:25:649 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-31 16:06:25:649 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-31 16:06:25:649 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-31 16:06:25:711 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 62ms -------------
2025-07-31 16:06:26:087 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:06:26:087 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:06:26:088 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:06:26:088 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:06:26:102 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:06:26:102 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:06:26:103 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:06:26:280 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 193ms -------------
2025-07-31 16:06:28:488 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2386ms -------------
2025-07-31 16:06:45:527 INFO  [http-nio-7001-exec-3] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:06:49:301 INFO  [http-nio-7001-exec-3] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:06:49:304 INFO  [http-nio-7001-exec-3] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1947131263267311618, nodeName=创易家, nodeCode=cyj, parentId=null, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:06:49:317 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求结束 => 耗时: 23215ms -------------
2025-07-31 16:09:05:497 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:09:05:499 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:09:05:500 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:09:05:500 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:09:05:675 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 178ms -------------
2025-07-31 16:09:05:790 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-31 16:09:05:790 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-31 16:09:05:790 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-31 16:09:05:790 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-31 16:09:05:935 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 145ms -------------
2025-07-31 16:09:06:236 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:09:06:236 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:09:06:236 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:09:06:236 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:09:06:246 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:09:06:246 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:09:06:246 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:09:06:246 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:09:06:246 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:09:06:247 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:09:06:247 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:09:06:247 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:09:06:247 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:09:06:247 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:09:06:541 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 305ms -------------
2025-07-31 16:09:08:849 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2603ms -------------
2025-07-31 16:12:46:741 INFO  [http-nio-7001-exec-5] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:12:47:325 INFO  [http-nio-7001-exec-5] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:12:47:980 INFO  [http-nio-7001-exec-5] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1947131263267311618, nodeName=创易家, nodeCode=cyj, parentId=null, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:12:47:996 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求结束 => 耗时: 221750ms -------------
2025-07-31 16:13:22:823 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:13:22:824 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:13:22:824 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:13:22:824 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:13:22:986 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 163ms -------------
2025-07-31 16:13:23:091 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-31 16:13:23:091 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-31 16:13:23:091 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-31 16:13:23:091 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-31 16:13:23:148 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 57ms -------------
2025-07-31 16:13:23:469 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:13:23:469 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:13:23:469 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:13:23:469 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:13:23:481 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:13:23:482 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:13:23:482 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:13:23:620 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 151ms -------------
2025-07-31 16:13:25:782 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2301ms -------------
2025-07-31 16:14:13:960 INFO  [http-nio-7001-exec-10] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:14:14:679 INFO  [http-nio-7001-exec-10] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:14:15:336 INFO  [http-nio-7001-exec-10] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1947131263267311618, nodeName=创易家, nodeCode=cyj, parentId=null, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null)]
2025-07-31 16:14:15:347 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求结束 => 耗时: 51866ms -------------
2025-07-31 16:14:49:140 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:14:49:141 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:14:49:141 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:14:49:141 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:14:49:141 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949689011.0146"],
	"typeCode":["common_status"]
}
2025-07-31 16:14:49:433 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 293ms -------------
2025-07-31 16:14:49:564 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:14:49:565 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:14:49:565 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:14:49:565 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:14:49:565 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949689458.0723"],
	"typeCode":["common_status"]
}
2025-07-31 16:14:49:716 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 152ms -------------
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:14:49:943 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.listAll
2025-07-31 16:14:49:944 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:14:49:944 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:14:50:182 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 239ms -------------
2025-07-31 16:14:50:202 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 259ms -------------
2025-07-31 16:15:00:629 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:15:00:629 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:15:00:629 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:00:629 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:15:00:629 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949700520.2466"],
	"typeCode":["common_status"]
}
2025-07-31 16:15:00:846 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 217ms -------------
2025-07-31 16:15:00:982 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:15:00:982 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:15:00:982 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:00:982 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:15:00:982 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949700862.795"],
	"typeCode":["common_status"]
}
2025-07-31 16:15:01:132 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 150ms -------------
2025-07-31 16:15:01:258 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/position/findPage" 请求开始 -------------
2025-07-31 16:15:01:259 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/position/findPage
2025-07-31 16:15:01:259 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:01:259 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: PositionRestController.findPage
2025-07-31 16:15:01:259 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"title_LIKE":[""],
	"positionCode_LIKE":[""],
	"departmentId_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:15:01:265 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-31 16:15:01:265 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-31 16:15:01:265 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:01:265 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.listAll
2025-07-31 16:15:01:265 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:15:01:492 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/position/findPage" 请求结束 => 耗时: 234ms -------------
2025-07-31 16:15:01:551 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 286ms -------------
2025-07-31 16:15:27:762 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:15:27:762 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:15:27:762 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:27:762 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:15:27:762 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949727554.363"],
	"typeCode":["employee_status"]
}
2025-07-31 16:15:27:909 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 147ms -------------
2025-07-31 16:15:28:025 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:15:28:025 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:15:28:025 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:28:025 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:15:28:025 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949727911.4949"],
	"typeCode":["gender"]
}
2025-07-31 16:15:28:247 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 222ms -------------
2025-07-31 16:15:28:375 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:15:28:376 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:15:28:376 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:28:376 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:15:28:376 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949728269.816"],
	"typeCode":["employee_status"]
}
2025-07-31 16:15:28:517 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 142ms -------------
2025-07-31 16:15:28:709 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:15:28:709 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:15:28:709 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:28:709 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:15:28:709 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949728519.5369"],
	"typeCode":["gender"]
}
2025-07-31 16:15:28:853 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 144ms -------------
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/position/listAll" 请求开始 -------------
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.listAll
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/employee/findPage" 请求开始 -------------
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/position/listAll
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/employee/findPage
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: PositionRestController.listAll
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: EmployeeRestController.findPage
2025-07-31 16:15:28:976 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"fullName_LIKE":[""],
	"employeeNumber_LIKE":[""],
	"departmentId_EQ":[""],
	"positionId_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:15:29:202 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/employee/findPage" 请求结束 => 耗时: 226ms -------------
2025-07-31 16:15:29:267 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/position/listAll" 请求结束 => 耗时: 291ms -------------
2025-07-31 16:15:29:273 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 297ms -------------
2025-07-31 16:16:04:712 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:16:04:712 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:16:04:713 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:16:04:713 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:16:04:713 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:16:04:714 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:16:04:714 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:16:04:714 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:16:04:721 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:16:04:721 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:16:07:454 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2742ms -------------
2025-07-31 16:16:25:441 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:16:25:445 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:16:25:445 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:16:25:445 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:16:25:446 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949785305.5288"],
	"typeCode":["common_status"]
}
2025-07-31 16:16:25:590 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 150ms -------------
2025-07-31 16:16:25:804 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-31 16:16:25:804 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-31 16:16:25:804 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:16:25:804 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-31 16:16:25:805 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1753949785610.8254"],
	"typeCode":["common_status"]
}
2025-07-31 16:16:25:952 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 149ms -------------
2025-07-31 16:16:26:067 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/position/findPage" 请求开始 -------------
2025-07-31 16:16:26:067 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/position/findPage
2025-07-31 16:16:26:067 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:16:26:067 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: PositionRestController.findPage
2025-07-31 16:16:26:067 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"title_LIKE":[""],
	"positionCode_LIKE":[""],
	"departmentId_EQ":[""],
	"status_EQ":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:16:26:070 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-31 16:16:26:070 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-31 16:16:26:070 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:16:26:070 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.listAll
2025-07-31 16:16:26:070 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:16:26:219 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/position/findPage" 请求结束 => 耗时: 152ms -------------
2025-07-31 16:16:26:366 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 296ms -------------
2025-07-31 16:20:19:645 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 58332 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-31 16:20:19:645 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-31 16:20:20:975 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-31 16:20:20:975 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-31 16:20:21:006 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-31 16:20:24:579 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-31 16:20:24:581 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-31 16:20:24:581 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-31 16:20:25:251 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-31 16:20:26:770 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-31 16:20:26:800 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-31 16:20:26:999 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-31 16:20:28:118 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-31 16:20:28:147 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-31 16:20:28:148 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-31 16:20:29:772 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 10.451 seconds (process running for 10.919)
2025-07-31 16:20:39:479 INFO  [http-nio-7001-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:20:40:087 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:20:40:089 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:20:40:089 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:20:42:843 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2756ms -------------
2025-07-31 16:22:33:459 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 58926 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-31 16:22:33:459 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-31 16:22:34:734 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-31 16:22:34:734 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-31 16:22:34:763 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-31 16:22:36:298 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-31 16:22:36:300 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-31 16:22:36:300 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-31 16:22:36:862 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-31 16:22:38:331 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-31 16:22:38:355 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-31 16:22:38:559 INFO  [redisson-netty-5-7] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-31 16:22:39:653 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-31 16:22:39:694 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-31 16:22:39:695 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-31 16:22:41:479 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 8.318 seconds (process running for 8.735)
2025-07-31 16:22:46:559 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 16:22:47:195 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:22:47:195 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:22:47:195 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:22:47:195 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:22:47:393 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 198ms -------------
2025-07-31 16:22:47:567 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-31 16:22:47:567 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-31 16:22:47:567 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-31 16:22:47:567 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-31 16:22:47:627 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 60ms -------------
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:22:48:015 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:22:48:016 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:22:48:018 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:22:48:018 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:22:48:160 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 145ms -------------
2025-07-31 16:22:50:697 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2682ms -------------
2025-07-31 16:23:13:431 INFO  [http-nio-7001-exec-1] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133439230283777, nodeName=测试工程师, nodeCode=csgxs, parentId=1946411883277549570, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1947133804944232450, nodeName=李测试, nodeCode=00004, parentId=1947133439230283777, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=李测试, employeeNumber=00004, gender=男, phoneNumber=, employeeStatus=在职, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=1, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null)]
2025-07-31 16:23:13:438 INFO  [http-nio-7001-exec-1] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133032957415425, nodeName=人事总监, nodeCode=rszj, parentId=1946393124441620482, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946442916177891329, nodeName=总经理, nodeCode=zjl, parentId=1946395541749039106, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946459202660278274, nodeName=里斯本, nodeCode=00002, parentId=1946442916177891329, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=里斯本, employeeNumber=00002, gender=女, phoneNumber=13444444444, employeeStatus=试用期, hireDate=2025-07-01, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=111, positionRequirements=222, positionLevel=1, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946443300187394049, nodeName=高级工程师, nodeCode=gjgcs, parentId=1946393203168706561, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946458807275823106, nodeName=张三, nodeCode=00001, parentId=1946443300187394049, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=张三, employeeNumber=00001, gender=男, phoneNumber=13700900900, employeeStatus=在职, hireDate=2025-07-02, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=ccc, positionRequirements=sss, positionLevel=3, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133439230283777, nodeName=测试工程师, nodeCode=csgxs, parentId=1946411883277549570, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1947133804944232450, nodeName=李测试, nodeCode=00004, parentId=1947133439230283777, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=李测试, employeeNumber=00004, gender=男, phoneNumber=, employeeStatus=在职, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=1, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=position, nodeId=1947133299773870082, nodeName=测试部长, nodeCode=csbz, parentId=1946413378718892033, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null)]
2025-07-31 16:23:13:439 INFO  [http-nio-7001-exec-1] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1947131263267311618, nodeName=创易家, nodeCode=cyj, parentId=null, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133032957415425, nodeName=人事总监, nodeCode=rszj, parentId=1946393124441620482, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946442916177891329, nodeName=总经理, nodeCode=zjl, parentId=1946395541749039106, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946459202660278274, nodeName=里斯本, nodeCode=00002, parentId=1946442916177891329, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=里斯本, employeeNumber=00002, gender=女, phoneNumber=13444444444, employeeStatus=试用期, hireDate=2025-07-01, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=111, positionRequirements=222, positionLevel=1, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946443300187394049, nodeName=高级工程师, nodeCode=gjgcs, parentId=1946393203168706561, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946458807275823106, nodeName=张三, nodeCode=00001, parentId=1946443300187394049, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=张三, employeeNumber=00001, gender=男, phoneNumber=13700900900, employeeStatus=在职, hireDate=2025-07-02, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=ccc, positionRequirements=sss, positionLevel=3, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133439230283777, nodeName=测试工程师, nodeCode=csgxs, parentId=1946411883277549570, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1947133804944232450, nodeName=李测试, nodeCode=00004, parentId=1947133439230283777, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=李测试, employeeNumber=00004, gender=男, phoneNumber=, employeeStatus=在职, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=1, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=position, nodeId=1947133299773870082, nodeName=测试部长, nodeCode=csbz, parentId=1946413378718892033, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=3, positionEmployeeCount=null)]
2025-07-31 16:23:13:444 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求结束 => 耗时: 25429ms -------------
2025-07-31 16:23:16:528 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:23:16:528 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:23:16:528 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:23:16:528 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:23:16:676 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 148ms -------------
2025-07-31 16:23:16:884 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-31 16:23:16:884 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-31 16:23:16:884 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-31 16:23:16:884 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-31 16:23:16:947 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 62ms -------------
2025-07-31 16:23:17:238 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-31 16:23:17:239 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-31 16:23:17:239 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:23:17:239 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求开始 -------------
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求开始 -------------
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationTree
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/organizationChart/getOrganizationStats
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationTree
2025-07-31 16:23:17:249 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: OrganizationChartRestController.getOrganizationStats
2025-07-31 16:23:17:250 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:23:17:250 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-31 16:23:17:485 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 247ms -------------
2025-07-31 16:23:19:730 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationStats" 请求结束 => 耗时: 2481ms -------------
2025-07-31 16:23:20:162 INFO  [http-nio-7001-exec-3] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133439230283777, nodeName=测试工程师, nodeCode=csgxs, parentId=1946411883277549570, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1947133804944232450, nodeName=李测试, nodeCode=00004, parentId=1947133439230283777, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=李测试, employeeNumber=00004, gender=男, phoneNumber=, employeeStatus=在职, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=1, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null)]
2025-07-31 16:23:20:163 INFO  [http-nio-7001-exec-3] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133032957415425, nodeName=人事总监, nodeCode=rszj, parentId=1946393124441620482, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946442916177891329, nodeName=总经理, nodeCode=zjl, parentId=1946395541749039106, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946459202660278274, nodeName=里斯本, nodeCode=00002, parentId=1946442916177891329, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=里斯本, employeeNumber=00002, gender=女, phoneNumber=13444444444, employeeStatus=试用期, hireDate=2025-07-01, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=111, positionRequirements=222, positionLevel=1, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946443300187394049, nodeName=高级工程师, nodeCode=gjgcs, parentId=1946393203168706561, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946458807275823106, nodeName=张三, nodeCode=00001, parentId=1946443300187394049, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=张三, employeeNumber=00001, gender=男, phoneNumber=13700900900, employeeStatus=在职, hireDate=2025-07-02, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=ccc, positionRequirements=sss, positionLevel=3, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133439230283777, nodeName=测试工程师, nodeCode=csgxs, parentId=1946411883277549570, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1947133804944232450, nodeName=李测试, nodeCode=00004, parentId=1947133439230283777, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=李测试, employeeNumber=00004, gender=男, phoneNumber=, employeeStatus=在职, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=1, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=position, nodeId=1947133299773870082, nodeName=测试部长, nodeCode=csbz, parentId=1946413378718892033, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null)]
2025-07-31 16:23:20:163 INFO  [http-nio-7001-exec-3] o.o.m.s.o.s.impl.OrganizationChartServiceImpl-? enrichTreeWithPositionsAndEmployees: [OrganizationChartModel(nodeType=department, nodeId=1947131263267311618, nodeName=创易家, nodeCode=cyj, parentId=null, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946393124441620482, nodeName=人事部, nodeCode=rsb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133032957415425, nodeName=人事总监, nodeCode=rszj, parentId=1946393124441620482, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395721932144641, nodeName=运营部, nodeCode=yyb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395600997777409, nodeName=产品部, nodeCode=cpb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395974328582145, nodeName=新业务部, nodeCode=xyeb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395803750432770, nodeName=客服部, nodeCode=kfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395655473397762, nodeName=设计部, nodeCode=sjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946396069799329794, nodeName=数据分析部, nodeCode=sjfx, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395541749039106, nodeName=总经办, nodeCode=zjb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946442916177891329, nodeName=总经理, nodeCode=zjl, parentId=1946395541749039106, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946459202660278274, nodeName=里斯本, nodeCode=00002, parentId=1946442916177891329, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=里斯本, employeeNumber=00002, gender=女, phoneNumber=13444444444, employeeStatus=试用期, hireDate=2025-07-01, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=111, positionRequirements=222, positionLevel=1, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946393203168706561, nodeName=研发部, nodeCode=yfb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1946443300187394049, nodeName=高级工程师, nodeCode=gjgcs, parentId=1946393203168706561, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1946458807275823106, nodeName=张三, nodeCode=00001, parentId=1946443300187394049, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=张三, employeeNumber=00001, gender=男, phoneNumber=13700900900, employeeStatus=在职, hireDate=2025-07-02, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=ccc, positionRequirements=sss, positionLevel=3, headcount=5, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946395892984250370, nodeName=市场部, nodeCode=scb, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946413378718892033, nodeName=测试部, nodeCode=cs, parentId=1947131263267311618, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=department, nodeId=1946396151407902722, nodeName=测试部啊, nodeCode=csb, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=软件测试, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=6, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946414800898330626, nodeName=测试2部, nodeCode=csb3, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=0, positionEmployeeCount=null), OrganizationChartModel(nodeType=department, nodeId=1946411883277549570, nodeName=测试1部, nodeCode=csb2, parentId=1946413378718892033, parentType=department, level=0, status=1, sort=0, children=[OrganizationChartModel(nodeType=position, nodeId=1947133439230283777, nodeName=测试工程师, nodeCode=csgxs, parentId=1946411883277549570, parentType=department, level=1, status=1, sort=0, children=[OrganizationChartModel(nodeType=employee, nodeId=1947133804944232450, nodeName=李测试, nodeCode=00004, parentId=1947133439230283777, parentType=position, level=2, status=1, sort=0, children=null, departmentDescription=null, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=null, employeeName=李测试, employeeNumber=00004, gender=男, phoneNumber=, employeeStatus=在职, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=null)], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=1, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=1)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=3, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null), OrganizationChartModel(nodeType=position, nodeId=1947133299773870082, nodeName=测试部长, nodeCode=csbz, parentId=1946413378718892033, parentType=department, level=1, status=1, sort=0, children=[], departmentDescription=null, positionDescription=, positionRequirements=, positionLevel=3, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=null, positionEmployeeCount=0)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=1, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=1, positionEmployeeCount=null)], departmentDescription=, positionDescription=null, positionRequirements=null, positionLevel=null, headcount=2, employeeName=null, employeeNumber=null, gender=null, phoneNumber=null, employeeStatus=null, hireDate=null, directSubordinates=null, totalSubordinates=null, departmentEmployeeCount=3, positionEmployeeCount=null)]
2025-07-31 16:23:20:166 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/organizationChart/getOrganizationTree" 请求结束 => 耗时: 2917ms -------------
2025-07-31 16:26:09:883 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 22.0.2 with PID 59588 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-31 16:26:09:884 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"

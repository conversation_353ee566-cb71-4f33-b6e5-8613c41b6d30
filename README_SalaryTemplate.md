# 工资表编制功能

## 功能概述

工资表编制功能是薪资管理系统的核心模块，用于创建和管理薪资模板，配置模板包含的薪资项目。

## 数据表关系

1. **`salary_items`** - 薪资项目基础定义表（基本工资、补贴、提成、奖金、扣除）
2. **`salary_templates_monthly`** - 薪资模板表（模板基本信息）
3. **`salary_template_items_monthly`** - 薪资模板项目关联表（模板包含哪些薪资项目）

**关系**：
- `salary_templates_monthly` (1) ←→ (N) `salary_template_items_monthly` 
- `salary_items` (1) ←→ (N) `salary_template_items_monthly`

## 功能模块

### 1. 薪资模板管理 (`index.vue`)

**主要功能**：
- 薪资模板列表展示
- 模板的增删改查
- 模板状态管理（启用/禁用）
- 设置默认模板
- 模板配置入口

**关键字段**：
- `name`: 模板名称
- `dataMonth`: 数据月份
- `scopeType`: 适用范围（全部/部门/岗位/员工）
- `scopeConfig`: 范围配置（JSON格式）
- `isDefault`: 是否默认模板
- `priority`: 优先级
- `status`: 状态

### 2. 薪资模板编辑 (`SalaryTemplateEdit.vue`)

**主要功能**：
- 新建薪资模板
- 编辑现有模板
- 表单验证（包括名称唯一性验证）
- 适用范围配置

**验证规则**：
- 模板名称必填且唯一
- 数据月份必选
- 优先级范围1-999

### 3. 薪资项目配置 (`SalaryTemplateConfig.vue`)

**主要功能**：
- 为模板添加薪资项目
- 配置项目属性（必填、可编辑、默认值等）
- 设置显示顺序
- 批量操作（启用/禁用/删除）
- 验证规则配置

**项目属性**：
- `displayOrder`: 显示顺序
- `isRequired`: 是否必填
- `isEditable`: 是否可编辑
- `defaultValue`: 默认值
- `validationRules`: 验证规则（JSON格式）
- `status`: 状态

## API接口

### 薪资模板接口 (`salaryTemplate.js`)
- `getList`: 获取模板分页数据
- `doInsert`: 新增模板
- `doUpdate`: 更新模板
- `doDelete`: 删除模板
- `setDefault`: 设置默认模板
- `checkNameUnique`: 检查名称唯一性

### 薪资项目接口 (`salaryItem.js`)
- `findEnabled`: 获取启用的薪资项目
- `findByCategory`: 按分类获取项目

### 模板项目关联接口 (`salaryTemplateItem.js`)
- `findByTemplate`: 获取模板的项目配置
- `batchSave`: 批量保存项目配置
- `batchUpdateStatus`: 批量更新状态
- `updateDisplayOrder`: 更新显示顺序

## 使用流程

1. **创建薪资模板**：
   - 点击"新建薪资模板"
   - 填写模板基本信息
   - 设置适用范围和优先级

2. **配置薪资项目**：
   - 在模板列表中点击"配置项目"
   - 添加需要的薪资项目
   - 设置项目属性和验证规则
   - 调整显示顺序

3. **模板管理**：
   - 启用/禁用模板
   - 设置默认模板
   - 编辑模板信息
   - 删除不需要的模板

## 路由配置

路径：`/hr/salaryTemplate`
组件：`@/views/modules/system/salaryTemplate/index.vue`

## 权限控制

- `system_salary_template_insert`: 新增模板权限
- `system_salary_template_update`: 更新模板权限
- `system_salary_template_delete`: 删除模板权限
- `system_salary_template_select`: 查看模板权限

## 注意事项

1. 模板名称在同一租户和数据月份下必须唯一
2. 每个租户和数据月份只能有一个默认模板
3. 删除模板前需要确保没有关联的员工薪资数据
4. 范围配置使用JSON格式，需要正确的格式验证
5. 验证规则支持最小值、最大值、必填和正则表达式验证
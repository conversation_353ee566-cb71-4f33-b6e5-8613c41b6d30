# OrgChart.js 插件参考文档

本文档根据 [OrgChart.js 官方 GitHub README](https://github.com/dabeng/OrgChart/blob/master/README.zh-cn.md) 整理，方便快速查阅。

## 1. 实例化

通过 jQuery 选择器在一个容器元素上初始化插件。

```javascript
var oc = $('#chart-container').orgchart(options);
```

## 2. 数据源 (data)

数据源是构造组织结构图的基础，可以是 JSON 对象或一个 `<ul>` 元素。

### JSON 数据源示例

```json
{
  "id": "rootNode",
  "name": "Lao Lao",
  "title": "general manager",
  "relationship": "111",
  "children": [
    { "name": "<PERSON> Miao", "title": "department manager", "relationship": "110" },
    { "name": "Su Miao", "title": "department manager", "relationship": "111" }
  ]
}
```

- `id`: (可选) 节点的唯一标识。
- `collapsed`: (可选, boolean) 初始化时是否折叠。
- `className`: (可选, string) 追加到节点的 CSS 类。
- `nodeTitle`: (可选, string) 关联到数据源的属性，用于节点标题。
- `nodeContent`: (可选, string) 关联到数据源的属性，用于节点内容。
- `relationship`: (可选, string) 长度为3的字符串（如`'110'`），分别表示是否存在父、兄弟、子节点，用于按需加载。

## 3. 配置选项 (Options)

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| `data` | json/jquery | | **必需**，组织结构图的数据源。 |
| `pan` | boolean | `false` | 允许鼠标拖拽平移图表。 |
| `zoom` | boolean | `false` | 允许鼠标滚轮缩放图表。 |
| `zoominLimit` | number | `7` | 放大上限。 |
| `zoomoutLimit` | number | `0.5` | 缩小下限。 |
| `direction` | string | `"t2b"` | 布局方向: `t2b`, `b2t`, `l2r`, `r2l`。 |
| `verticalLevel`| integer | | 从指定层级开始，节点垂直布局。 |
| `visibleLevel` | integer | `999` | 初始化时可见的展开层级。 |
| `toggleSiblingsResp` | boolean | `false` | 点击箭头时，只展开/折叠一侧的兄弟节点。 |
| `nodeTitle` | string | `"name"` | 节点标题对应的数据源属性名。 |
| `nodeContent` | string | | 节点内容对应的数据源属性名。 |
| `nodeId` | string | `"id"` | 节点ID对应的数据源属性名。 |
| `nodeTemplate` | function | | 自定义节点渲染模板的函数。 |
| `createNode` | function | | 节点创建完成后的回调函数，用于定制节点。 |
| `exportButton` | boolean | `false` | 是否显示导出按钮。 |
| `exportFilename` | string | `"Orgchart"`| 导出文件的名称。 |
| `draggable` | boolean | `false` | 是否允许拖拽节点来改变结构。 |
| `initCompleted` | function | | 图表初始化完成后的回调函数。 |

## 4. 公共方法 (Methods)

假设 `var oc = $('#chart-container').orgchart(options);`

- `oc.init(newOptions)`: 使用新选项或数据重新初始化图表。
- `oc.addParent(data)`: 添加父节点。
- `oc.addSiblings($node, data)`: 为指定节点添加兄弟节点。
- `oc.addChildren($node, data)`: 为指定节点添加子节点。
- `oc.removeNodes($node)`: 删除指定节点及其后代。
- `oc.getHierarchy()`: 获取整个图表的层级结构。
- `oc.hideParent($node)`: 隐藏指定节点的父节点。
- `oc.showParent($node)`: 显示指定节点的父节点。
- `oc.hideChildren($node)`: 隐藏指定节点的子节点。
- `oc.showChildren($node)`: 显示指定节点的子节点（仅下一级）。
- `oc.hideSiblings($node, direction)`: 隐藏兄弟节点。
- `oc.showSiblings($node, direction)`: 显示兄弟节点。
- `oc.getNodeState($node, relation)`: 获取关联节点（父、子、兄弟）的状态。
- `oc.getRelatedNodes($node, relation)`: 获取关联节点的 jQuery 对象。
- `oc.setChartScale($chart, newScale)`: 设置图表的缩放比例。
- `oc.export(filename, extension)`: 导出图表为图片或PDF。

## 5. 事件 (Events)

通过 jQuery 的 `.on()` 方法监听。

| 事件名 | 描述 |
| --- | --- |
| `nodedrop.orgchart` | 节点拖放完成时触发。 |
| `init.orgchart` | 图表初始化完成时触发。 |
| `show-[relation].orgchart` | 显示关联节点时触发（如 `show-children.orgchart`）。 |
| `hide-[relation].orgchart` | 隐藏关联节点时触发（如 `hide-children.orgchart`）。 |

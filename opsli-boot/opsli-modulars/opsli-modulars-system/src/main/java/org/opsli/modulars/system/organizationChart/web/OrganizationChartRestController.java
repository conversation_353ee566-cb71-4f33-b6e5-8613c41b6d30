/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.organizationChart.web;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.organizationChart.OrganizationChartApi;
import org.opsli.api.wrapper.system.organizationChart.OrganizationChartModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.modulars.system.organizationChart.service.IOrganizationChartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestParam;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 组织架构图 Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Tag(name = OrganizationChartApi.TITLE, description = OrganizationChartApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/organizationChart")
public class OrganizationChartRestController implements OrganizationChartApi {

    @Autowired
    private IOrganizationChartService organizationChartService;

    /**
     * 获取完整的组织架构树
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @return ResultWrapper
     */
    @Operation(summary = "获取完整的组织架构树")
    @PreAuthorize("hasAuthority('system_organization_chart_select')")
    @Override
    public ResultWrapper<List<OrganizationChartModel>> getOrganizationTree(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth) {
        
        try {
            Date date = parseDataMonth(dataMonth);
            List<OrganizationChartModel> tree = organizationChartService.getOrganizationTree(tenantId, date);
            return ResultWrapper.getSuccessResultWrapper(tree);
        } catch (Exception e) {
            log.error("获取组织架构树失败", e);
            return ResultWrapper.<List<OrganizationChartModel>>getErrorResultWrapper().setMsg("获取组织架构树失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定部门的组织架构子树
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @param departmentId 部门ID
     * @return ResultWrapper
     */
    @Operation(summary = "获取指定部门的组织架构子树")
    @PreAuthorize("hasAuthority('system_organization_chart_select')")
    @Override
    public ResultWrapper<List<OrganizationChartModel>> getDepartmentTree(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestParam("departmentId") Long departmentId) {
        
        try {
            Date date = parseDataMonth(dataMonth);
            List<OrganizationChartModel> tree = organizationChartService.getDepartmentTree(tenantId, date, departmentId);
            return ResultWrapper.getSuccessResultWrapper(tree);
        } catch (Exception e) {
            log.error("获取部门组织架构树失败", e);
            return ResultWrapper.<List<OrganizationChartModel>>getErrorResultWrapper().setMsg("获取部门组织架构树失败: " + e.getMessage());
        }
    }

    /**
     * 获取组织架构统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @return ResultWrapper
     */
    @Operation(summary = "获取组织架构统计信息")
    @PreAuthorize("hasAuthority('system_organization_chart_select')")
    @Override
    public ResultWrapper<OrganizationChartModel> getOrganizationStats(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth) {
        
        try {
            Date date = parseDataMonth(dataMonth);
            OrganizationChartModel stats = organizationChartService.getOrganizationStats(tenantId, date);
            return ResultWrapper.getSuccessResultWrapper(stats);
        } catch (Exception e) {
            log.error("获取组织架构统计信息失败", e);
            return ResultWrapper.<OrganizationChartModel>getErrorResultWrapper().setMsg("获取组织架构统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 搜索组织架构节点
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @param keyword 搜索关键词
     * @param nodeType 节点类型 (可选: department, position, employee)
     * @return ResultWrapper
     */
    @Operation(summary = "搜索组织架构节点")
    @PreAuthorize("hasAuthority('system_organization_chart_select')")
    @Override
    public ResultWrapper<List<OrganizationChartModel>> searchNodes(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "nodeType", required = false) String nodeType) {
        
        try {
            Date date = parseDataMonth(dataMonth);
            List<OrganizationChartModel> results = organizationChartService.searchNodes(tenantId, date, keyword, nodeType);
            return ResultWrapper.getSuccessResultWrapper(results);
        } catch (Exception e) {
            log.error("搜索组织架构节点失败", e);
            return ResultWrapper.<List<OrganizationChartModel>>getErrorResultWrapper().setMsg("搜索组织架构节点失败: " + e.getMessage());
        }
    }

    /**
     * 解析数据月份字符串为Date对象
     * @param dataMonth 数据月份字符串 (格式: yyyy-MM)
     * @return Date对象
     */
    private Date parseDataMonth(String dataMonth) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            return sdf.parse(dataMonth);
        } catch (Exception e) {
            log.error("解析数据月份失败: {}", dataMonth, e);
            throw new RuntimeException("数据月份格式错误，请使用 yyyy-MM 格式");
        }
    }
}

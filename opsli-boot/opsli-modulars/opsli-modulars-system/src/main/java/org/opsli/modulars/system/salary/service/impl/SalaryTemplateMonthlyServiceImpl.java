package org.opsli.modulars.system.salary.service.impl;

import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.modulars.system.salary.entity.SalaryTemplateMonthly;
import org.opsli.api.wrapper.system.salary.SalaryTemplateMonthlyModel;
import org.opsli.modulars.system.salary.mapper.SalaryTemplateMonthlyMapper;
import org.opsli.modulars.system.salary.service.ISalaryTemplateMonthlyService;
import org.opsli.common.utils.WrapperUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import cn.hutool.core.util.StrUtil;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class SalaryTemplateMonthlyServiceImpl extends CrudServiceImpl<SalaryTemplateMonthlyMapper, SalaryTemplateMonthly, SalaryTemplateMonthlyModel> implements ISalaryTemplateMonthlyService {

    @Autowired(required = false)
    private SalaryTemplateMonthlyMapper mapper;

    @Override
    public List<SalaryTemplateMonthlyModel> findByTenantIdAndDataMonth(Long tenantId, Date dataMonth) {
        if (tenantId == null || dataMonth == null) {
            return Collections.emptyList();
        }
        
        List<SalaryTemplateMonthly> entityList = mapper.findByTenantIdAndDataMonth(tenantId, dataMonth);
        return WrapperUtil.transformInstance(entityList, SalaryTemplateMonthlyModel.class);
    }

    @Override
    public SalaryTemplateMonthlyModel findDefaultByTenantIdAndDataMonth(Long tenantId, Date dataMonth) {
        if (tenantId == null || dataMonth == null) {
            return null;
        }
        
        SalaryTemplateMonthly entity = mapper.findDefaultByTenantIdAndDataMonth(tenantId, dataMonth);
        return WrapperUtil.transformInstance(entity, SalaryTemplateMonthlyModel.class);
    }

    @Override
    public List<SalaryTemplateMonthlyModel> findByTenantIdAndDataMonthAndScope(Long tenantId, Date dataMonth, String scopeType, Long scopeId) {
        // 由于已移除scope相关字段，直接调用基本查询方法
        return findByTenantIdAndDataMonth(tenantId, dataMonth);
    }



    @Override
    public boolean checkNameUnique(Long tenantId, Date dataMonth, String name, String excludeId) {
        if (tenantId == null || dataMonth == null || StrUtil.isBlank(name)) {
            return false;
        }
        
        int count = mapper.checkNameUnique(tenantId, dataMonth, name, excludeId);
        return count == 0;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalaryTemplateMonthlyModel copyTemplateToNewMonth(String sourceTemplateId, Date targetDataMonth, String newTemplateName) {
        if (StrUtil.isBlank(sourceTemplateId) || targetDataMonth == null || StrUtil.isBlank(newTemplateName)) {
            return null;
        }
        
        // 获取源模板
        SalaryTemplateMonthlyModel sourceTemplate = super.get(sourceTemplateId);
        if (sourceTemplate == null) {
            return null;
        }
        
        // 创建新模板
        SalaryTemplateMonthlyModel newTemplate = new SalaryTemplateMonthlyModel();
        newTemplate.setTenantId(sourceTemplate.getTenantId());
        newTemplate.setDataMonth(targetDataMonth);
        newTemplate.setName(newTemplateName);
        newTemplate.setStatus(sourceTemplate.getStatus());
        newTemplate.setDescription("从模板[" + sourceTemplate.getName() + "]复制而来");
        
        return super.insert(newTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, Boolean status) {
        if (ids == null || ids.isEmpty() || status == null) {
            return false;
        }
        
        UpdateWrapper<SalaryTemplateMonthly> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                    .set("status", status)
                    .set("update_time", new Date());
        
        return super.update(updateWrapper);
    }
}

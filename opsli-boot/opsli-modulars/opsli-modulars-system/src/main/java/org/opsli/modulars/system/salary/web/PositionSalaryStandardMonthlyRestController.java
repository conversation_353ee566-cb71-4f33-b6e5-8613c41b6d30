/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.web;

import cn.hutool.core.convert.Convert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.salary.PositionSalaryStandardMonthlyApi;
import org.opsli.api.wrapper.system.salary.PositionSalaryStandardMonthlyModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.QueryBuilder;
import org.opsli.core.persistence.querybuilder.GenQueryBuilder;
import org.opsli.modulars.system.salary.entity.PositionSalaryStandardMonthly;
import org.opsli.modulars.system.salary.service.IPositionSalaryStandardMonthlyService;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 岗位薪资标准月度数据表 Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Tag(name = PositionSalaryStandardMonthlyApi.TITLE, description = PositionSalaryStandardMonthlyApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/salary/positionStandard")
public class PositionSalaryStandardMonthlyRestController extends BaseRestController<PositionSalaryStandardMonthly, PositionSalaryStandardMonthlyModel, IPositionSalaryStandardMonthlyService>
        implements PositionSalaryStandardMonthlyApi {

    /**
     * 岗位薪资标准 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条岗位薪资标准数据")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<PositionSalaryStandardMonthlyModel> get(PositionSalaryStandardMonthlyModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 岗位薪资标准 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, PositionSalaryStandardMonthlyModel request) {
        QueryBuilder<PositionSalaryStandardMonthly> queryBuilder = new GenQueryBuilder<>();
        Page<PositionSalaryStandardMonthly, PositionSalaryStandardMonthlyModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryBuilder.build());
        page = IService.findPage(page);
        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 岗位薪资标准 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部岗位薪资标准数据")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findAll(PositionSalaryStandardMonthlyModel request) {
        QueryBuilder<PositionSalaryStandardMonthly> queryBuilder = new GenQueryBuilder<>();
        List<PositionSalaryStandardMonthly> entityList = IService.findList(queryBuilder.build());
        List<PositionSalaryStandardMonthlyModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 岗位薪资标准 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增岗位薪资标准数据")
    @PreAuthorize("hasAuthority('system_salary_position_insert')")
    @OperateLogger(description = "新增岗位薪资标准数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(PositionSalaryStandardMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增岗位薪资标准数据成功");
    }

    /**
     * 岗位薪资标准 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改岗位薪资标准数据")
    @PreAuthorize("hasAuthority('system_salary_position_update')")
    @OperateLogger(description = "修改岗位薪资标准数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(PositionSalaryStandardMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改岗位薪资标准数据成功");
    }

    /**
     * 岗位薪资标准 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除岗位薪资标准数据")
    @PreAuthorize("hasAuthority('system_salary_position_delete')")
    @OperateLogger(description = "删除岗位薪资标准数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除岗位薪资标准数据成功");
    }

    /**
     * 岗位薪资标准 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除岗位薪资标准数据")
    @PreAuthorize("hasAuthority('system_salary_position_delete')")
    @OperateLogger(description = "批量删除岗位薪资标准数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除岗位薪资标准数据成功");
    }

    /**
     * 根据租户ID、数据月份和岗位ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据岗位查询薪资标准")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findByPosition(Long tenantId, Date dataMonth, Long positionId) {
        List<PositionSalaryStandardMonthlyModel> modelList = IService.findByTenantIdAndDataMonthAndPositionId(tenantId, dataMonth, positionId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据租户ID、数据月份和模板ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据模板查询薪资标准")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findByTemplate(Long tenantId, Date dataMonth, Long templateId) {
        List<PositionSalaryStandardMonthlyModel> modelList = IService.findByTenantIdAndDataMonthAndTemplateId(tenantId, dataMonth, templateId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据岗位ID、模板ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "根据岗位和模板查询薪资标准")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findByPositionAndTemplate(Long positionId, Long templateId, Date dataMonth) {
        List<PositionSalaryStandardMonthlyModel> modelList = IService.findByPositionIdAndTemplateIdAndDataMonth(positionId, templateId, dataMonth);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据薪资项目ID查询关联的岗位标准
     * @param salaryItemId 薪资项目ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据薪资项目查询关联的岗位标准")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findBySalaryItem(Long salaryItemId) {
        List<PositionSalaryStandardMonthlyModel> modelList = IService.findBySalaryItemId(salaryItemId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据岗位ID、薪资项目ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "根据岗位和薪资项目查询薪资标准")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<PositionSalaryStandardMonthlyModel> findByPositionAndSalaryItem(Long positionId, Long salaryItemId, Date dataMonth) {
        PositionSalaryStandardMonthlyModel model = IService.findByPositionIdAndSalaryItemIdAndDataMonth(positionId, salaryItemId, dataMonth);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 检查岗位薪资标准是否已存在
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查岗位薪资标准是否已存在")
    @Override
    public ResultWrapper<Boolean> checkExists(PositionSalaryStandardMonthlyModel model) {
        boolean exists = IService.checkPositionStandardExists(model.getTenantId(), model.getDataMonth(), 
                model.getPositionId(), model.getTemplateId(), model.getSalaryItemId(), model.getId());
        return ResultWrapper.getSuccessResultWrapper(exists);
    }

    /**
     * 批量保存岗位薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param standards 薪资标准列表
     * @return ResultWrapper
     */
    @Operation(summary = "批量保存岗位薪资标准")
    @PreAuthorize("hasAuthority('system_salary_position_update')")
    @OperateLogger(description = "批量保存岗位薪资标准",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchSave(Long positionId, Long templateId, Long tenantId, Date dataMonth, List<PositionSalaryStandardMonthlyModel> standards) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.batchSavePositionStandards(positionId, templateId, tenantId, dataMonth, standards);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("批量保存岗位薪资标准成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "批量保存岗位薪资标准失败");
        }
    }

    /**
     * 删除岗位薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "删除岗位薪资标准")
    @PreAuthorize("hasAuthority('system_salary_position_delete')")
    @OperateLogger(description = "删除岗位薪资标准",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> deleteByPositionAndTemplate(Long positionId, Long templateId, Long tenantId, Date dataMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.deleteByPositionIdAndTemplateId(positionId, templateId, tenantId, dataMonth);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("删除岗位薪资标准成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "删除岗位薪资标准失败");
        }
    }

    /**
     * 批量启用/禁用自动预填充
     * @param ids ID列表
     * @param isAutoFill 是否自动预填充
     * @return ResultWrapper
     */
    @Operation(summary = "批量启用/禁用自动预填充")
    @PreAuthorize("hasAuthority('system_salary_position_update')")
    @OperateLogger(description = "批量启用/禁用自动预填充",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchUpdateAutoFill(String ids, Boolean isAutoFill) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        List<String> idList = Convert.toList(String.class, idArray);
        boolean success = IService.batchUpdateAutoFill(idList, isAutoFill);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("批量更新自动预填充状态成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "批量更新自动预填充状态失败");
        }
    }

    /**
     * 批量设置只读状态
     * @param ids ID列表
     * @param isReadonly 是否只读
     * @return ResultWrapper
     */
    @Operation(summary = "批量设置只读状态")
    @PreAuthorize("hasAuthority('system_salary_position_update')")
    @OperateLogger(description = "批量设置只读状态",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchUpdateReadonly(String ids, Boolean isReadonly) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        List<String> idList = Convert.toList(String.class, idArray);
        boolean success = IService.batchUpdateReadonly(idList, isReadonly);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("批量更新只读状态成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "批量更新只读状态失败");
        }
    }

    /**
     * 复制岗位薪资标准到新模板
     * @param sourcePositionId 源岗位ID
     * @param sourceTemplateId 源模板ID
     * @param targetPositionId 目标岗位ID
     * @param targetTemplateId 目标模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "复制岗位薪资标准到新模板")
    @PreAuthorize("hasAuthority('system_salary_position_insert')")
    @OperateLogger(description = "复制岗位薪资标准到新模板",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> copyToNewTemplate(Long sourcePositionId, Long sourceTemplateId, Long targetPositionId, Long targetTemplateId, Long tenantId, Date dataMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.copyPositionStandards(sourcePositionId, sourceTemplateId, targetPositionId, targetTemplateId, tenantId, dataMonth);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("复制岗位薪资标准成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "复制岗位薪资标准失败");
        }
    }

    /**
     * 复制岗位薪资标准到新月份
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param sourceMonth 源月份
     * @param targetMonth 目标月份
     * @return ResultWrapper
     */
    @Operation(summary = "复制岗位薪资标准到新月份")
    @PreAuthorize("hasAuthority('system_salary_position_insert')")
    @OperateLogger(description = "复制岗位薪资标准到新月份",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> copyToNewMonth(Long positionId, Long templateId, Long tenantId, Date sourceMonth, Date targetMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.copyToNewMonth(positionId, templateId, tenantId, sourceMonth, targetMonth);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("复制岗位薪资标准到新月份成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "复制岗位薪资标准到新月份失败");
        }
    }

    /**
     * 根据模板项目自动创建岗位标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "根据模板项目自动创建岗位标准")
    @PreAuthorize("hasAuthority('system_salary_position_insert')")
    @OperateLogger(description = "根据模板项目自动创建岗位标准",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> autoCreateFromTemplate(Long positionId, Long templateId, Long tenantId, Date dataMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.autoCreateFromTemplateItems(positionId, templateId, tenantId, dataMonth);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("自动创建岗位标准成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "自动创建岗位标准失败");
        }
    }

    /**
     * 批量导入岗位薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param standards 薪资标准列表
     * @return ResultWrapper
     */
    @Operation(summary = "批量导入岗位薪资标准")
    @PreAuthorize("hasAuthority('system_salary_position_insert')")
    @OperateLogger(description = "批量导入岗位薪资标准",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<Map<String, Object>> batchImport(Long tenantId, Date dataMonth, List<PositionSalaryStandardMonthlyModel> standards) {
        // 演示模式 不允许操作
        super.demoError();
        
        Map<String, Object> result = IService.batchImportStandards(tenantId, dataMonth, standards);
        return ResultWrapper.getSuccessResultWrapper(result);
    }

    /**
     * 验证标准值范围
     * @param standardValue 标准值
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return ResultWrapper
     */
    @Operation(summary = "验证标准值范围")
    @Override
    public ResultWrapper<Boolean> validateValueRange(String standardValue, String minValue, String maxValue) {
        boolean isValid = IService.validateValueRange(standardValue, minValue, maxValue);
        return ResultWrapper.getSuccessResultWrapper(isValid);
    }

    /**
     * 获取岗位薪资标准统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return ResultWrapper
     */
    @Operation(summary = "获取岗位薪资标准统计信息")
    @PreAuthorize("hasAuthority('system_salary_position_select')")
    @Override
    public ResultWrapper<Map<String, Object>> getStatistics(Long tenantId, Date dataMonth, Long positionId) {
        Map<String, Object> statistics = IService.getPositionStandardStatistics(tenantId, dataMonth, positionId);
        return ResultWrapper.getSuccessResultWrapper(statistics);
    }
}
package org.opsli.modulars.system.salary.service;

import org.opsli.api.wrapper.system.salary.EmployeeSalaryMonthlyModel;
import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.salary.entity.EmployeeSalaryMonthly;

import java.util.Date;
import java.util.List;

/**
 * 员工月度薪资汇总表 Service
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IEmployeeSalaryMonthlyService extends CrudServiceInterface<EmployeeSalaryMonthly, EmployeeSalaryMonthlyModel> {

    /**
     * 根据数据月份查询员工薪资
     * @param dataMonth 数据月份
     * @return List<EmployeeSalaryMonthlyModel>
     */
    List<EmployeeSalaryMonthlyModel> findByDataMonth(Date dataMonth);

    /**
     * 根据员工ID和数据月份查询薪资
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @return EmployeeSalaryMonthlyModel
     */
    EmployeeSalaryMonthlyModel findByEmployeeIdAndDataMonth(Long employeeId, Date dataMonth);

    /**
     * 根据租户ID和数据月份查询薪资列表
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return List<EmployeeSalaryMonthlyModel>
     */
    List<EmployeeSalaryMonthlyModel> findByTenantIdAndDataMonth(Long tenantId, Date dataMonth);

    /**
     * 批量更新薪资状态
     * @param ids ID数组
     * @param status 状态
     */
    void batchUpdateStatus(List<String> ids, String status);

    /**
     * 计算员工薪资
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     */
    void calculateSalary(Long employeeId, Date dataMonth);

    /**
     * 批量计算员工薪资
     * @param employeeIds 员工ID数组
     * @param dataMonth 数据月份
     */
    void batchCalculateSalary(List<Long> employeeIds, Date dataMonth);

    /**
     * 审批员工薪资
     * @param id 薪资ID
     */
    void approve(String id);

    /**
     * 批量审批员工薪资
     * @param ids ID数组
     */
    void batchApprove(List<String> ids);

    /**
     * 查询员工薪资历史记录
     * @param employeeId 员工ID
     * @param limit 限制数量
     * @return List<EmployeeSalaryMonthlyModel>
     */
    List<EmployeeSalaryMonthlyModel> findSalaryHistoryByEmployeeId(Long employeeId, Integer limit);
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.salary.entity.SalaryTemplateItemMonthly;

import java.util.Date;
import java.util.List;

/**
 * 薪资模板项目关联表 Mapper
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface SalaryTemplateItemMonthlyMapper extends BaseMapper<SalaryTemplateItemMonthly> {

    /**
     * 根据模板ID查询模板项目（按显示顺序排序）
     * @param templateId 模板ID
     * @return 模板项目列表
     */
    List<SalaryTemplateItemMonthly> findByTemplateIdOrderByDisplayOrder(@Param("templateId") Long templateId);

    /**
     * 根据租户ID、数据月份和模板ID查询模板项目
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return 模板项目列表
     */
    List<SalaryTemplateItemMonthly> findByTenantIdAndDataMonthAndTemplateId(@Param("tenantId") Long tenantId, 
                                                                            @Param("dataMonth") Date dataMonth,
                                                                            @Param("templateId") Long templateId);

    /**
     * 根据薪资项目ID查询关联的模板项目
     * @param salaryItemId 薪资项目ID
     * @return 模板项目列表
     */
    List<SalaryTemplateItemMonthly> findBySalaryItemId(@Param("salaryItemId") Long salaryItemId);

    /**
     * 批量删除模板项目
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 删除数量
     */
    int deleteByTemplateId(@Param("templateId") Long templateId, @Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);

    /**
     * 检查模板项目是否已存在
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @param salaryItemId 薪资项目ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 数量
     */
    int checkTemplateItemExists(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth,
                               @Param("templateId") Long templateId, @Param("salaryItemId") Long salaryItemId,
                               @Param("excludeId") String excludeId);

    /**
     * 根据租户ID、数据月份和模板ID查询启用的模板项目（按显示顺序排序）
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return 模板项目列表
     */
    List<SalaryTemplateItemMonthly> findEnabledByTenantIdAndDataMonthAndTemplateIdOrderByDisplayOrder(@Param("tenantId") Long tenantId, 
                                                                                                       @Param("dataMonth") Date dataMonth,
                                                                                                       @Param("templateId") Long templateId);
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.salary.entity.SalaryTemplateMonthly;

import java.util.Date;
import java.util.List;

/**
 * 薪资模板月度数据表 Mapper
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface SalaryTemplateMonthlyMapper extends BaseMapper<SalaryTemplateMonthly> {

    /**
     * 根据租户ID和数据月份查询薪资模板
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 薪资模板列表
     */
    List<SalaryTemplateMonthly> findByTenantIdAndDataMonth(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据租户ID和数据月份查询默认薪资模板
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 默认薪资模板
     */
    SalaryTemplateMonthly findDefaultByTenantIdAndDataMonth(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据租户ID、数据月份和适用范围查询薪资模板
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param scopeType 适用范围类型
     * @param scopeId 适用范围ID
     * @return 薪资模板列表
     */
    List<SalaryTemplateMonthly> findByTenantIdAndDataMonthAndScope(@Param("tenantId") Long tenantId, 
                                                                   @Param("dataMonth") Date dataMonth,
                                                                   @Param("scopeType") String scopeType,
                                                                   @Param("scopeId") Long scopeId);

    /**
     * 检查模板名称是否唯一
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param name 模板名称
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 数量
     */
    int checkNameUnique(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth, 
                       @Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 根据租户ID和数据月份查询启用的薪资模板（按优先级排序）
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 薪资模板列表
     */
    List<SalaryTemplateMonthly> findEnabledByTenantIdAndDataMonthOrderByPriority(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);
}

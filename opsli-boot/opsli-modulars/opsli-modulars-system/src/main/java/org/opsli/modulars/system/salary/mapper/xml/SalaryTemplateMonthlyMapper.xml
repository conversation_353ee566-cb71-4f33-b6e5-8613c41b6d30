<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.salary.mapper.SalaryTemplateMonthlyMapper">

    <!-- 根据租户ID和数据月份查询薪资模板 -->
    <select id="findByTenantIdAndDataMonth" resultType="org.opsli.modulars.system.salary.entity.SalaryTemplateMonthly">
        SELECT * FROM salary_templates_monthly 
        WHERE tenant_id = #{tenantId} 
          AND data_month = #{dataMonth} 
          AND deleted = 0
        ORDER BY name ASC
    </select>

    <!-- 根据租户ID和数据月份查询默认薪资模板 -->
    <select id="findDefaultByTenantIdAndDataMonth" resultType="org.opsli.modulars.system.salary.entity.SalaryTemplateMonthly">
        SELECT * FROM salary_templates_monthly 
        WHERE tenant_id = #{tenantId} 
          AND data_month = #{dataMonth} 
          AND deleted = 0
        ORDER BY name ASC
        LIMIT 1
    </select>

    <!-- 检查模板名称是否唯一 -->
    <select id="checkNameUnique" resultType="int">
        SELECT COUNT(1) FROM salary_templates_monthly 
        WHERE tenant_id = #{tenantId} 
          AND data_month = #{dataMonth} 
          AND name = #{name} 
          AND deleted = 0
        <if test="excludeId != null and excludeId != ''">
          AND id != #{excludeId}
        </if>
    </select>

</mapper>
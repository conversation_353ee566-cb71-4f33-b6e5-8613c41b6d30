/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.web;

import cn.hutool.core.convert.Convert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.salary.EmployeeSalaryMonthlyApi;
import org.opsli.api.wrapper.system.salary.EmployeeSalaryMonthlyModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.QueryBuilder;
import org.opsli.core.persistence.querybuilder.GenQueryBuilder;
import org.opsli.modulars.system.salary.entity.EmployeeSalaryMonthly;
import org.opsli.modulars.system.salary.service.IEmployeeSalaryMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 员工月度薪资汇总表 Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Tag(name = EmployeeSalaryMonthlyApi.TITLE, description = EmployeeSalaryMonthlyApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/salary/employee")
public class EmployeeSalaryMonthlyRestController extends BaseRestController<EmployeeSalaryMonthly, EmployeeSalaryMonthlyModel, IEmployeeSalaryMonthlyService>
        implements EmployeeSalaryMonthlyApi {



    /**
     * 从请求中获取租户ID
     * @return 租户ID
     */
    private Long getTenantIdFromRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return Convert.toLong(request.getParameter("tenantId"));
        }
        return null;
    }

    /**
     * 员工薪资 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条员工薪资数据")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<EmployeeSalaryMonthlyModel> get(EmployeeSalaryMonthlyModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 员工薪资 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, EmployeeSalaryMonthlyModel request) {
        QueryBuilder<EmployeeSalaryMonthly> queryBuilder = new GenQueryBuilder<>();
        Page<EmployeeSalaryMonthly, EmployeeSalaryMonthlyModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryBuilder.build());
        page = IService.findPage(page);
        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 员工薪资 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部员工薪资数据")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryMonthlyModel>> findAll(EmployeeSalaryMonthlyModel request) {
        QueryBuilder<EmployeeSalaryMonthly> queryBuilder = new GenQueryBuilder<>();
        List<EmployeeSalaryMonthly> entityList = IService.findList(queryBuilder.build());
        List<EmployeeSalaryMonthlyModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 员工薪资 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增员工薪资数据")
    @PreAuthorize("hasAuthority('system_salary_employee_insert')")
    @OperateLogger(description = "新增员工薪资数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(EmployeeSalaryMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增员工薪资数据成功");
    }

    /**
     * 员工薪资 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改员工薪资数据")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "修改员工薪资数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(EmployeeSalaryMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改员工薪资数据成功");
    }

    /**
     * 员工薪资 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除员工薪资数据")
    @PreAuthorize("hasAuthority('system_salary_employee_delete')")
    @OperateLogger(description = "删除员工薪资数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除员工薪资数据成功");
    }

    /**
     * 员工薪资 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除员工薪资数据")
    @PreAuthorize("hasAuthority('system_salary_employee_delete')")
    @OperateLogger(description = "批量删除员工薪资数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除员工薪资数据成功");
    }

    /**
     * 根据数据月份查询员工薪资
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "根据数据月份查询员工薪资")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryMonthlyModel>> findByDataMonth(Date dataMonth) {
        // 从请求参数中获取租户ID
        Long tenantId = getTenantIdFromRequest();
        if (tenantId == null) {
            return ResultWrapper.getSuccessResultWrapper(java.util.Collections.emptyList());
        }
        List<EmployeeSalaryMonthlyModel> modelList = IService.findByTenantIdAndDataMonth(tenantId, dataMonth);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据员工ID查询薪资
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "根据员工ID查询薪资")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<EmployeeSalaryMonthlyModel> findByEmployee(Long employeeId, Date dataMonth) {
        EmployeeSalaryMonthlyModel model = IService.findByEmployeeIdAndDataMonth(employeeId, dataMonth);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 批量更新薪资状态
     * @param ids ID数组
     * @param status 状态
     * @return ResultWrapper
     */
    @Operation(summary = "批量更新薪资状态")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "批量更新薪资状态",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchUpdateStatus(String ids, String status) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        List<String> idList = Convert.toList(String.class, idArray);
        IService.batchUpdateStatus(idList, status);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量更新薪资状态成功");
    }

    /**
     * 计算员工薪资
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "计算员工薪资")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "计算员工薪资",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> calculateSalary(Long employeeId, Date dataMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.calculateSalary(employeeId, dataMonth);
        return ResultWrapper.getSuccessResultWrapperByMsg("计算员工薪资成功");
    }

    /**
     * 批量计算员工薪资
     * @param employeeIds 员工ID数组
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "批量计算员工薪资")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "批量计算员工薪资",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchCalculateSalary(String employeeIds, Date dataMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(employeeIds);
        List<Long> employeeIdList = Convert.toList(Long.class, idArray);
        IService.batchCalculateSalary(employeeIdList, dataMonth);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量计算员工薪资成功");
    }

    /**
     * 审批员工薪资
     * @param id 薪资ID
     * @return ResultWrapper
     */
    @Operation(summary = "审批员工薪资")
    @PreAuthorize("hasAuthority('system_salary_employee_approve')")
    @OperateLogger(description = "审批员工薪资",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> approve(String id) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.approve(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("审批员工薪资成功");
    }

    /**
     * 批量审批员工薪资
     * @param ids ID数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量审批员工薪资")
    @PreAuthorize("hasAuthority('system_salary_employee_approve')")
    @OperateLogger(description = "批量审批员工薪资",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchApprove(String ids) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        List<String> idList = Convert.toList(String.class, idArray);
        IService.batchApprove(idList);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量审批员工薪资成功");
    }

    /**
     * 查询员工薪资历史记录
     * @param employeeId 员工ID
     * @param limit 限制数量
     * @return ResultWrapper
     */
    @Operation(summary = "查询员工薪资历史记录")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryMonthlyModel>> findSalaryHistory(Long employeeId, Integer limit) {
        List<EmployeeSalaryMonthlyModel> modelList = IService.findSalaryHistoryByEmployeeId(employeeId, limit);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }
}

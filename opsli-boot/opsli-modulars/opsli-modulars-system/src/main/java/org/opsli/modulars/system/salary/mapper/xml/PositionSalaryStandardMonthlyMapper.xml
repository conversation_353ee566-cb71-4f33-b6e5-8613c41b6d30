<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.salary.mapper.PositionSalaryStandardMonthlyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.opsli.modulars.system.salary.entity.PositionSalaryStandardMonthly">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="data_month" property="dataMonth" />
        <result column="position_id" property="positionId" />
        <result column="template_id" property="templateId" />
        <result column="salary_item_id" property="salaryItemId" />
        <result column="standard_value" property="standardValue" />
        <result column="min_value" property="minValue" />
        <result column="max_value" property="maxValue" />
        <result column="is_auto_fill" property="isAutoFill" />
        <result column="is_readonly" property="isReadonly" />
        <result column="description" property="description" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 带关联信息的查询映射结果 -->
    <resultMap id="DetailResultMap" type="org.opsli.modulars.system.salary.entity.PositionSalaryStandardMonthly" extends="BaseResultMap">
        <result column="position_name" property="positionName" />
        <result column="template_name" property="templateName" />
        <result column="salary_item_name" property="salaryItemName" />
        <result column="salary_item_category" property="salaryItemCategory" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pss.id, pss.tenant_id, pss.data_month, pss.position_id, pss.template_id, pss.salary_item_id, 
        pss.standard_value, pss.min_value, pss.max_value, pss.is_auto_fill, pss.is_readonly, 
        pss.description, pss.version, pss.deleted, pss.create_by, pss.create_time, pss.update_by, pss.update_time
    </sql>

    <!-- 带关联信息的查询结果列 -->
    <sql id="Detail_Column_List">
        <include refid="Base_Column_List" />,
        p.name as position_name, st.name as template_name,
        si.name as salary_item_name, si.category as salary_item_category
    </sql>

    <!-- 根据岗位ID、模板ID和数据月份查询薪资标准 -->
    <select id="findByPositionIdAndTemplateIdAndDataMonth" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM position_salary_standards_monthly pss
        LEFT JOIN positions p ON pss.position_id = p.id AND p.deleted = 0
        LEFT JOIN salary_templates_monthly st ON pss.template_id = st.id AND st.deleted = 0
        LEFT JOIN salary_items si ON pss.salary_item_id = si.id AND si.deleted = 0
        WHERE pss.position_id = #{positionId} 
        AND pss.template_id = #{templateId} 
        AND pss.data_month = #{dataMonth} 
        AND pss.deleted = 0
        ORDER BY si.category, si.create_time ASC
    </select>

    <!-- 根据租户ID、数据月份和岗位ID查询薪资标准 -->
    <select id="findByTenantIdAndDataMonthAndPositionId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM position_salary_standards_monthly pss
        LEFT JOIN positions p ON pss.position_id = p.id AND p.deleted = 0
        LEFT JOIN salary_templates_monthly st ON pss.template_id = st.id AND st.deleted = 0
        LEFT JOIN salary_items si ON pss.salary_item_id = si.id AND si.deleted = 0
        WHERE pss.tenant_id = #{tenantId} 
        AND pss.data_month = #{dataMonth} 
        AND pss.position_id = #{positionId} 
        AND pss.deleted = 0
        ORDER BY st.priority ASC, si.category, si.create_time ASC
    </select>

    <!-- 根据租户ID、数据月份和模板ID查询薪资标准 -->
    <select id="findByTenantIdAndDataMonthAndTemplateId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM position_salary_standards_monthly pss
        LEFT JOIN positions p ON pss.position_id = p.id AND p.deleted = 0
        LEFT JOIN salary_templates_monthly st ON pss.template_id = st.id AND st.deleted = 0
        LEFT JOIN salary_items si ON pss.salary_item_id = si.id AND si.deleted = 0
        WHERE pss.tenant_id = #{tenantId} 
        AND pss.data_month = #{dataMonth} 
        AND pss.template_id = #{templateId} 
        AND pss.deleted = 0
        ORDER BY p.name ASC, si.category, si.create_time ASC
    </select>

    <!-- 根据薪资项目ID查询关联的岗位标准 -->
    <select id="findBySalaryItemId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM position_salary_standards_monthly pss
        LEFT JOIN positions p ON pss.position_id = p.id AND p.deleted = 0
        LEFT JOIN salary_templates_monthly st ON pss.template_id = st.id AND st.deleted = 0
        LEFT JOIN salary_items si ON pss.salary_item_id = si.id AND si.deleted = 0
        WHERE pss.salary_item_id = #{salaryItemId} 
        AND pss.deleted = 0
        ORDER BY pss.data_month DESC, p.name ASC
    </select>

    <!-- 根据岗位ID、薪资项目ID和数据月份查询薪资标准 -->
    <select id="findByPositionIdAndSalaryItemIdAndDataMonth" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM position_salary_standards_monthly pss
        LEFT JOIN positions p ON pss.position_id = p.id AND p.deleted = 0
        LEFT JOIN salary_templates_monthly st ON pss.template_id = st.id AND st.deleted = 0
        LEFT JOIN salary_items si ON pss.salary_item_id = si.id AND si.deleted = 0
        WHERE pss.position_id = #{positionId} 
        AND pss.salary_item_id = #{salaryItemId} 
        AND pss.data_month = #{dataMonth} 
        AND pss.deleted = 0
    </select>

    <!-- 检查岗位薪资标准是否已存在 -->
    <select id="checkPositionStandardExists" resultType="int">
        SELECT COUNT(1)
        FROM position_salary_standards_monthly
        WHERE tenant_id = #{tenantId} 
        AND data_month = #{dataMonth}
        AND position_id = #{positionId} 
        AND template_id = #{templateId} 
        AND salary_item_id = #{salaryItemId} 
        AND deleted = 0
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量删除岗位薪资标准 -->
    <update id="deleteByPositionIdAndTemplateId">
        UPDATE position_salary_standards_monthly 
        SET deleted = 1, update_time = NOW()
        WHERE position_id = #{positionId} 
        AND template_id = #{templateId} 
        AND tenant_id = #{tenantId} 
        AND data_month = #{dataMonth}
        AND deleted = 0
    </update>

    <!-- 获取岗位薪资标准统计信息 -->
    <select id="getPositionStandardStatistics" resultType="map">
        SELECT 
            COUNT(1) as total_standards,
            COUNT(CASE WHEN is_auto_fill = 1 THEN 1 END) as auto_fill_standards,
            COUNT(CASE WHEN is_readonly = 1 THEN 1 END) as readonly_standards,
            COUNT(CASE WHEN standard_value IS NOT NULL AND standard_value != '' THEN 1 END) as has_standard_value,
            COUNT(CASE WHEN min_value IS NOT NULL AND min_value != '' THEN 1 END) as has_min_value,
            COUNT(CASE WHEN max_value IS NOT NULL AND max_value != '' THEN 1 END) as has_max_value
        FROM position_salary_standards_monthly
        WHERE tenant_id = #{tenantId} 
        AND data_month = #{dataMonth} 
        AND position_id = #{positionId} 
        AND deleted = 0
    </select>

</mapper>
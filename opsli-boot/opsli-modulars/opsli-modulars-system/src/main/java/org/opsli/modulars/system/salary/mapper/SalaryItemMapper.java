/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.salary.entity.SalaryItem;

import java.util.List;

/**
 * 薪资项目基础定义表 Mapper
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface SalaryItemMapper extends BaseMapper<SalaryItem> {

    /**
     * 根据租户ID和分类查询薪资项目
     * @param tenantId 租户ID
     * @param category 分类
     * @return 薪资项目列表
     */
    List<SalaryItem> findByTenantIdAndCategory(@Param("tenantId") Long tenantId, @Param("category") String category);

    /**
     * 根据租户ID查询启用的薪资项目
     * @param tenantId 租户ID
     * @return 薪资项目列表
     */
    List<SalaryItem> findEnabledByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 检查薪资项目名称是否唯一
     * @param tenantId 租户ID
     * @param name 薪资项目名称
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 数量
     */
    int checkNameUnique(@Param("tenantId") Long tenantId, @Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 根据租户ID和数据类型查询薪资项目
     * @param tenantId 租户ID
     * @param dataType 数据类型
     * @return 薪资项目列表
     */
    List<SalaryItem> findByTenantIdAndDataType(@Param("tenantId") Long tenantId, @Param("dataType") String dataType);
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.position.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.position.entity.PositionMonthly;

import java.util.Date;
import java.util.List;

/**
 * 职位管理 - 按月份 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Mapper
public interface PositionMapper extends BaseMapper<PositionMonthly> {

    /**
     * 根据部门ID查询职位列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 职位列表
     */
    List<PositionMonthly> findByDepartmentId(@Param("tenantId") Long tenantId, 
                                           @Param("dataMonth") Date dataMonth, 
                                           @Param("departmentId") Long departmentId);

    /**
     * 根据租户ID和月份查询岗位列表
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return List
     */
    List<PositionMonthly> findByTenantAndMonth(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);

    /**
     * 检查职位编码是否唯一（在同一租户和月份下）
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionCode 职位编码
     * @param id 排除的职位ID（更新时使用）
     * @return 存在的数量
     */
    int checkCodeUnique(@Param("tenantId") Long tenantId, 
                       @Param("dataMonth") Date dataMonth, 
                       @Param("positionCode") String positionCode, 
                       @Param("id") Long id);

    /**
     * 检查职位名称是否唯一（在同一租户和月份下）
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param title 职位名称
     * @param id 排除的职位ID（更新时使用）
     * @return 存在的数量
     */
    int checkTitleUnique(@Param("tenantId") Long tenantId, 
                        @Param("dataMonth") Date dataMonth, 
                        @Param("title") String title, 
                        @Param("id") Long id);
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.entity;

import java.util.Date;

import org.opsli.core.base.entity.BaseEntity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 薪资模板项目关联表
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("salary_template_items_monthly")
public class SalaryTemplateItemMonthly extends BaseEntity {

    /** 租户ID */
    private Long tenantId;
    
    /** 数据月份 */
    private Date dataMonth;
    
    /** 薪资模板ID */
    private Long templateId;
    
    /** 薪资项目ID */
    private Long salaryItemId;
    
    /** 显示顺序 */
    private Integer displayOrder;
    
    /** 是否必填 */
    private Boolean isRequired;
    
    /** 是否可编辑 */
    private Boolean isEditable;
    
    /** 默认值 */
    private String defaultValue;
    
    /** 验证规则 (如: {"min": 0, "max": 50000, "required": true}) */
    private String validationRules;
    
    /** 状态 */
    private Boolean status;
    
    /** 版本号 */
    @Version
    private Integer version;
    
    /** 逻辑删除标识：0-未删除，1-已删除 */
    @TableLogic
    private Integer deleted;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新人 */
    private String updateBy;
    
    /** 更新时间 */
    private Date updateTime;
    
    // ========== 关联查询字段 ==========
    
    /** 薪资项目名称 (关联查询字段) */
    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String salaryItemName;
    
    /** 薪资项目分类 (关联查询字段) */
    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String salaryItemCategory;
}

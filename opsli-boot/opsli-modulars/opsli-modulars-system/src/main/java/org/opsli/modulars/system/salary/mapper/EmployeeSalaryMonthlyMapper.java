/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.salary.entity.EmployeeSalaryMonthly;

import java.util.Date;
import java.util.List;

/**
 * 员工月度薪资汇总表 Mapper
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface EmployeeSalaryMonthlyMapper extends BaseMapper<EmployeeSalaryMonthly> {

    /**
     * 根据租户ID和数据月份查询员工薪资
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 员工薪资列表
     */
    List<EmployeeSalaryMonthly> findByTenantIdAndDataMonth(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据员工ID和数据月份查询员工薪资
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @return 员工薪资
     */
    EmployeeSalaryMonthly findByEmployeeIdAndDataMonth(@Param("employeeId") Long employeeId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据租户ID、数据月份和状态查询员工薪资
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param status 状态
     * @return 员工薪资列表
     */
    List<EmployeeSalaryMonthly> findByTenantIdAndDataMonthAndStatus(@Param("tenantId") Long tenantId, 
                                                                   @Param("dataMonth") Date dataMonth,
                                                                   @Param("status") String status);

    /**
     * 根据模板ID查询使用该模板的员工薪资
     * @param templateId 模板ID
     * @return 员工薪资列表
     */
    List<EmployeeSalaryMonthly> findByTemplateId(@Param("templateId") Long templateId);

    /**
     * 检查员工薪资是否已存在
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 数量
     */
    int checkEmployeeSalaryExists(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth,
                                 @Param("employeeId") Long employeeId, @Param("excludeId") String excludeId);

    /**
     * 根据租户ID、数据月份和员工ID列表查询员工薪资
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeIds 员工ID列表
     * @return 员工薪资列表
     */
    List<EmployeeSalaryMonthly> findByTenantIdAndDataMonthAndEmployeeIds(@Param("tenantId") Long tenantId, 
                                                                        @Param("dataMonth") Date dataMonth,
                                                                        @Param("employeeIds") List<Long> employeeIds);

    /**
     * 批量更新员工薪资状态
     * @param ids ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status, @Param("updateBy") String updateBy);

    /**
     * 根据员工ID查询薪资历史记录
     * @param employeeId 员工ID
     * @param limit 限制数量
     * @return 员工薪资列表
     */
    List<EmployeeSalaryMonthly> findSalaryHistoryByEmployeeId(@Param("employeeId") Long employeeId, @Param("limit") Integer limit);
}

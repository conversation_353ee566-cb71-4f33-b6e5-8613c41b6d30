/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service;

import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.salary.entity.PositionSalaryStandardMonthly;
import org.opsli.api.wrapper.system.salary.PositionSalaryStandardMonthlyModel;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 岗位薪资标准月度数据表 Service接口
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IPositionSalaryStandardMonthlyService extends CrudServiceInterface<PositionSalaryStandardMonthly, PositionSalaryStandardMonthlyModel> {

    /**
     * 根据租户ID、数据月份和岗位ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return 薪资标准列表
     */
    List<PositionSalaryStandardMonthlyModel> findByTenantIdAndDataMonthAndPositionId(Long tenantId, Date dataMonth, Long positionId);

    /**
     * 根据租户ID、数据月份和模板ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return 薪资标准列表
     */
    List<PositionSalaryStandardMonthlyModel> findByTenantIdAndDataMonthAndTemplateId(Long tenantId, Date dataMonth, Long templateId);

    /**
     * 根据岗位ID、模板ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param dataMonth 数据月份
     * @return 薪资标准列表
     */
    List<PositionSalaryStandardMonthlyModel> findByPositionIdAndTemplateIdAndDataMonth(Long positionId, Long templateId, Date dataMonth);

    /**
     * 根据薪资项目ID查询关联的岗位标准
     * @param salaryItemId 薪资项目ID
     * @return 岗位标准列表
     */
    List<PositionSalaryStandardMonthlyModel> findBySalaryItemId(Long salaryItemId);

    /**
     * 根据岗位ID、薪资项目ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return 薪资标准
     */
    PositionSalaryStandardMonthlyModel findByPositionIdAndSalaryItemIdAndDataMonth(Long positionId, Long salaryItemId, Date dataMonth);

    /**
     * 检查岗位薪资标准是否已存在
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param salaryItemId 薪资项目ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否已存在
     */
    boolean checkPositionStandardExists(Long tenantId, Date dataMonth, Long positionId, Long templateId, Long salaryItemId, String excludeId);

    /**
     * 批量保存岗位薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param standards 薪资标准列表
     * @return 是否成功
     */
    boolean batchSavePositionStandards(Long positionId, Long templateId, Long tenantId, Date dataMonth, List<PositionSalaryStandardMonthlyModel> standards);

    /**
     * 删除岗位薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 是否成功
     */
    boolean deleteByPositionIdAndTemplateId(Long positionId, Long templateId, Long tenantId, Date dataMonth);

    /**
     * 批量启用/禁用自动预填充
     * @param ids ID列表
     * @param isAutoFill 是否自动预填充
     * @return 是否成功
     */
    boolean batchUpdateAutoFill(List<String> ids, Boolean isAutoFill);

    /**
     * 批量设置只读状态
     * @param ids ID列表
     * @param isReadonly 是否只读
     * @return 是否成功
     */
    boolean batchUpdateReadonly(List<String> ids, Boolean isReadonly);

    /**
     * 复制岗位薪资标准到新模板
     * @param sourcePositionId 源岗位ID
     * @param sourceTemplateId 源模板ID
     * @param targetPositionId 目标岗位ID
     * @param targetTemplateId 目标模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 是否成功
     */
    boolean copyPositionStandards(Long sourcePositionId, Long sourceTemplateId, Long targetPositionId, Long targetTemplateId, Long tenantId, Date dataMonth);

    /**
     * 复制岗位薪资标准到新月份
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param sourceMonth 源月份
     * @param targetMonth 目标月份
     * @return 是否成功
     */
    boolean copyToNewMonth(Long positionId, Long templateId, Long tenantId, Date sourceMonth, Date targetMonth);

    /**
     * 根据模板项目自动创建岗位标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 是否成功
     */
    boolean autoCreateFromTemplateItems(Long positionId, Long templateId, Long tenantId, Date dataMonth);

    /**
     * 批量导入岗位薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param standards 薪资标准列表
     * @return 导入结果
     */
    Map<String, Object> batchImportStandards(Long tenantId, Date dataMonth, List<PositionSalaryStandardMonthlyModel> standards);

    /**
     * 验证标准值范围
     * @param standardValue 标准值
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 是否有效
     */
    boolean validateValueRange(String standardValue, String minValue, String maxValue);

    /**
     * 获取岗位薪资标准统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return 统计信息
     */
    Map<String, Object> getPositionStandardStatistics(Long tenantId, Date dataMonth, Long positionId);
}
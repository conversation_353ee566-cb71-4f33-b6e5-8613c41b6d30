/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.web;

import cn.hutool.core.convert.Convert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.salary.SalaryItemApi;
import org.opsli.api.wrapper.system.salary.SalaryItemModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.QueryBuilder;
import org.opsli.core.persistence.querybuilder.GenQueryBuilder;
import org.opsli.modulars.system.salary.entity.SalaryItem;
import org.opsli.modulars.system.salary.service.ISalaryItemService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 薪资项目基础定义表 Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Tag(name = SalaryItemApi.TITLE, description = SalaryItemApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/salary/item")
public class SalaryItemRestController extends BaseRestController<SalaryItem, SalaryItemModel, ISalaryItemService>
        implements SalaryItemApi {

    /**
     * 从请求中获取租户ID
     * @return 租户ID
     */
    private Long getTenantIdFromRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return Convert.toLong(request.getParameter("tenantId"));
        }
        return null;
    }

    /**
     * 薪资项目 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条薪资项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<SalaryItemModel> get(SalaryItemModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 薪资项目 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, SalaryItemModel request) {
        QueryBuilder<SalaryItem> queryBuilder = new GenQueryBuilder<>();
        Page<SalaryItem, SalaryItemModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryBuilder.build());
        page = IService.findPage(page);
        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 薪资项目 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部薪资项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryItemModel>> findAll(SalaryItemModel request) {
        QueryBuilder<SalaryItem> queryBuilder = new GenQueryBuilder<>();
        List<SalaryItem> entityList = IService.findList(queryBuilder.build());
        List<SalaryItemModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 薪资项目 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增薪资项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_insert')")
    @OperateLogger(description = "新增薪资项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(SalaryItemModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增薪资项目数据成功");
    }

    /**
     * 薪资项目 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改薪资项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "修改薪资项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(SalaryItemModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改薪资项目数据成功");
    }

    /**
     * 薪资项目 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除薪资项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_delete')")
    @OperateLogger(description = "删除薪资项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除薪资项目数据成功");
    }

    /**
     * 薪资项目 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除薪资项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_delete')")
    @OperateLogger(description = "批量删除薪资项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除薪资项目数据成功");
    }

    /**
     * 根据分类查询薪资项目
     * @param category 分类
     * @return ResultWrapper
     */
    @Operation(summary = "根据分类查询薪资项目")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryItemModel>> findByCategory(String category) {
        // 从请求参数中获取租户ID
        Long tenantId = getTenantIdFromRequest();
        if (tenantId == null) {
            return ResultWrapper.getSuccessResultWrapper(java.util.Collections.emptyList());
        }
        List<SalaryItemModel> modelList = IService.findByTenantIdAndCategory(tenantId, category);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 查询启用的薪资项目
     * @return ResultWrapper
     */
    @Operation(summary = "查询启用的薪资项目")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryItemModel>> findEnabled() {
        // 从请求参数中获取租户ID
        Long tenantId = getTenantIdFromRequest();
        if (tenantId == null) {
            return ResultWrapper.getSuccessResultWrapper(java.util.Collections.emptyList());
        }
        List<SalaryItemModel> modelList = IService.findEnabledByTenantId(tenantId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 检查薪资项目名称是否唯一
     * @param tenantId 租户ID
     * @param name 项目名称
     * @param id 项目ID（编辑时排除自己）
     * @return ResultWrapper
     */
    @Operation(summary = "检查薪资项目名称是否唯一")
    @Override
    public ResultWrapper<Boolean> checkNameUnique(Long tenantId, String name, String id) {
        if (tenantId == null) {
            tenantId = getTenantIdFromRequest();
        }
        if (tenantId == null) {
            return ResultWrapper.getSuccessResultWrapper(false);
        }
        boolean isUnique = IService.checkNameUnique(tenantId, name, id);
        return ResultWrapper.getSuccessResultWrapper(isUnique);
    }

    /**
     * 批量启用/禁用薪资项目
     * @param ids ID数组
     * @param status 状态
     * @return ResultWrapper
     */
    @Operation(summary = "批量启用/禁用薪资项目")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "批量启用/禁用薪资项目",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchUpdateStatus(String ids, Boolean status) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        List<String> idList = Convert.toList(String.class, idArray);
        IService.batchUpdateStatus(idList, status);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量更新薪资项目状态成功");
    }

    /**
     * 初始化系统内置薪资项目
     * @return ResultWrapper
     */
    @Operation(summary = "初始化系统内置薪资项目")
    @PreAuthorize("hasAuthority('system_salary_template_insert')")
    @OperateLogger(description = "初始化系统内置薪资项目",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> initSystemItems() {
        // 演示模式 不允许操作
        super.demoError();

        // 从请求参数中获取租户ID
        Long tenantId = getTenantIdFromRequest();
        if (tenantId == null) {
            return ResultWrapper.getCustomResultWrapper(500, "租户ID不能为空");
        }
        IService.initSystemSalaryItems(tenantId);
        return ResultWrapper.getSuccessResultWrapperByMsg("初始化系统内置薪资项目成功");
    }

    /**
     * 验证计算公式
     * @param formula 计算公式
     * @return ResultWrapper
     */
    @Operation(summary = "验证计算公式")
    @Override
    public ResultWrapper<Boolean> validateFormula(String formula) {
        boolean isValid = IService.validateCalculationFormula(formula);
        return ResultWrapper.getSuccessResultWrapper(isValid);
    }
}

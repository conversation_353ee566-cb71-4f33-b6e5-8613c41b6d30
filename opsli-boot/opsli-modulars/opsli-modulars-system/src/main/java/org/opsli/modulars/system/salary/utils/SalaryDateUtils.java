/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 薪资模块日期工具类
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
public class SalaryDateUtils {

    private static final String DATA_MONTH_FORMAT = "yyyy-MM";
    private static final ThreadLocal<SimpleDateFormat> FORMATTER = ThreadLocal.withInitial(() -> {
        SimpleDateFormat formatter = new SimpleDateFormat(DATA_MONTH_FORMAT);
        formatter.setLenient(false); // 严格模式
        return formatter;
    });

    /**
     * 解析数据月份字符串为Date对象
     * @param dataMonth 数据月份字符串 (格式: YYYY-MM)
     * @return Date对象
     * @throws ParseException 解析异常
     */
    public static Date parseDataMonth(String dataMonth) throws ParseException {
        if (dataMonth == null || dataMonth.trim().isEmpty()) {
            throw new ParseException("数据月份不能为空", 0);
        }
        
        return FORMATTER.get().parse(dataMonth.trim());
    }

    /**
     * 格式化Date对象为数据月份字符串
     * @param date Date对象
     * @return 数据月份字符串 (格式: YYYY-MM)
     */
    public static String formatDataMonth(Date date) {
        if (date == null) {
            return null;
        }
        return FORMATTER.get().format(date);
    }

    /**
     * 验证数据月份字符串格式是否正确
     * @param dataMonth 数据月份字符串
     * @return 是否有效
     */
    public static boolean isValidDataMonth(String dataMonth) {
        if (dataMonth == null || dataMonth.trim().isEmpty()) {
            return false;
        }
        
        try {
            parseDataMonth(dataMonth);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }
}
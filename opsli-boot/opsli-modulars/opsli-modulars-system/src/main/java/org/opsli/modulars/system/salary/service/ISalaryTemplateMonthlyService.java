/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service;

import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.salary.entity.SalaryTemplateMonthly;
import org.opsli.api.wrapper.system.salary.SalaryTemplateMonthlyModel;

import java.util.Date;
import java.util.List;

/**
 * 薪资模板月度数据表 Service接口
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface ISalaryTemplateMonthlyService extends CrudServiceInterface<SalaryTemplateMonthly, SalaryTemplateMonthlyModel> {

    /**
     * 根据租户ID和数据月份查询薪资模板
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 薪资模板列表
     */
    List<SalaryTemplateMonthlyModel> findByTenantIdAndDataMonth(Long tenantId, Date dataMonth);

    /**
     * 根据租户ID和数据月份查询默认薪资模板
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 默认薪资模板
     */
    SalaryTemplateMonthlyModel findDefaultByTenantIdAndDataMonth(Long tenantId, Date dataMonth);

    /**
     * 根据租户ID、数据月份和适用范围查询薪资模板
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param scopeType 适用范围类型
     * @param scopeId 适用范围ID
     * @return 薪资模板列表
     */
    List<SalaryTemplateMonthlyModel> findByTenantIdAndDataMonthAndScope(Long tenantId, Date dataMonth, String scopeType, Long scopeId);

    /**
     * 检查模板名称是否唯一
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param name 模板名称
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否唯一
     */
    boolean checkNameUnique(Long tenantId, Date dataMonth, String name, String excludeId);



    /**
     * 复制模板到新月份
     * @param sourceTemplateId 源模板ID
     * @param targetDataMonth 目标月份
     * @param newTemplateName 新模板名称
     * @return 新模板
     */
    SalaryTemplateMonthlyModel copyTemplateToNewMonth(String sourceTemplateId, Date targetDataMonth, String newTemplateName);

    /**
     * 批量启用/禁用薪资模板
     * @param ids ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> ids, Boolean status);
}

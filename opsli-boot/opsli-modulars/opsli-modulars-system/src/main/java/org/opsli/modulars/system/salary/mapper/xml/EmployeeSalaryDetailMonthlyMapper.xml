<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.salary.mapper.EmployeeSalaryDetailMonthlyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.opsli.modulars.system.salary.entity.EmployeeSalaryDetailMonthly">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="data_month" property="dataMonth" />
        <result column="employee_id" property="employeeId" />
        <result column="salary_item_id" property="salaryItemId" />
        <result column="actual_value" property="actualValue" />
        <result column="standard_value" property="standardValue" />
        <result column="calculated_value" property="calculatedValue" />
        <result column="value_source" property="valueSource" />
        <result column="is_modified" property="isModified" />
        <result column="modification_reason" property="modificationReason" />
        <result column="calculation_method" property="calculationMethod" />
        <result column="source_data" property="sourceData" />
        <result column="calculation_note" property="calculationNote" />
        <result column="last_calculated_at" property="lastCalculatedAt" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 带关联信息的查询映射结果 -->
    <resultMap id="DetailResultMap" type="org.opsli.modulars.system.salary.entity.EmployeeSalaryDetailMonthly" extends="BaseResultMap">
        <result column="employee_name" property="employeeName" />
        <result column="employee_code" property="employeeCode" />
        <result column="salary_item_name" property="salaryItemName" />
        <result column="salary_item_category" property="salaryItemCategory" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        esd.id, esd.tenant_id, esd.data_month, esd.employee_id, esd.salary_item_id, 
        esd.actual_value, esd.standard_value, esd.calculated_value, esd.value_source, 
        esd.is_modified, esd.modification_reason, esd.calculation_method, esd.source_data, 
        esd.calculation_note, esd.last_calculated_at, esd.version, esd.deleted, 
        esd.create_by, esd.create_time, esd.update_by, esd.update_time
    </sql>

    <!-- 带关联信息的查询结果列 -->
    <sql id="Detail_Column_List">
        <include refid="Base_Column_List" />,
        e.name as employee_name, e.code as employee_code,
        si.name as salary_item_name, si.category as salary_item_category
    </sql>

    <!-- 根据员工ID和数据月份查询薪资明细 -->
    <select id="findByEmployeeIdAndDataMonth" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM employee_salary_details_monthly esd
        LEFT JOIN employees e ON esd.employee_id = e.id AND e.deleted = 0
        LEFT JOIN salary_items si ON esd.salary_item_id = si.id AND si.deleted = 0
        WHERE esd.employee_id = #{employeeId} 
        AND esd.data_month = #{dataMonth} 
        AND esd.deleted = 0
        ORDER BY si.category, si.create_time ASC
    </select>

    <!-- 根据租户ID、数据月份和员工ID查询薪资明细 -->
    <select id="findByTenantIdAndDataMonthAndEmployeeId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM employee_salary_details_monthly esd
        LEFT JOIN employees e ON esd.employee_id = e.id AND e.deleted = 0
        LEFT JOIN salary_items si ON esd.salary_item_id = si.id AND si.deleted = 0
        WHERE esd.tenant_id = #{tenantId} 
        AND esd.data_month = #{dataMonth} 
        AND esd.employee_id = #{employeeId} 
        AND esd.deleted = 0
        ORDER BY si.category, si.create_time ASC
    </select>

    <!-- 根据薪资项目ID查询相关的薪资明细 -->
    <select id="findBySalaryItemId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM employee_salary_details_monthly esd
        LEFT JOIN employees e ON esd.employee_id = e.id AND e.deleted = 0
        LEFT JOIN salary_items si ON esd.salary_item_id = si.id AND si.deleted = 0
        WHERE esd.salary_item_id = #{salaryItemId} 
        AND esd.deleted = 0
        ORDER BY esd.data_month DESC, e.name ASC
    </select>

    <!-- 根据员工ID、薪资项目ID和数据月份查询薪资明细 -->
    <select id="findByEmployeeIdAndSalaryItemIdAndDataMonth" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM employee_salary_details_monthly esd
        LEFT JOIN employees e ON esd.employee_id = e.id AND e.deleted = 0
        LEFT JOIN salary_items si ON esd.salary_item_id = si.id AND si.deleted = 0
        WHERE esd.employee_id = #{employeeId} 
        AND esd.salary_item_id = #{salaryItemId} 
        AND esd.data_month = #{dataMonth} 
        AND esd.deleted = 0
    </select>

    <!-- 检查员工薪资明细是否已存在 -->
    <select id="checkEmployeeSalaryDetailExists" resultType="int">
        SELECT COUNT(1)
        FROM employee_salary_details_monthly
        WHERE tenant_id = #{tenantId} 
        AND data_month = #{dataMonth}
        AND employee_id = #{employeeId} 
        AND salary_item_id = #{salaryItemId} 
        AND deleted = 0
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量删除员工薪资明细 -->
    <update id="deleteByEmployeeIdAndDataMonth">
        UPDATE employee_salary_details_monthly 
        SET deleted = 1, update_time = NOW()
        WHERE employee_id = #{employeeId} 
        AND data_month = #{dataMonth} 
        AND tenant_id = #{tenantId}
        AND deleted = 0
    </update>

    <!-- 根据租户ID、数据月份和员工ID列表查询薪资明细 -->
    <select id="findByTenantIdAndDataMonthAndEmployeeIds" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM employee_salary_details_monthly esd
        LEFT JOIN employees e ON esd.employee_id = e.id AND e.deleted = 0
        LEFT JOIN salary_items si ON esd.salary_item_id = si.id AND si.deleted = 0
        WHERE esd.tenant_id = #{tenantId} 
        AND esd.data_month = #{dataMonth} 
        AND esd.employee_id IN
        <foreach collection="employeeIds" item="employeeId" open="(" separator="," close=")">
            #{employeeId}
        </foreach>
        AND esd.deleted = 0
        ORDER BY e.name ASC, si.category, si.create_time ASC
    </select>

    <!-- 根据值来源查询薪资明细 -->
    <select id="findByValueSource" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM employee_salary_details_monthly esd
        LEFT JOIN employees e ON esd.employee_id = e.id AND e.deleted = 0
        LEFT JOIN salary_items si ON esd.salary_item_id = si.id AND si.deleted = 0
        WHERE esd.value_source = #{valueSource} 
        AND esd.deleted = 0
        ORDER BY esd.data_month DESC, e.name ASC
    </select>

    <!-- 根据员工ID查询薪资明细历史记录 -->
    <select id="findDetailHistoryByEmployeeIdAndSalaryItemId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM employee_salary_details_monthly esd
        LEFT JOIN employees e ON esd.employee_id = e.id AND e.deleted = 0
        LEFT JOIN salary_items si ON esd.salary_item_id = si.id AND si.deleted = 0
        WHERE esd.employee_id = #{employeeId} 
        AND esd.salary_item_id = #{salaryItemId} 
        AND esd.deleted = 0
        ORDER BY esd.data_month DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取员工薪资明细统计信息 -->
    <select id="getEmployeeSalaryStatistics" resultType="map">
        SELECT 
            COUNT(1) as total_items,
            COUNT(CASE WHEN is_modified = 1 THEN 1 END) as modified_items,
            COUNT(CASE WHEN value_source = '岗位标准' THEN 1 END) as standard_filled_items,
            COUNT(CASE WHEN value_source = '手动录入' THEN 1 END) as manual_input_items,
            COUNT(CASE WHEN value_source = '公式计算' THEN 1 END) as calculated_items,
            SUM(CASE WHEN calculated_value IS NOT NULL THEN calculated_value ELSE 0 END) as total_calculated_value
        FROM employee_salary_details_monthly
        WHERE tenant_id = #{tenantId} 
        AND data_month = #{dataMonth} 
        AND employee_id = #{employeeId} 
        AND deleted = 0
    </select>

</mapper>
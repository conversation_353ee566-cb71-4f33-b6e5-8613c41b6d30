<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.salary.mapper.SalaryItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.opsli.modulars.system.salary.entity.SalaryItem">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="name" property="name" />
        <result column="category" property="category" />
        <result column="data_type" property="dataType" />
        <result column="unit" property="unit" />
        <result column="decimal_places" property="decimalPlaces" />
        <result column="calculation_formula" property="calculationFormula" />
        <result column="is_system_item" property="isSystemItem" />
        <result column="status" property="status" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, name, category, data_type, unit, decimal_places, calculation_formula, 
        is_system_item, status, version, deleted, create_by, create_time, update_by, update_time
    </sql>

    <!-- 根据租户ID和分类查询薪资项目 -->
    <select id="findByTenantIdAndCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM salary_items
        WHERE tenant_id = #{tenantId} AND category = #{category} AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据租户ID查询启用的薪资项目 -->
    <select id="findEnabledByTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM salary_items
        WHERE tenant_id = #{tenantId} AND status = 1 AND deleted = 0
        ORDER BY category, create_time DESC
    </select>

    <!-- 检查薪资项目名称是否唯一 -->
    <select id="checkNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM salary_items
        WHERE tenant_id = #{tenantId} AND name = #{name} AND deleted = 0
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据租户ID和数据类型查询薪资项目 -->
    <select id="findByTenantIdAndDataType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM salary_items
        WHERE tenant_id = #{tenantId} AND data_type = #{dataType} AND deleted = 0
        ORDER BY create_time DESC
    </select>

</mapper>

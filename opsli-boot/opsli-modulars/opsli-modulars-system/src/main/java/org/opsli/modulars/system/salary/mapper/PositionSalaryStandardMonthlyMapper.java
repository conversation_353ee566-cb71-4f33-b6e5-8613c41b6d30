/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.salary.entity.PositionSalaryStandardMonthly;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 岗位薪资标准月度数据表 Mapper
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface PositionSalaryStandardMonthlyMapper extends BaseMapper<PositionSalaryStandardMonthly> {

    /**
     * 根据岗位ID、模板ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param dataMonth 数据月份
     * @return 薪资标准列表
     */
    List<PositionSalaryStandardMonthly> findByPositionIdAndTemplateIdAndDataMonth(@Param("positionId") Long positionId, 
                                                                                  @Param("templateId") Long templateId,
                                                                                  @Param("dataMonth") Date dataMonth);

    /**
     * 根据租户ID、数据月份和岗位ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return 薪资标准列表
     */
    List<PositionSalaryStandardMonthly> findByTenantIdAndDataMonthAndPositionId(@Param("tenantId") Long tenantId, 
                                                                               @Param("dataMonth") Date dataMonth,
                                                                               @Param("positionId") Long positionId);

    /**
     * 根据租户ID、数据月份和模板ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return 薪资标准列表
     */
    List<PositionSalaryStandardMonthly> findByTenantIdAndDataMonthAndTemplateId(@Param("tenantId") Long tenantId, 
                                                                               @Param("dataMonth") Date dataMonth,
                                                                               @Param("templateId") Long templateId);

    /**
     * 根据薪资项目ID查询关联的岗位标准
     * @param salaryItemId 薪资项目ID
     * @return 岗位标准列表
     */
    List<PositionSalaryStandardMonthly> findBySalaryItemId(@Param("salaryItemId") Long salaryItemId);

    /**
     * 检查岗位薪资标准是否已存在
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param salaryItemId 薪资项目ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 数量
     */
    int checkPositionStandardExists(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth,
                                   @Param("positionId") Long positionId, @Param("templateId") Long templateId,
                                   @Param("salaryItemId") Long salaryItemId, @Param("excludeId") String excludeId);

    /**
     * 批量删除岗位薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 删除数量
     */
    int deleteByPositionIdAndTemplateId(@Param("positionId") Long positionId, @Param("templateId") Long templateId,
                                       @Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据岗位ID、薪资项目ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return 薪资标准
     */
    PositionSalaryStandardMonthly findByPositionIdAndSalaryItemIdAndDataMonth(@Param("positionId") Long positionId, 
                                                                              @Param("salaryItemId") Long salaryItemId,
                                                                              @Param("dataMonth") Date dataMonth);

    /**
     * 获取岗位薪资标准统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return 统计信息
     */
    Map<String, Object> getPositionStandardStatistics(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth, @Param("positionId") Long positionId);
}

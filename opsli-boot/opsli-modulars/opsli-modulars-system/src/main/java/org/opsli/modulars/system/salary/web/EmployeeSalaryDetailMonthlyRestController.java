/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.web;

import cn.hutool.core.convert.Convert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.salary.EmployeeSalaryDetailMonthlyApi;
import org.opsli.api.wrapper.system.salary.EmployeeSalaryDetailMonthlyModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.QueryBuilder;
import org.opsli.core.persistence.querybuilder.GenQueryBuilder;
import org.opsli.modulars.system.salary.entity.EmployeeSalaryDetailMonthly;
import org.opsli.modulars.system.salary.service.IEmployeeSalaryDetailMonthlyService;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 员工薪资明细月度数据表 Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Tag(name = EmployeeSalaryDetailMonthlyApi.TITLE, description = EmployeeSalaryDetailMonthlyApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/salary/employeeDetail")
public class EmployeeSalaryDetailMonthlyRestController extends BaseRestController<EmployeeSalaryDetailMonthly, EmployeeSalaryDetailMonthlyModel, IEmployeeSalaryDetailMonthlyService>
        implements EmployeeSalaryDetailMonthlyApi {

    /**
     * 员工薪资明细 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条员工薪资明细数据")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<EmployeeSalaryDetailMonthlyModel> get(EmployeeSalaryDetailMonthlyModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 员工薪资明细 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, EmployeeSalaryDetailMonthlyModel request) {
        QueryBuilder<EmployeeSalaryDetailMonthly> queryBuilder = new GenQueryBuilder<>();
        Page<EmployeeSalaryDetailMonthly, EmployeeSalaryDetailMonthlyModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryBuilder.build());
        page = IService.findPage(page);
        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 员工薪资明细 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部员工薪资明细数据")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findAll(EmployeeSalaryDetailMonthlyModel request) {
        QueryBuilder<EmployeeSalaryDetailMonthly> queryBuilder = new GenQueryBuilder<>();
        List<EmployeeSalaryDetailMonthly> entityList = IService.findList(queryBuilder.build());
        List<EmployeeSalaryDetailMonthlyModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 员工薪资明细 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增员工薪资明细数据")
    @PreAuthorize("hasAuthority('system_salary_employee_insert')")
    @OperateLogger(description = "新增员工薪资明细数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(EmployeeSalaryDetailMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增员工薪资明细数据成功");
    }

    /**
     * 员工薪资明细 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改员工薪资明细数据")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "修改员工薪资明细数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(EmployeeSalaryDetailMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改员工薪资明细数据成功");
    }

    /**
     * 员工薪资明细 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除员工薪资明细数据")
    @PreAuthorize("hasAuthority('system_salary_employee_delete')")
    @OperateLogger(description = "删除员工薪资明细数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除员工薪资明细数据成功");
    }

    /**
     * 员工薪资明细 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除员工薪资明细数据")
    @PreAuthorize("hasAuthority('system_salary_employee_delete')")
    @OperateLogger(description = "批量删除员工薪资明细数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除员工薪资明细数据成功");
    }

    /**
     * 根据租户ID、数据月份和员工ID查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据员工查询薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findByEmployee(Long tenantId, Date dataMonth, Long employeeId) {
        List<EmployeeSalaryDetailMonthlyModel> modelList = IService.findByTenantIdAndDataMonthAndEmployeeId(tenantId, dataMonth, employeeId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据租户ID、数据月份和员工ID列表查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeIds 员工ID列表
     * @return ResultWrapper
     */
    @Operation(summary = "根据员工列表查询薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findByEmployees(Long tenantId, Date dataMonth, String employeeIds) {
        String[] idArray = Convert.toStrArray(employeeIds);
        List<Long> idList = Convert.toList(Long.class, idArray);
        List<EmployeeSalaryDetailMonthlyModel> modelList = IService.findByTenantIdAndDataMonthAndEmployeeIds(tenantId, dataMonth, idList);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据薪资项目ID查询相关的薪资明细
     * @param salaryItemId 薪资项目ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据薪资项目查询相关的薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findBySalaryItem(Long salaryItemId) {
        List<EmployeeSalaryDetailMonthlyModel> modelList = IService.findBySalaryItemId(salaryItemId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据员工ID、薪资项目ID和数据月份查询薪资明细
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "根据员工和薪资项目查询薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<EmployeeSalaryDetailMonthlyModel> findByEmployeeAndSalaryItem(Long employeeId, Long salaryItemId, Date dataMonth) {
        EmployeeSalaryDetailMonthlyModel model = IService.findByEmployeeIdAndSalaryItemIdAndDataMonth(employeeId, salaryItemId, dataMonth);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 检查员工薪资明细是否已存在
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查员工薪资明细是否已存在")
    @Override
    public ResultWrapper<Boolean> checkExists(EmployeeSalaryDetailMonthlyModel model) {
        boolean exists = IService.checkEmployeeSalaryDetailExists(model.getTenantId(), model.getDataMonth(), 
                model.getEmployeeId(), model.getSalaryItemId(), model.getId());
        return ResultWrapper.getSuccessResultWrapper(exists);
    }

    /**
     * 根据值来源查询薪资明细
     * @param valueSource 值来源
     * @return ResultWrapper
     */
    @Operation(summary = "根据值来源查询薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findByValueSource(String valueSource) {
        List<EmployeeSalaryDetailMonthlyModel> modelList = IService.findByValueSource(valueSource);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 根据员工ID查询薪资明细历史记录
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param limit 限制数量
     * @return ResultWrapper
     */
    @Operation(summary = "查询薪资明细历史记录")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findDetailHistory(Long employeeId, Long salaryItemId, Integer limit) {
        List<EmployeeSalaryDetailMonthlyModel> modelList = IService.findDetailHistoryByEmployeeIdAndSalaryItemId(employeeId, salaryItemId, limit);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 批量保存员工薪资明细
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param salaryDetails 薪资明细列表
     * @return ResultWrapper
     */
    @Operation(summary = "批量保存员工薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "批量保存员工薪资明细",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchSave(Long employeeId, Long tenantId, Date dataMonth, List<EmployeeSalaryDetailMonthlyModel> salaryDetails) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.batchSaveEmployeeSalaryDetails(employeeId, tenantId, dataMonth, salaryDetails);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("批量保存员工薪资明细成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "批量保存员工薪资明细失败");
        }
    }

    /**
     * 删除员工薪资明细
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @param tenantId 租户ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除员工薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_delete')")
    @OperateLogger(description = "删除员工薪资明细",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> deleteByEmployee(Long employeeId, Date dataMonth, Long tenantId) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.deleteByEmployeeIdAndDataMonth(employeeId, dataMonth, tenantId);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("删除员工薪资明细成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "删除员工薪资明细失败");
        }
    }

    /**
     * 根据岗位标准自动预填充员工薪资明细
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 薪资模板ID
     * @return ResultWrapper
     */
    @Operation(summary = "自动预填充员工薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_insert')")
    @OperateLogger(description = "自动预填充员工薪资明细",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> autoFillFromStandards(Long employeeId, Long tenantId, Date dataMonth, Long templateId) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.autoFillFromPositionStandards(employeeId, tenantId, dataMonth, templateId);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("自动预填充员工薪资明细成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "自动预填充员工薪资明细失败");
        }
    }

    /**
     * 批量更新计算值
     * @param salaryDetailIds 薪资明细ID列表
     * @return ResultWrapper
     */
    @Operation(summary = "批量更新计算值")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "批量更新计算值",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchUpdateCalculatedValues(List<String> salaryDetailIds) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.batchUpdateCalculatedValues(salaryDetailIds);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("批量更新计算值成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "批量更新计算值失败");
        }
    }

    /**
     * 标记为已修改
     * @param id 薪资明细ID
     * @param modificationReason 修改原因
     * @return ResultWrapper
     */
    @Operation(summary = "标记为已修改")
    @PreAuthorize("hasAuthority('system_salary_employee_update')")
    @OperateLogger(description = "标记薪资明细为已修改",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> markAsModified(String id, String modificationReason) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.markAsModified(id, modificationReason);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("标记为已修改成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "标记为已修改失败");
        }
    }

    /**
     * 批量导入员工薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param salaryDetails 薪资明细列表
     * @return ResultWrapper
     */
    @Operation(summary = "批量导入员工薪资明细")
    @PreAuthorize("hasAuthority('system_salary_employee_insert')")
    @OperateLogger(description = "批量导入员工薪资明细",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<Map<String, Object>> batchImport(Long tenantId, Date dataMonth, List<EmployeeSalaryDetailMonthlyModel> salaryDetails) {
        // 演示模式 不允许操作
        super.demoError();
        
        Map<String, Object> result = IService.batchImportSalaryDetails(tenantId, dataMonth, salaryDetails);
        return ResultWrapper.getSuccessResultWrapper(result);
    }

    /**
     * 复制上月薪资明细到当月
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param sourceMonth 源月份
     * @param targetMonth 目标月份
     * @return ResultWrapper
     */
    @Operation(summary = "复制上月薪资明细到当月")
    @PreAuthorize("hasAuthority('system_salary_employee_insert')")
    @OperateLogger(description = "复制上月薪资明细到当月",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> copyFromPreviousMonth(Long employeeId, Long tenantId, Date sourceMonth, Date targetMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.copyFromPreviousMonth(employeeId, tenantId, sourceMonth, targetMonth);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("复制上月薪资明细成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "复制上月薪资明细失败");
        }
    }

    /**
     * 获取员工薪资明细统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return ResultWrapper
     */
    @Operation(summary = "获取员工薪资明细统计信息")
    @PreAuthorize("hasAuthority('system_salary_employee_select')")
    @Override
    public ResultWrapper<Map<String, Object>> getStatistics(Long tenantId, Date dataMonth, Long employeeId) {
        Map<String, Object> statistics = IService.getEmployeeSalaryStatistics(tenantId, dataMonth, employeeId);
        return ResultWrapper.getSuccessResultWrapper(statistics);
    }
}
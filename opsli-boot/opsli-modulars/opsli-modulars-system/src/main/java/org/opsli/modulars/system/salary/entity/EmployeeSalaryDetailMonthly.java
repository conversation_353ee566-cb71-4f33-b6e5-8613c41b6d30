/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.entity;

import java.math.BigDecimal;
import java.util.Date;

import org.opsli.core.base.entity.BaseEntity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 员工薪资明细月度数据表
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("employee_salary_details_monthly")
public class EmployeeSalaryDetailMonthly extends BaseEntity {

    /** 租户ID */
    private Long tenantId;
    
    /** 数据月份 */
    private Date dataMonth;
    
    /** 员工ID */
    private Long employeeId;
    
    /** 薪资项目ID */
    private Long salaryItemId;
    
    /** 实际值（用户录入或系统计算） */
    private String actualValue;
    
    /** 岗位标准值（来源于position_salary_standards） */
    private String standardValue;
    
    /** 计算后的数值（仅用于统计，非decimal类型为NULL） */
    private BigDecimal calculatedValue;
    
    /** 值来源 */
    private String valueSource;
    
    /** 是否已修改（区别于预填充值） */
    private Boolean isModified;
    
    /** 修改原因 */
    private String modificationReason;
    
    /** 计算方式 */
    private String calculationMethod;
    
    /** 计算来源数据 */
    private String sourceData;
    
    /** 计算说明 */
    private String calculationNote;
    
    /** 最后计算时间 */
    private Date lastCalculatedAt;
    
    /** 版本号 */
    @Version
    private Integer version;
    
    /** 逻辑删除标识：0-未删除，1-已删除 */
    @TableLogic
    private Integer deleted;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新人 */
    private String updateBy;
    
    /** 更新时间 */
    private Date updateTime;
    
    // ========== 关联查询字段 ==========
    
    /** 员工姓名 (关联查询字段) */
    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String employeeName;
    
    /** 员工工号 (关联查询字段) */
    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String employeeCode;
    
    /** 薪资项目名称 (关联查询字段) */
    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String salaryItemName;
    
    /** 薪资项目分类 (关联查询字段) */
    @com.baomidou.mybatisplus.annotation.TableField(exist = false)
    private String salaryItemCategory;
}

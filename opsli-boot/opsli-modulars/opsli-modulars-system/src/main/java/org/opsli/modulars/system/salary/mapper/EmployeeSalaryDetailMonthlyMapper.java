/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.salary.entity.EmployeeSalaryDetailMonthly;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 员工薪资明细月度数据表 Mapper
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Mapper
public interface EmployeeSalaryDetailMonthlyMapper extends BaseMapper<EmployeeSalaryDetailMonthly> {

    /**
     * 根据员工ID和数据月份查询薪资明细
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthly> findByEmployeeIdAndDataMonth(@Param("employeeId") Long employeeId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据租户ID、数据月份和员工ID查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthly> findByTenantIdAndDataMonthAndEmployeeId(@Param("tenantId") Long tenantId, 
                                                                             @Param("dataMonth") Date dataMonth,
                                                                             @Param("employeeId") Long employeeId);

    /**
     * 根据薪资项目ID查询相关的薪资明细
     * @param salaryItemId 薪资项目ID
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthly> findBySalaryItemId(@Param("salaryItemId") Long salaryItemId);

    /**
     * 根据员工ID、薪资项目ID和数据月份查询薪资明细
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return 薪资明细
     */
    EmployeeSalaryDetailMonthly findByEmployeeIdAndSalaryItemIdAndDataMonth(@Param("employeeId") Long employeeId, 
                                                                           @Param("salaryItemId") Long salaryItemId,
                                                                           @Param("dataMonth") Date dataMonth);

    /**
     * 检查员工薪资明细是否已存在
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 数量
     */
    int checkEmployeeSalaryDetailExists(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth,
                                       @Param("employeeId") Long employeeId, @Param("salaryItemId") Long salaryItemId,
                                       @Param("excludeId") String excludeId);

    /**
     * 批量删除员工薪资明细
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @param tenantId 租户ID
     * @return 删除数量
     */
    int deleteByEmployeeIdAndDataMonth(@Param("employeeId") Long employeeId, @Param("dataMonth") Date dataMonth, @Param("tenantId") Long tenantId);

    /**
     * 根据租户ID、数据月份和员工ID列表查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeIds 员工ID列表
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthly> findByTenantIdAndDataMonthAndEmployeeIds(@Param("tenantId") Long tenantId, 
                                                                              @Param("dataMonth") Date dataMonth,
                                                                              @Param("employeeIds") List<Long> employeeIds);

    /**
     * 根据值来源查询薪资明细
     * @param valueSource 值来源
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthly> findByValueSource(@Param("valueSource") String valueSource);

    /**
     * 根据员工ID查询薪资明细历史记录
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param limit 限制数量
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthly> findDetailHistoryByEmployeeIdAndSalaryItemId(@Param("employeeId") Long employeeId, 
                                                                                   @Param("salaryItemId") Long salaryItemId,
                                                                                   @Param("limit") Integer limit);

    /**
     * 获取员工薪资明细统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return 统计信息
     */
    Map<String, Object> getEmployeeSalaryStatistics(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth, @Param("employeeId") Long employeeId);
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.organizationChart.service;

import org.opsli.api.wrapper.system.organizationChart.OrganizationChartModel;

import java.util.Date;
import java.util.List;

/**
 * 组织架构图 Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IOrganizationChartService {

    /**
     * 获取完整的组织架构树
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 组织架构树列表
     */
    List<OrganizationChartModel> getOrganizationTree(Long tenantId, Date dataMonth);

    /**
     * 获取指定部门的组织架构子树
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 部门组织架构子树列表
     */
    List<OrganizationChartModel> getDepartmentTree(Long tenantId, Date dataMonth, Long departmentId);

    /**
     * 获取组织架构统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 统计信息
     */
    OrganizationChartModel getOrganizationStats(Long tenantId, Date dataMonth);

    /**
     * 搜索组织架构节点
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param keyword 搜索关键词
     * @param nodeType 节点类型
     * @return 搜索结果列表
     */
    List<OrganizationChartModel> searchNodes(Long tenantId, Date dataMonth, String keyword, String nodeType);
}

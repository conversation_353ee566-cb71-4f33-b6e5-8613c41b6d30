<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.department.mapper.DepartmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="departmentResultMap" type="org.opsli.modulars.system.department.entity.DepartmentMonthly">
        <id column="tenant_id" property="tenantId" />
        <id column="data_month" property="dataMonth" />
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="dept_code" property="deptCode" />
        <result column="parent_department_id" property="parentDepartmentId" />
        <result column="status" property="status" />
        <result column="version" property="version" />
        <result column="responsible_person" property="responsiblePerson" />
        <result column="headcount" property="headcount" />
        <result column="responsibilities" property="responsibilities" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        tenant_id, data_month, id, name, dept_code, parent_department_id, status, version,
        responsible_person, headcount, responsibilities, deleted, create_by, create_time, update_by, update_time
    </sql>

    <!-- 根据父部门ID查询子部门列表 -->
    <select id="findByParentId" resultMap="departmentResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM departments_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        <if test="parentId != null">
            AND parent_department_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_department_id IS NULL
        </if>
        AND deleted = 0
    </select>

    <!-- 检查部门名称是否唯一（在同一租户和月份下） -->
    <select id="checkNameUnique" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM departments_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND name = #{name}
        AND deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <!-- 检查部门编码是否唯一（在同一租户和月份下） -->
    <select id="checkCodeUnique" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM departments_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND dept_code = #{deptCode}
        AND deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

</mapper>
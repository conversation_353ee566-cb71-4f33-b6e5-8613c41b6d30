/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.wrapper.system.salary.SalaryTemplateItemMonthlyModel;
import org.opsli.common.exception.ServiceException;
import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.core.utils.UserUtil;
import org.opsli.modulars.system.salary.entity.SalaryTemplateItemMonthly;
import org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper;
import org.opsli.modulars.system.salary.service.ISalaryTemplateItemMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 薪资模板项目关联表 Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Service
public class SalaryTemplateItemMonthlyServiceImpl extends CrudServiceImpl<SalaryTemplateItemMonthlyMapper, SalaryTemplateItemMonthly, SalaryTemplateItemMonthlyModel> 
        implements ISalaryTemplateItemMonthlyService {

    @Autowired(required = false)
    private SalaryTemplateItemMonthlyMapper mapper;

    @Override
    public List<SalaryTemplateItemMonthlyModel> findByTenantIdAndDataMonthAndTemplateId(Long tenantId, Date dataMonth, Long templateId) {
        if (tenantId == null || dataMonth == null || templateId == null) {
            return CollUtil.newArrayList();
        }
        
        List<SalaryTemplateItemMonthly> entityList = mapper.findByTenantIdAndDataMonthAndTemplateId(tenantId, dataMonth, templateId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<SalaryTemplateItemMonthlyModel> findEnabledByTenantIdAndDataMonthAndTemplateId(Long tenantId, Date dataMonth, Long templateId) {
        if (tenantId == null || dataMonth == null || templateId == null) {
            return CollUtil.newArrayList();
        }
        
        List<SalaryTemplateItemMonthly> entityList = mapper.findEnabledByTenantIdAndDataMonthAndTemplateIdOrderByDisplayOrder(tenantId, dataMonth, templateId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<SalaryTemplateItemMonthlyModel> findBySalaryItemId(Long salaryItemId) {
        if (salaryItemId == null) {
            return CollUtil.newArrayList();
        }
        
        List<SalaryTemplateItemMonthly> entityList = mapper.findBySalaryItemId(salaryItemId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public boolean checkTemplateItemExists(Long tenantId, Date dataMonth, Long templateId, Long salaryItemId, String excludeId) {
        if (tenantId == null || dataMonth == null || templateId == null || salaryItemId == null) {
            return false;
        }
        
        int count = mapper.checkTemplateItemExists(tenantId, dataMonth, templateId, salaryItemId, excludeId);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveTemplateItems(Long templateId, Long tenantId, Date dataMonth, List<SalaryTemplateItemMonthlyModel> templateItems) {
        if (templateId == null || tenantId == null || dataMonth == null || CollUtil.isEmpty(templateItems)) {
            return false;
        }

        try {
            // 先物理删除原有的模板项目（避免唯一约束冲突）
            this.hardDeleteByTemplateId(templateId, tenantId, dataMonth);
            
            // 去重处理：使用Set来跟踪已处理的薪资项目ID
            Set<Long> processedItemIds = new HashSet<>();
            
            // 批量保存新的模板项目
            for (int i = 0; i < templateItems.size(); i++) {
                SalaryTemplateItemMonthlyModel model = templateItems.get(i);
                
                // 检查是否重复
                if (processedItemIds.contains(model.getSalaryItemId())) {
                    log.warn("发现重复的薪资项目ID: {}, 跳过处理", model.getSalaryItemId());
                    continue;
                }
                processedItemIds.add(model.getSalaryItemId());
                
                // 清空ID，确保插入新记录
                model.setId(null);
                model.setTemplateId(templateId);
                model.setTenantId(tenantId);
                model.setDataMonth(dataMonth);
                model.setDisplayOrder(i + 1); // 设置显示顺序
                
                // 设置默认值
                if (model.getIsRequired() == null) {
                    model.setIsRequired(true);
                }
                if (model.getIsEditable() == null) {
                    model.setIsEditable(true);
                }
                if (model.getStatus() == null) {
                    model.setStatus(true);
                }
                
                // 处理JSON字段：如果为空字符串，设置为null
                if (StrUtil.isBlank(model.getValidationRules())) {
                    model.setValidationRules(null);
                }
                if (StrUtil.isBlank(model.getDefaultValue())) {
                    model.setDefaultValue(null);
                }
                
                super.insert(model);
            }
            
            return true;
        } catch (Exception e) {
            log.error("批量保存模板项目失败, templateId: {}, tenantId: {}, dataMonth: {}", templateId, tenantId, dataMonth, e);
            
            // 检查是否是唯一约束冲突
            if (e.getMessage() != null && e.getMessage().contains("uk_template_item")) {
                throw new ServiceException(500, "模板项目配置冲突，请检查是否有重复的薪资项目");
            }
            
            throw new ServiceException(500, "批量保存模板项目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByTemplateId(Long templateId, Long tenantId, Date dataMonth) {
        if (templateId == null || tenantId == null || dataMonth == null) {
            return false;
        }
        
        try {
            int result = mapper.deleteByTemplateId(templateId, tenantId, dataMonth);
            return result >= 0;
        } catch (Exception e) {
            log.error("删除模板项目失败", e);
            throw new ServiceException(500, "删除模板项目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean hardDeleteByTemplateId(Long templateId, Long tenantId, Date dataMonth) {
        if (templateId == null || tenantId == null || dataMonth == null) {
            return false;
        }
        
        try {
            int result = mapper.hardDeleteByTemplateId(templateId, tenantId, dataMonth);
            log.info("物理删除模板项目成功, templateId: {}, tenantId: {}, dataMonth: {}, 删除数量: {}", 
                    templateId, tenantId, dataMonth, result);
            return result >= 0;
        } catch (Exception e) {
            log.error("物理删除模板项目失败", e);
            throw new ServiceException(500, "物理删除模板项目失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDisplayOrder(List<String> templateItemIds) {
        if (CollUtil.isEmpty(templateItemIds)) {
            return false;
        }

        try {
            for (int i = 0; i < templateItemIds.size(); i++) {
                String id = templateItemIds.get(i);
                if (StrUtil.isNotBlank(id)) {
                    UpdateWrapper<SalaryTemplateItemMonthly> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("id", id);
                    updateWrapper.set("display_order", i + 1);
                    updateWrapper.set("update_time", new Date());
                    updateWrapper.set("update_by", UserUtil.getUser().getUsername());
                    
                    super.update(updateWrapper);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("更新显示顺序失败", e);
            throw new ServiceException(500, "更新显示顺序失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, Boolean status) {
        if (CollUtil.isEmpty(ids) || status == null) {
            return false;
        }

        try {
            UpdateWrapper<SalaryTemplateItemMonthly> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", ids);
            updateWrapper.set("status", status);
            updateWrapper.set("update_time", new Date());
            updateWrapper.set("update_by", UserUtil.getUser().getUsername());
            
            return super.update(updateWrapper);
        } catch (Exception e) {
            log.error("批量更新状态失败", e);
            throw new ServiceException(500, "批量更新状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyTemplateItems(Long sourceTemplateId, Long targetTemplateId, Long tenantId, Date dataMonth) {
        if (sourceTemplateId == null || targetTemplateId == null || tenantId == null || dataMonth == null) {
            return false;
        }

        try {
            // 查询源模板的项目
            List<SalaryTemplateItemMonthlyModel> sourceItems = this.findByTenantIdAndDataMonthAndTemplateId(tenantId, dataMonth, sourceTemplateId);
            
            if (CollUtil.isEmpty(sourceItems)) {
                return true; // 源模板没有项目，复制成功
            }

            // 复制到目标模板
            for (SalaryTemplateItemMonthlyModel sourceItem : sourceItems) {
                sourceItem.setId(null); // 清空ID，让系统自动生成
                sourceItem.setTemplateId(targetTemplateId);
                sourceItem.setCreateTime(null);
                sourceItem.setUpdateTime(null);
                sourceItem.setCreateBy(null);
                sourceItem.setUpdateBy(null);
                sourceItem.setVersion(0);
                
                // 处理JSON字段：如果为空字符串，设置为null
                if (StrUtil.isBlank(sourceItem.getValidationRules())) {
                    sourceItem.setValidationRules(null);
                }
                if (StrUtil.isBlank(sourceItem.getDefaultValue())) {
                    sourceItem.setDefaultValue(null);
                }
                
                super.insert(sourceItem);
            }
            
            return true;
        } catch (Exception e) {
            log.error("复制模板项目失败", e);
            throw new ServiceException(500, "复制模板项目失败: " + e.getMessage());
        }
    }

    @Override
    public Integer getMaxDisplayOrder(Long templateId, Long tenantId, Date dataMonth) {
        if (templateId == null || tenantId == null || dataMonth == null) {
            return 0;
        }
        
        Integer maxOrder = mapper.getMaxDisplayOrder(templateId, tenantId, dataMonth);
        return maxOrder != null ? maxOrder : 0;
    }

    @Override
    protected void beforeInsert(SalaryTemplateItemMonthlyModel model, SalaryTemplateItemMonthly entity) {
        // 检查模板项目是否已存在
        if (this.checkTemplateItemExists(model.getTenantId(), model.getDataMonth(), 
                model.getTemplateId(), model.getSalaryItemId(), null)) {
            throw new ServiceException(400, "该薪资项目已在模板中存在");
        }
        
        // 如果没有设置显示顺序，自动设置为最大值+1
        if (model.getDisplayOrder() == null) {
            Integer maxOrder = this.getMaxDisplayOrder(model.getTemplateId(), model.getTenantId(), model.getDataMonth());
            model.setDisplayOrder(maxOrder + 1);
        }
        
        // 设置默认值
        if (model.getIsRequired() == null) {
            model.setIsRequired(true);
        }
        if (model.getIsEditable() == null) {
            model.setIsEditable(true);
        }
        if (model.getStatus() == null) {
            model.setStatus(true);
        }
        
        // 处理JSON字段：如果为空字符串，设置为null
        if (StrUtil.isBlank(model.getValidationRules())) {
            model.setValidationRules(null);
        }
        if (StrUtil.isBlank(model.getDefaultValue())) {
            model.setDefaultValue(null);
        }
    }

    @Override
    protected void beforeUpdate(SalaryTemplateItemMonthlyModel model, SalaryTemplateItemMonthly entity) {
        // 检查模板项目是否已存在（排除当前记录）
        if (this.checkTemplateItemExists(model.getTenantId(), model.getDataMonth(), 
                model.getTemplateId(), model.getSalaryItemId(), model.getId())) {
            throw new ServiceException(400, "该薪资项目已在模板中存在");
        }
        
        // 处理JSON字段：如果为空字符串，设置为null
        if (StrUtil.isBlank(model.getValidationRules())) {
            model.setValidationRules(null);
        }
        if (StrUtil.isBlank(model.getDefaultValue())) {
            model.setDefaultValue(null);
        }
    }
}
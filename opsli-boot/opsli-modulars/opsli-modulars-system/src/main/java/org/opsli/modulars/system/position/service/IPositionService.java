/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.position.service;

import org.opsli.api.wrapper.system.position.PositionModel;
import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.position.entity.PositionMonthly;

import java.util.Date;
import java.util.List;

/**
 * 职位管理 - 按月份 Service
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IPositionService extends CrudServiceInterface<PositionMonthly, PositionModel> {

    /**
     * 根据职位编码检查唯一性（在同一租户和月份下）
     *
     * @param model 职位模型
     * @return 是否唯一
     */
    boolean checkCodeUnique(PositionModel model);

    /**
     * 根据职位名称检查唯一性（在同一租户和月份下）
     *
     * @param model 职位模型
     * @return 是否唯一
     */
    boolean checkTitleUnique(PositionModel model);

    /**
     * 根据部门ID获取职位列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 职位列表
     */
    List<PositionModel> findByDepartmentId(Long tenantId, Date dataMonth, Long departmentId);

    /**
     * 根据租户ID和月份查询岗位列表
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return List
     */
    List<PositionModel> findByTenantAndMonth(Long tenantId, Date dataMonth);
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.opsli.core.base.entity.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 员工月度薪资汇总表
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("employee_salaries_monthly")
public class EmployeeSalaryMonthly extends BaseEntity {

    /** 租户ID */
    private Long tenantId;
    
    /** 数据月份 */
    private Date dataMonth;
    
    /** 员工ID */
    private Long employeeId;
    
    /** 使用的薪资模板ID */
    private Long templateId;
    
    /** 基本工资合计 */
    private BigDecimal baseSalaryTotal;
    
    /** 补贴合计 */
    private BigDecimal allowanceTotal;
    
    /** 提成合计 */
    private BigDecimal commissionTotal;
    
    /** 奖金合计 */
    private BigDecimal bonusTotal;
    
    /** 扣除合计 */
    private BigDecimal deductionTotal;
    
    /** 应发工资总额 */
    private BigDecimal grossSalary;
    
    /** 实发工资 */
    private BigDecimal netSalary;
    
    /** 计算时间 */
    private Date calculatedAt;
    
    /** 计算结果哈希（用于检测变更） */
    private String calculationHash;
    
    /** 状态 */
    private String status;
    
    /** 审批人 */
    private String approvedBy;
    
    /** 审批时间 */
    private Date approvedTime;
    
    /** 发放日期 */
    private Date payDate;
    
    /** 版本号 */
    @Version
    private Integer version;
    
    /** 逻辑删除标识：0-未删除，1-已删除 */
    @TableLogic
    private Integer deleted;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新人 */
    private String updateBy;
    
    /** 更新时间 */
    private Date updateTime;
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.employee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.opsli.modulars.system.employee.entity.EmployeeMonthly;

import java.util.Date;
import java.util.List;

/**
 * 员工管理 - 按月份 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Mapper
public interface EmployeeMapper extends BaseMapper<EmployeeMonthly> {

    /**
     * 根据部门ID查询员工列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param departmentId 部门ID
     * @return 员工列表
     */
    List<EmployeeMonthly> findByDepartmentId(@Param("tenantId") Long tenantId, 
                                           @Param("dataMonth") Date dataMonth, 
                                           @Param("departmentId") Long departmentId);

    /**
     * 根据租户ID和月份查询员工列表
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return List
     */
    List<EmployeeMonthly> findByTenantAndMonth(@Param("tenantId") Long tenantId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据职位ID查询员工列表
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 职位ID
     * @return 员工列表
     */
    List<EmployeeMonthly> findByPositionId(@Param("tenantId") Long tenantId, 
                                         @Param("dataMonth") Date dataMonth, 
                                         @Param("positionId") Long positionId);

    /**
     * 检查员工编号是否唯一（在同一租户和月份下）
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeNumber 员工编号
     * @param id 排除的员工ID（更新时使用）
     * @return 存在的数量
     */
    int checkEmployeeNumberUnique(@Param("tenantId") Long tenantId, 
                                @Param("dataMonth") Date dataMonth, 
                                @Param("employeeNumber") String employeeNumber, 
                                @Param("id") Long id);

    /**
     * 检查身份证号是否唯一（在同一租户和月份下）
     *
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param idCardNumber 身份证号
     * @param id 排除的员工ID（更新时使用）
     * @return 存在的数量
     */
    int checkIdCardNumberUnique(@Param("tenantId") Long tenantId, 
                              @Param("dataMonth") Date dataMonth, 
                              @Param("idCardNumber") String idCardNumber, 
                              @Param("id") Long id);
}

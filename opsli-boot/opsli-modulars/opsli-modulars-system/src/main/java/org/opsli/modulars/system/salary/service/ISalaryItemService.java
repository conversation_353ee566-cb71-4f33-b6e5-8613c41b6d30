/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service;

import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.salary.entity.SalaryItem;
import org.opsli.api.wrapper.system.salary.SalaryItemModel;

import java.util.List;

/**
 * 薪资项目基础定义表 Service接口
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface ISalaryItemService extends CrudServiceInterface<SalaryItem, SalaryItemModel> {

    /**
     * 根据租户ID和分类查询薪资项目
     * @param tenantId 租户ID
     * @param category 分类
     * @return 薪资项目列表
     */
    List<SalaryItemModel> findByTenantIdAndCategory(Long tenantId, String category);

    /**
     * 根据租户ID查询启用的薪资项目
     * @param tenantId 租户ID
     * @return 薪资项目列表
     */
    List<SalaryItemModel> findEnabledByTenantId(Long tenantId);

    /**
     * 检查薪资项目名称是否唯一
     * @param tenantId 租户ID
     * @param name 薪资项目名称
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否唯一
     */
    boolean checkNameUnique(Long tenantId, String name, String excludeId);

    /**
     * 根据租户ID和数据类型查询薪资项目
     * @param tenantId 租户ID
     * @param dataType 数据类型
     * @return 薪资项目列表
     */
    List<SalaryItemModel> findByTenantIdAndDataType(Long tenantId, String dataType);

    /**
     * 批量启用/禁用薪资项目
     * @param ids ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> ids, Boolean status);

    /**
     * 初始化系统内置薪资项目
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean initSystemSalaryItems(Long tenantId);

    /**
     * 验证薪资项目计算公式
     * @param formula 计算公式
     * @return 验证结果
     */
    boolean validateCalculationFormula(String formula);
}

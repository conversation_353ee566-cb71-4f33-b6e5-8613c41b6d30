/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service;

import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.salary.entity.SalaryTemplateItemMonthly;
import org.opsli.api.wrapper.system.salary.SalaryTemplateItemMonthlyModel;

import java.util.Date;
import java.util.List;

/**
 * 薪资模板项目关联表 Service接口
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface ISalaryTemplateItemMonthlyService extends CrudServiceInterface<SalaryTemplateItemMonthly, SalaryTemplateItemMonthlyModel> {

    /**
     * 根据租户ID、数据月份和模板ID查询模板项目（按显示顺序排序）
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return 模板项目列表
     */
    List<SalaryTemplateItemMonthlyModel> findByTenantIdAndDataMonthAndTemplateId(Long tenantId, Date dataMonth, Long templateId);

    /**
     * 根据租户ID、数据月份和模板ID查询启用的模板项目（按显示顺序排序）
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return 启用的模板项目列表
     */
    List<SalaryTemplateItemMonthlyModel> findEnabledByTenantIdAndDataMonthAndTemplateId(Long tenantId, Date dataMonth, Long templateId);

    /**
     * 根据薪资项目ID查询关联的模板项目
     * @param salaryItemId 薪资项目ID
     * @return 模板项目列表
     */
    List<SalaryTemplateItemMonthlyModel> findBySalaryItemId(Long salaryItemId);

    /**
     * 检查模板项目是否已存在
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @param salaryItemId 薪资项目ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否已存在
     */
    boolean checkTemplateItemExists(Long tenantId, Date dataMonth, Long templateId, Long salaryItemId, String excludeId);

    /**
     * 批量保存模板项目配置
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateItems 模板项目列表
     * @return 是否成功
     */
    boolean batchSaveTemplateItems(Long templateId, Long tenantId, Date dataMonth, List<SalaryTemplateItemMonthlyModel> templateItems);

    /**
     * 删除模板的所有项目（软删除）
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 是否成功
     */
    boolean deleteByTemplateId(Long templateId, Long tenantId, Date dataMonth);

    /**
     * 物理删除模板的所有项目
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 是否成功
     */
    boolean hardDeleteByTemplateId(Long templateId, Long tenantId, Date dataMonth);

    /**
     * 更新显示顺序
     * @param templateItemIds 模板项目ID列表（按新顺序排列）
     * @return 是否成功
     */
    boolean updateDisplayOrder(List<String> templateItemIds);

    /**
     * 批量启用/禁用模板项目
     * @param ids ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<String> ids, Boolean status);

    /**
     * 复制模板项目到新模板
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 是否成功
     */
    boolean copyTemplateItems(Long sourceTemplateId, Long targetTemplateId, Long tenantId, Date dataMonth);

    /**
     * 获取模板项目的最大显示顺序
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return 最大显示顺序
     */
    Integer getMaxDisplayOrder(Long templateId, Long tenantId, Date dataMonth);
}
/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.wrapper.system.salary.PositionSalaryStandardMonthlyModel;
import org.opsli.api.wrapper.system.salary.SalaryTemplateItemMonthlyModel;
import org.opsli.common.exception.ServiceException;
import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.core.utils.UserUtil;
import org.opsli.modulars.system.salary.entity.PositionSalaryStandardMonthly;
import org.opsli.modulars.system.salary.mapper.PositionSalaryStandardMonthlyMapper;
import org.opsli.modulars.system.salary.service.IPositionSalaryStandardMonthlyService;
import org.opsli.modulars.system.salary.service.ISalaryTemplateItemMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 岗位薪资标准月度数据表 Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Service
public class PositionSalaryStandardMonthlyServiceImpl extends CrudServiceImpl<PositionSalaryStandardMonthlyMapper, PositionSalaryStandardMonthly, PositionSalaryStandardMonthlyModel> 
        implements IPositionSalaryStandardMonthlyService {

    @Autowired(required = false)
    private PositionSalaryStandardMonthlyMapper mapper;

    @Autowired(required = false)
    private ISalaryTemplateItemMonthlyService salaryTemplateItemService;

    @Override
    public List<PositionSalaryStandardMonthlyModel> findByTenantIdAndDataMonthAndPositionId(Long tenantId, Date dataMonth, Long positionId) {
        if (tenantId == null || dataMonth == null || positionId == null) {
            return CollUtil.newArrayList();
        }
        
        List<PositionSalaryStandardMonthly> entityList = mapper.findByTenantIdAndDataMonthAndPositionId(tenantId, dataMonth, positionId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<PositionSalaryStandardMonthlyModel> findByTenantIdAndDataMonthAndTemplateId(Long tenantId, Date dataMonth, Long templateId) {
        if (tenantId == null || dataMonth == null || templateId == null) {
            return CollUtil.newArrayList();
        }
        
        List<PositionSalaryStandardMonthly> entityList = mapper.findByTenantIdAndDataMonthAndTemplateId(tenantId, dataMonth, templateId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<PositionSalaryStandardMonthlyModel> findByPositionIdAndTemplateIdAndDataMonth(Long positionId, Long templateId, Date dataMonth) {
        if (positionId == null || templateId == null || dataMonth == null) {
            return CollUtil.newArrayList();
        }
        
        List<PositionSalaryStandardMonthly> entityList = mapper.findByPositionIdAndTemplateIdAndDataMonth(positionId, templateId, dataMonth);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<PositionSalaryStandardMonthlyModel> findBySalaryItemId(Long salaryItemId) {
        if (salaryItemId == null) {
            return CollUtil.newArrayList();
        }
        
        List<PositionSalaryStandardMonthly> entityList = mapper.findBySalaryItemId(salaryItemId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public PositionSalaryStandardMonthlyModel findByPositionIdAndSalaryItemIdAndDataMonth(Long positionId, Long salaryItemId, Date dataMonth) {
        if (positionId == null || salaryItemId == null || dataMonth == null) {
            return null;
        }
        
        PositionSalaryStandardMonthly entity = mapper.findByPositionIdAndSalaryItemIdAndDataMonth(positionId, salaryItemId, dataMonth);
        return super.transformT2M(entity);
    }

    @Override
    public boolean checkPositionStandardExists(Long tenantId, Date dataMonth, Long positionId, Long templateId, Long salaryItemId, String excludeId) {
        if (tenantId == null || dataMonth == null || positionId == null || templateId == null || salaryItemId == null) {
            return false;
        }
        
        int count = mapper.checkPositionStandardExists(tenantId, dataMonth, positionId, templateId, salaryItemId, excludeId);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSavePositionStandards(Long positionId, Long templateId, Long tenantId, Date dataMonth, List<PositionSalaryStandardMonthlyModel> standards) {
        if (positionId == null || templateId == null || tenantId == null || dataMonth == null || CollUtil.isEmpty(standards)) {
            return false;
        }

        try {
            // 先删除原有的岗位标准
            this.deleteByPositionIdAndTemplateId(positionId, templateId, tenantId, dataMonth);

            // 批量保存新的岗位标准
            for (PositionSalaryStandardMonthlyModel model : standards) {
                model.setPositionId(positionId);
                model.setTemplateId(templateId);
                model.setTenantId(tenantId);
                model.setDataMonth(dataMonth);
                
                // 设置默认值
                if (model.getIsAutoFill() == null) {
                    model.setIsAutoFill(true);
                }
                if (model.getIsReadonly() == null) {
                    model.setIsReadonly(false);
                }
                
                super.insert(model);
            }
            
            return true;
        } catch (Exception e) {
            log.error("批量保存岗位薪资标准失败", e);
            throw new ServiceException(500, "批量保存岗位薪资标准失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPositionIdAndTemplateId(Long positionId, Long templateId, Long tenantId, Date dataMonth) {
        if (positionId == null || templateId == null || tenantId == null || dataMonth == null) {
            return false;
        }
        
        try {
            int result = mapper.deleteByPositionIdAndTemplateId(positionId, templateId, tenantId, dataMonth);
            return result >= 0;
        } catch (Exception e) {
            log.error("删除岗位薪资标准失败", e);
            throw new ServiceException(500, "删除岗位薪资标准失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateAutoFill(List<String> ids, Boolean isAutoFill) {
        if (CollUtil.isEmpty(ids) || isAutoFill == null) {
            return false;
        }

        try {
            UpdateWrapper<PositionSalaryStandardMonthly> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", ids);
            updateWrapper.set("is_auto_fill", isAutoFill);
            updateWrapper.set("update_time", new Date());
            updateWrapper.set("update_by", UserUtil.getUser().getUsername());
            
            return super.update(updateWrapper);
        } catch (Exception e) {
            log.error("批量更新自动预填充状态失败", e);
            throw new ServiceException(500, "批量更新自动预填充状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateReadonly(List<String> ids, Boolean isReadonly) {
        if (CollUtil.isEmpty(ids) || isReadonly == null) {
            return false;
        }

        try {
            UpdateWrapper<PositionSalaryStandardMonthly> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", ids);
            updateWrapper.set("is_readonly", isReadonly);
            updateWrapper.set("update_time", new Date());
            updateWrapper.set("update_by", UserUtil.getUser().getUsername());
            
            return super.update(updateWrapper);
        } catch (Exception e) {
            log.error("批量更新只读状态失败", e);
            throw new ServiceException(500, "批量更新只读状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyPositionStandards(Long sourcePositionId, Long sourceTemplateId, Long targetPositionId, Long targetTemplateId, Long tenantId, Date dataMonth) {
        if (sourcePositionId == null || sourceTemplateId == null || targetPositionId == null || targetTemplateId == null || tenantId == null || dataMonth == null) {
            return false;
        }

        try {
            // 查询源岗位标准
            List<PositionSalaryStandardMonthlyModel> sourceStandards = this.findByPositionIdAndTemplateIdAndDataMonth(sourcePositionId, sourceTemplateId, dataMonth);
            
            if (CollUtil.isEmpty(sourceStandards)) {
                return true; // 源岗位没有标准，复制成功
            }

            // 复制到目标岗位
            for (PositionSalaryStandardMonthlyModel sourceStandard : sourceStandards) {
                sourceStandard.setId(null); // 清空ID，让系统自动生成
                sourceStandard.setPositionId(targetPositionId);
                sourceStandard.setTemplateId(targetTemplateId);
                sourceStandard.setCreateTime(null);
                sourceStandard.setUpdateTime(null);
                sourceStandard.setCreateBy(null);
                sourceStandard.setUpdateBy(null);
                sourceStandard.setVersion(0);
                
                super.insert(sourceStandard);
            }
            
            return true;
        } catch (Exception e) {
            log.error("复制岗位薪资标准失败", e);
            throw new ServiceException(500, "复制岗位薪资标准失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyToNewMonth(Long positionId, Long templateId, Long tenantId, Date sourceMonth, Date targetMonth) {
        if (positionId == null || templateId == null || tenantId == null || sourceMonth == null || targetMonth == null) {
            return false;
        }

        try {
            // 查询源月份标准
            List<PositionSalaryStandardMonthlyModel> sourceStandards = this.findByPositionIdAndTemplateIdAndDataMonth(positionId, templateId, sourceMonth);
            
            if (CollUtil.isEmpty(sourceStandards)) {
                return true; // 源月份没有标准，复制成功
            }

            // 复制到目标月份
            for (PositionSalaryStandardMonthlyModel sourceStandard : sourceStandards) {
                // 检查目标月份是否已存在
                if (!this.checkPositionStandardExists(tenantId, targetMonth, positionId, templateId, sourceStandard.getSalaryItemId(), null)) {
                    sourceStandard.setId(null); // 清空ID，让系统自动生成
                    sourceStandard.setDataMonth(targetMonth);
                    sourceStandard.setCreateTime(null);
                    sourceStandard.setUpdateTime(null);
                    sourceStandard.setCreateBy(null);
                    sourceStandard.setUpdateBy(null);
                    sourceStandard.setVersion(0);
                    
                    super.insert(sourceStandard);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("复制岗位薪资标准到新月份失败", e);
            throw new ServiceException(500, "复制岗位薪资标准到新月份失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoCreateFromTemplateItems(Long positionId, Long templateId, Long tenantId, Date dataMonth) {
        if (positionId == null || templateId == null || tenantId == null || dataMonth == null) {
            return false;
        }

        try {
            // 查询模板项目
            List<SalaryTemplateItemMonthlyModel> templateItems = salaryTemplateItemService.findByTenantIdAndDataMonthAndTemplateId(tenantId, dataMonth, templateId);
            
            if (CollUtil.isEmpty(templateItems)) {
                return true; // 模板没有项目，创建成功
            }

            // 根据模板项目创建岗位标准
            for (SalaryTemplateItemMonthlyModel templateItem : templateItems) {
                // 检查是否已存在
                if (!this.checkPositionStandardExists(tenantId, dataMonth, positionId, templateId, templateItem.getSalaryItemId(), null)) {
                    PositionSalaryStandardMonthlyModel model = new PositionSalaryStandardMonthlyModel();
                    model.setTenantId(tenantId);
                    model.setDataMonth(dataMonth);
                    model.setPositionId(positionId);
                    model.setTemplateId(templateId);
                    model.setSalaryItemId(templateItem.getSalaryItemId());
                    model.setStandardValue(templateItem.getDefaultValue());
                    model.setIsAutoFill(true);
                    model.setIsReadonly(false);
                    model.setDescription("根据模板项目自动创建");
                    
                    super.insert(model);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("根据模板项目自动创建岗位标准失败", e);
            throw new ServiceException(500, "根据模板项目自动创建岗位标准失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchImportStandards(Long tenantId, Date dataMonth, List<PositionSalaryStandardMonthlyModel> standards) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        if (tenantId == null || dataMonth == null || CollUtil.isEmpty(standards)) {
            result.put("success", false);
            result.put("message", "参数不能为空");
            return result;
        }

        try {
            for (int i = 0; i < standards.size(); i++) {
                try {
                    PositionSalaryStandardMonthlyModel model = standards.get(i);
                    model.setTenantId(tenantId);
                    model.setDataMonth(dataMonth);
                    
                    // 设置默认值
                    if (model.getIsAutoFill() == null) {
                        model.setIsAutoFill(true);
                    }
                    if (model.getIsReadonly() == null) {
                        model.setIsReadonly(false);
                    }
                    
                    // 验证标准值范围
                    if (!this.validateValueRange(model.getStandardValue(), model.getMinValue(), model.getMaxValue())) {
                        throw new ServiceException(400, "标准值不在有效范围内");
                    }
                    
                    super.insert(model);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMessages.add("第" + (i + 1) + "行导入失败: " + e.getMessage());
                }
            }
            
            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errorMessages", errorMessages);
            
            return result;
        } catch (Exception e) {
            log.error("批量导入岗位薪资标准失败", e);
            result.put("success", false);
            result.put("message", "批量导入失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public boolean validateValueRange(String standardValue, String minValue, String maxValue) {
        if (StrUtil.isBlank(standardValue)) {
            return true; // 标准值为空，不需要验证
        }
        
        try {
            BigDecimal standard = new BigDecimal(standardValue);
            
            // 验证最小值
            if (StrUtil.isNotBlank(minValue)) {
                BigDecimal min = new BigDecimal(minValue);
                if (standard.compareTo(min) < 0) {
                    return false;
                }
            }
            
            // 验证最大值
            if (StrUtil.isNotBlank(maxValue)) {
                BigDecimal max = new BigDecimal(maxValue);
                if (standard.compareTo(max) > 0) {
                    return false;
                }
            }
            
            return true;
        } catch (NumberFormatException e) {
            // 如果不是数值类型，跳过范围验证
            return true;
        }
    }

    @Override
    public Map<String, Object> getPositionStandardStatistics(Long tenantId, Date dataMonth, Long positionId) {
        if (tenantId == null || dataMonth == null || positionId == null) {
            return new HashMap<>();
        }
        
        return mapper.getPositionStandardStatistics(tenantId, dataMonth, positionId);
    }

    @Override
    protected void beforeInsert(PositionSalaryStandardMonthlyModel model, PositionSalaryStandardMonthly entity) {
        // 检查岗位薪资标准是否已存在
        if (this.checkPositionStandardExists(model.getTenantId(), model.getDataMonth(), 
                model.getPositionId(), model.getTemplateId(), model.getSalaryItemId(), null)) {
            throw new ServiceException(400, "该岗位的薪资项目标准已存在");
        }
        
        // 验证标准值范围
        if (!this.validateValueRange(model.getStandardValue(), model.getMinValue(), model.getMaxValue())) {
            throw new ServiceException(400, "标准值不在有效范围内");
        }
        
        // 设置默认值
        if (model.getIsAutoFill() == null) {
            model.setIsAutoFill(true);
        }
        if (model.getIsReadonly() == null) {
            model.setIsReadonly(false);
        }
    }

    @Override
    protected void beforeUpdate(PositionSalaryStandardMonthlyModel model, PositionSalaryStandardMonthly entity) {
        // 检查岗位薪资标准是否已存在（排除当前记录）
        if (this.checkPositionStandardExists(model.getTenantId(), model.getDataMonth(), 
                model.getPositionId(), model.getTemplateId(), model.getSalaryItemId(), model.getId())) {
            throw new ServiceException(400, "该岗位的薪资项目标准已存在");
        }
        
        // 验证标准值范围
        if (!this.validateValueRange(model.getStandardValue(), model.getMinValue(), model.getMaxValue())) {
            throw new ServiceException(400, "标准值不在有效范围内");
        }
    }
}
/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.opsli.api.wrapper.system.salary.SalaryItemModel;
import org.opsli.common.exception.ServiceException;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.modulars.system.salary.entity.SalaryItem;
import org.opsli.modulars.system.salary.mapper.SalaryItemMapper;
import org.opsli.modulars.system.salary.service.ISalaryItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 薪资项目基础定义表 Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class SalaryItemServiceImpl extends CrudServiceImpl<SalaryItemMapper, SalaryItem, SalaryItemModel>
        implements ISalaryItemService {

    @Autowired(required = false)
    private SalaryItemMapper mapper;

    @Override
    public List<SalaryItemModel> findByTenantIdAndCategory(Long tenantId, String category) {
        List<SalaryItem> entityList = mapper.findByTenantIdAndCategory(tenantId, category);
        return WrapperUtil.transformInstance(entityList, SalaryItemModel.class);
    }

    @Override
    public List<SalaryItemModel> findEnabledByTenantId(Long tenantId) {
        List<SalaryItem> entityList = mapper.findEnabledByTenantId(tenantId);
        return WrapperUtil.transformInstance(entityList, SalaryItemModel.class);
    }

    @Override
    public boolean checkNameUnique(Long tenantId, String name, String excludeId) {
        int count = mapper.checkNameUnique(tenantId, name, excludeId);
        return count == 0;
    }

    @Override
    public List<SalaryItemModel> findByTenantIdAndDataType(Long tenantId, String dataType) {
        List<SalaryItem> entityList = mapper.findByTenantIdAndDataType(tenantId, dataType);
        return WrapperUtil.transformInstance(entityList, SalaryItemModel.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, Boolean status) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        QueryWrapper<SalaryItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        
        SalaryItem updateEntity = new SalaryItem();
        updateEntity.setStatus(status);
        
        return super.update(updateEntity, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initSystemSalaryItems(Long tenantId) {
        // 检查是否已经初始化过
        QueryWrapper<SalaryItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId)
                   .eq("is_system_item", true);
        long count = super.count(queryWrapper);
        
        if (count > 0) {
            throw new ServiceException("系统内置薪资项目已存在，无需重复初始化");
        }
        
        // 初始化系统内置薪资项目
        initBasicSalaryItems(tenantId);
        initAllowanceItems(tenantId);
        initCommissionItems(tenantId);
        initBonusItems(tenantId);
        initDeductionItems(tenantId);
        
        return true;
    }

    @Override
    public boolean validateCalculationFormula(String formula) {
        if (formula == null || formula.trim().isEmpty()) {
            return true; // 空公式认为是有效的
        }
        
        // TODO: 实现公式验证逻辑
        // 这里可以使用表达式引擎如SpEL、MVEL等来验证公式的语法正确性
        try {
            // 简单的语法检查
            if (formula.contains("${") && formula.contains("}")) {
                return true;
            }
            // 检查是否包含基本的数学运算符
            return formula.matches(".*[+\\-*/()\\d\\s]+.*");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 初始化基本工资项目
     */
    private void initBasicSalaryItems(Long tenantId) {
        createSystemItem(tenantId, "岗位工资", "基本工资", "decimal", "元", 2, null);
        createSystemItem(tenantId, "级数", "基本工资", "integer", "级", 0, null);
        createSystemItem(tenantId, "绩效占比", "基本工资", "percentage", "%", 2, null);
    }

    /**
     * 初始化补贴项目
     */
    private void initAllowanceItems(Long tenantId) {
        createSystemItem(tenantId, "交通补贴", "补贴", "decimal", "元", 2, null);
        createSystemItem(tenantId, "餐饮补贴", "补贴", "decimal", "元", 2, null);
        createSystemItem(tenantId, "通讯补贴", "补贴", "decimal", "元", 2, null);
    }

    /**
     * 初始化提成项目
     */
    private void initCommissionItems(Long tenantId) {
        createSystemItem(tenantId, "销售提成", "提成", "decimal", "元", 2, null);
        createSystemItem(tenantId, "业绩提成", "提成", "decimal", "元", 2, null);
        createSystemItem(tenantId, "团队提成", "提成", "decimal", "元", 2, null);
    }

    /**
     * 初始化奖金项目
     */
    private void initBonusItems(Long tenantId) {
        createSystemItem(tenantId, "季度奖金", "奖金", "decimal", "元", 2, null);
        createSystemItem(tenantId, "年终奖金", "奖金", "decimal", "元", 2, null);
        createSystemItem(tenantId, "专项奖金", "奖金", "decimal", "元", 2, null);
    }

    /**
     * 初始化扣除项目
     */
    private void initDeductionItems(Long tenantId) {
        createSystemItem(tenantId, "社保扣除", "扣除", "decimal", "元", 2, null);
        createSystemItem(tenantId, "公积金扣除", "扣除", "decimal", "元", 2, null);
        createSystemItem(tenantId, "个税扣除", "扣除", "decimal", "元", 2, null);
    }

    /**
     * 创建系统内置薪资项目
     */
    private void createSystemItem(Long tenantId, String name, String category, String dataType, 
                                 String unit, Integer decimalPlaces, String formula) {
        SalaryItemModel model = new SalaryItemModel();
        model.setTenantId(tenantId);
        model.setName(name);
        model.setCategory(category);
        model.setDataType(dataType);
        model.setUnit(unit);
        model.setDecimalPlaces(decimalPlaces);
        model.setCalculationFormula(formula);
        model.setIsSystemItem(true);
        model.setStatus(true);
        
        super.insert(model);
    }
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.employee.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opsli.api.wrapper.system.employee.EmployeeModel;
import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.modulars.system.employee.entity.EmployeeMonthly;
import org.opsli.modulars.system.employee.mapper.EmployeeMapper;
import org.opsli.modulars.system.employee.service.IEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 员工管理 - 按月份 Service 实现类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@Service
public class EmployeeServiceImpl extends CrudServiceImpl<EmployeeMapper, EmployeeMonthly, EmployeeModel>
        implements IEmployeeService {

    @Autowired(required = false)
    private EmployeeMapper mapper;

    @Override
    public boolean checkEmployeeNumberUnique(EmployeeModel model) {
        if (model == null) {
            return false;
        }
        
        Long id = null;
        if (StringUtils.isNotBlank(model.getId())) {
            id = Long.valueOf(model.getId());
        }
        
        int count = mapper.checkEmployeeNumberUnique(
                model.getTenantId(), 
                model.getDataMonth(), 
                model.getEmployeeNumber(), 
                id);
        
        return count == 0;
    }

    @Override
    public boolean checkIdCardNumberUnique(EmployeeModel model) {
        if (model == null) {
            return false;
        }
        
        Long id = null;
        if (StringUtils.isNotBlank(model.getId())) {
            id = Long.valueOf(model.getId());
        }
        
        int count = mapper.checkIdCardNumberUnique(
                model.getTenantId(), 
                model.getDataMonth(), 
                model.getIdCardNumber(), 
                id);
        
        return count == 0;
    }

    @Override
    public List<EmployeeModel> findByDepartmentId(Long tenantId, Date dataMonth, Long departmentId) {
        List<EmployeeMonthly> entityList = mapper.findByDepartmentId(tenantId, dataMonth, departmentId);
        return WrapperUtil.transformInstance(entityList, getModelClass());
    }

    @Override
    public List<EmployeeModel> findByTenantAndMonth(Long tenantId, Date dataMonth) {
        List<EmployeeMonthly> entityList = mapper.findByTenantAndMonth(tenantId, dataMonth);
        return WrapperUtil.transformInstance(entityList, getModelClass());
    }

    @Override
    public List<EmployeeModel> findByPositionId(Long tenantId, Date dataMonth, Long positionId) {
        List<EmployeeMonthly> entityList = mapper.findByPositionId(tenantId, dataMonth, positionId);
        return WrapperUtil.transformInstance(entityList, getModelClass());
    }
}

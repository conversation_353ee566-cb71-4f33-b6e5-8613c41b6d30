/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.organizationChart.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opsli.api.wrapper.system.department.DepartmentModel;
import org.opsli.api.wrapper.system.employee.EmployeeModel;
import org.opsli.api.wrapper.system.organizationChart.OrganizationChartModel;
import org.opsli.api.wrapper.system.position.PositionModel;
import org.opsli.modulars.system.department.service.IDepartmentService;
import org.opsli.modulars.system.employee.service.IEmployeeService;
import org.opsli.modulars.system.organizationChart.service.IOrganizationChartService;
import org.opsli.modulars.system.position.service.IPositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织架构图 Service 实现类
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@Service
public class OrganizationChartServiceImpl implements IOrganizationChartService {

    @Autowired
    private IDepartmentService departmentService;
    
    @Autowired
    private IPositionService positionService;
    
    @Autowired
    private IEmployeeService employeeService;

    @Override
    public List<OrganizationChartModel> getOrganizationTree(Long tenantId, Date dataMonth) {
        // 获取所有部门数据
        List<DepartmentModel> departments = getAllDepartments(tenantId, dataMonth);
        
        // 构建部门树
        List<OrganizationChartModel> departmentTree = buildDepartmentTree(departments);
        
        // 为每个部门添加岗位和员工, 并计算人数
        enrichTreeWithPositionsAndEmployees(departmentTree, tenantId, dataMonth);
        
        return departmentTree;
    }

    @Override
    public List<OrganizationChartModel> getDepartmentTree(Long tenantId, Date dataMonth, Long departmentId) {
        // 获取指定部门及其子部门
        List<DepartmentModel> departments = getDepartmentAndChildren(tenantId, dataMonth, departmentId);
        
        // 构建部门树
        List<OrganizationChartModel> departmentTree = buildDepartmentTree(departments);
        
        // 为每个部门添加岗位和员工, 并计算人数
        enrichTreeWithPositionsAndEmployees(departmentTree, tenantId, dataMonth);
        
        return departmentTree;
    }

    @Override
    public OrganizationChartModel getOrganizationStats(Long tenantId, Date dataMonth) {
        OrganizationChartModel stats = new OrganizationChartModel();
        
        // 获取统计数据
        List<DepartmentModel> departments = getAllDepartments(tenantId, dataMonth);
        List<PositionModel> positions = getAllPositions(tenantId, dataMonth);
        List<EmployeeModel> employees = getAllEmployees(tenantId, dataMonth);
        
        // 设置统计信息
        stats.setNodeType("stats");
        stats.setNodeName("组织架构统计");
        stats.setDirectSubordinates(departments.size());
        stats.setTotalSubordinates(departments.size() + positions.size() + employees.size());
        stats.setDepartmentEmployeeCount(employees.size());
        stats.setPositionEmployeeCount(positions.size());
        
        return stats;
    }

    @Override
    public List<OrganizationChartModel> searchNodes(Long tenantId, Date dataMonth, String keyword, String nodeType) {
        List<OrganizationChartModel> results = new ArrayList<>();
        
        if (StringUtils.isBlank(keyword)) {
            return results;
        }
        
        // 根据节点类型搜索
        if (nodeType == null || "department".equals(nodeType)) {
            results.addAll(searchDepartments(tenantId, dataMonth, keyword));
        }
        
        if (nodeType == null || "position".equals(nodeType)) {
            results.addAll(searchPositions(tenantId, dataMonth, keyword));
        }
        
        if (nodeType == null || "employee".equals(nodeType)) {
            results.addAll(searchEmployees(tenantId, dataMonth, keyword));
        }
        
        return results;
    }

    /**
     * 获取所有部门
     */
    private List<DepartmentModel> getAllDepartments(Long tenantId, Date dataMonth) {
        try {
            // 获取根部门（parentId为null的部门）
            List<DepartmentModel> rootDepartments = departmentService.findByParentId(tenantId, dataMonth, null);
            List<DepartmentModel> allDepartments = new ArrayList<>(rootDepartments);

            // 递归获取所有子部门
            for (DepartmentModel dept : rootDepartments) {
                if (dept.getId() != null) {
                    allDepartments.addAll(getAllChildDepartments(tenantId, dataMonth, Long.valueOf(dept.getId())));
                }
            }

            return allDepartments;
        } catch (Exception e) {
            log.error("获取部门数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 递归获取所有子部门
     */
    private List<DepartmentModel> getAllChildDepartments(Long tenantId, Date dataMonth, Long parentId) {
        List<DepartmentModel> allChildren = new ArrayList<>();
        List<DepartmentModel> children = departmentService.findByParentId(tenantId, dataMonth, parentId);

        allChildren.addAll(children);

        for (DepartmentModel child : children) {
            if (child.getId() != null) {
                allChildren.addAll(getAllChildDepartments(tenantId, dataMonth, Long.valueOf(child.getId())));
            }
        }

        return allChildren;
    }

    /**
     * 获取所有岗位
     */
    private List<PositionModel> getAllPositions(Long tenantId, Date dataMonth) {
        try {
            // 通过一次查询获取所有岗位，避免N+1问题
            return positionService.findByTenantAndMonth(tenantId, dataMonth);
        } catch (Exception e) {
            log.error("获取岗位数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有员工
     */
    private List<EmployeeModel> getAllEmployees(Long tenantId, Date dataMonth) {
        try {
            // 通过一次查询获取所有员工，避免N+1问题
            return employeeService.findByTenantAndMonth(tenantId, dataMonth);
        } catch (Exception e) {
            log.error("获取员工数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建部门树
     */
    private List<OrganizationChartModel> buildDepartmentTree(List<DepartmentModel> departments) {
        List<OrganizationChartModel> tree = new ArrayList<>();
        Map<Long, OrganizationChartModel> nodeMap = new HashMap<>();
        
        // 转换为组织架构节点
        for (DepartmentModel dept : departments) {
            OrganizationChartModel node = convertDepartmentToNode(dept);
            nodeMap.put(dept.getId() != null ? Long.valueOf(dept.getId()) : null, node);
        }
        
        // 构建树形结构
        for (OrganizationChartModel node : nodeMap.values()) {
            if (node.getParentId() == null) {
                tree.add(node);
            } else {
                OrganizationChartModel parent = nodeMap.get(node.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(node);
                }
            }
        }
        
        return tree;
    }

    /**
     * 为树添加岗位和员工信息, 并计算各级人数 (入口方法)
     */
    private void enrichTreeWithPositionsAndEmployees(List<OrganizationChartModel> nodes, Long tenantId, Date dataMonth) {
        // 1. 一次性获取所有岗位和员工数据
        List<PositionModel> allPositions = getAllPositions(tenantId, dataMonth);
        List<EmployeeModel> allEmployees = getAllEmployees(tenantId, dataMonth);

        // 2. 将员工按岗位ID分组
        Map<String, List<EmployeeModel>> employeesByPosition = allEmployees.stream()
                .filter(e -> e.getPositionId() != null)
                .collect(Collectors.groupingBy(e -> String.valueOf(e.getPositionId())));

        // 3. 将岗位按部门ID分组
        Map<String, List<PositionModel>> positionsByDepartment = allPositions.stream()
                .filter(p -> p.getDepartmentId() != null)
                .collect(Collectors.groupingBy(p -> String.valueOf(p.getDepartmentId())));

        // 4. 递归处理树
        recursiveEnrichTree(nodes, positionsByDepartment, employeesByPosition);
    }

    /**
     * 递归为树添加岗位和员工信息, 并计算各级人数
     */
    private void recursiveEnrichTree(List<OrganizationChartModel> nodes, Map<String, List<PositionModel>> positionsByDepartment, Map<String, List<EmployeeModel>> employeesByPosition) {
        for (OrganizationChartModel deptNode : nodes) {
            if (!"department".equals(deptNode.getNodeType())) {
                continue;
            }

            // 递归处理子部门
            if (deptNode.getChildren() != null && !deptNode.getChildren().isEmpty()) {
                recursiveEnrichTree(deptNode.getChildren(), positionsByDepartment, employeesByPosition);
            }

            // 添加岗位到部门
            List<OrganizationChartModel> positionChildren = new ArrayList<>();
            List<PositionModel> positionsInDept = positionsByDepartment.getOrDefault(deptNode.getNodeId().toString(), Collections.emptyList());
            for (PositionModel position : positionsInDept) {
                OrganizationChartModel posNode = convertPositionToNode(position);
                List<EmployeeModel> employeesInPos = employeesByPosition.getOrDefault(posNode.getNodeId().toString(), Collections.emptyList());

                List<OrganizationChartModel> employeeNodes = employeesInPos.stream()
                        .map(this::convertEmployeeToNode)
                        .collect(Collectors.toList());

                posNode.setChildren(employeeNodes);
                posNode.setPositionEmployeeCount(employeeNodes.size());
                positionChildren.add(posNode);
            }

            // 将岗位节点添加到部门的子节点中
            if (deptNode.getChildren() == null) {
                deptNode.setChildren(new ArrayList<>());
            }
            deptNode.getChildren().addAll(positionChildren);

            // 计算部门总人数
            int departmentHeadcount = 0;
            if (deptNode.getChildren() != null) {
                for (OrganizationChartModel child : deptNode.getChildren()) {
                    if ("department".equals(child.getNodeType())) {
                        departmentHeadcount += Optional.ofNullable(child.getDepartmentEmployeeCount()).orElse(0);
                    } else if ("position".equals(child.getNodeType())) {
                        departmentHeadcount += Optional.ofNullable(child.getPositionEmployeeCount()).orElse(0);
                    }
                }
            }
            deptNode.setDepartmentEmployeeCount(departmentHeadcount);
        }
    }

    /**
     * 转换部门为组织架构节点
     */
    private OrganizationChartModel convertDepartmentToNode(DepartmentModel dept) {
        OrganizationChartModel node = new OrganizationChartModel();
        node.setNodeType("department");
        node.setNodeId(dept.getId() != null ? Long.valueOf(dept.getId()) : null);
        node.setNodeName(dept.getName());
        node.setNodeCode(dept.getDeptCode());
        node.setParentId(dept.getParentDepartmentId());
        node.setParentType("department");
        node.setStatus(dept.getStatus());
        node.setLevel(0); // 需要计算层级
        node.setSort(0);
        node.setDepartmentDescription(dept.getResponsibilities());
        node.setHeadcount(dept.getHeadcount());
        return node;
    }

    /**
     * 转换岗位为组织架构节点
     */
    private OrganizationChartModel convertPositionToNode(PositionModel position) {
        OrganizationChartModel node = new OrganizationChartModel();
        node.setNodeType("position");
        node.setNodeId(position.getId() != null ? Long.valueOf(position.getId()) : null);
        node.setNodeName(position.getTitle());
        node.setNodeCode(position.getPositionCode());
        node.setParentId(position.getDepartmentId());
        node.setParentType("department");
        node.setStatus(position.getStatus());
        node.setLevel(1); // 岗位层级
        node.setSort(0);
        node.setPositionDescription(position.getDescription());
        node.setPositionRequirements(position.getRequirements());
        node.setPositionLevel(position.getPositionLevel());
        node.setHeadcount(position.getHeadcount());
        return node;
    }

    /**
     * 转换员工为组织架构节点
     */
    private OrganizationChartModel convertEmployeeToNode(EmployeeModel employee) {
        OrganizationChartModel node = new OrganizationChartModel();
        node.setNodeType("employee");
        node.setNodeId(employee.getId() != null ? Long.valueOf(employee.getId()) : null);
        node.setNodeName(employee.getFullName());
        node.setNodeCode(employee.getEmployeeNumber());
        node.setParentId(employee.getPositionId());
        node.setParentType("position");
        node.setStatus(1); // 员工默认启用
        node.setLevel(2); // 员工层级
        node.setSort(0);
        node.setEmployeeName(employee.getFullName());
        node.setEmployeeNumber(employee.getEmployeeNumber());
        node.setGender(employee.getGender());
        node.setPhoneNumber(employee.getPhoneNumber());
        node.setEmployeeStatus(employee.getStatus());
        
        // 格式化入职日期
        if (employee.getHireDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            node.setHireDate(sdf.format(employee.getHireDate()));
        }
        
        return node;
    }

    /**
     * 获取指定部门及其子部门
     */
    private List<DepartmentModel> getDepartmentAndChildren(Long tenantId, Date dataMonth, Long departmentId) {
        List<DepartmentModel> result = new ArrayList<>();

        // 获取指定部门
        DepartmentModel dept = new DepartmentModel();
        dept.setId(String.valueOf(departmentId));
        dept.setTenantId(tenantId);
        dept.setDataMonth(dataMonth);
        DepartmentModel targetDept = departmentService.get(dept);
        if (targetDept != null) {
            result.add(targetDept);
        }

        // 递归获取子部门
        List<DepartmentModel> children = departmentService.findByParentId(tenantId, dataMonth, departmentId);
        result.addAll(children);

        for (DepartmentModel child : children) {
            if (child.getId() != null) {
                result.addAll(getDepartmentAndChildren(tenantId, dataMonth, Long.valueOf(child.getId())));
            }
        }

        return result;
    }

    /**
     * 搜索部门
     */
    private List<OrganizationChartModel> searchDepartments(Long tenantId, Date dataMonth, String keyword) {
        List<OrganizationChartModel> results = new ArrayList<>();

        try {
            List<DepartmentModel> departments = getAllDepartments(tenantId, dataMonth);
            for (DepartmentModel dept : departments) {
                if (dept.getName() != null && dept.getName().contains(keyword) ||
                    dept.getDeptCode() != null && dept.getDeptCode().contains(keyword)) {
                    results.add(convertDepartmentToNode(dept));
                }
            }
        } catch (Exception e) {
            log.error("搜索部门失败", e);
        }

        return results;
    }

    /**
     * 搜索岗位
     */
    private List<OrganizationChartModel> searchPositions(Long tenantId, Date dataMonth, String keyword) {
        List<OrganizationChartModel> results = new ArrayList<>();

        try {
            List<PositionModel> positions = getAllPositions(tenantId, dataMonth);
            for (PositionModel position : positions) {
                if (position.getTitle() != null && position.getTitle().contains(keyword) ||
                    position.getPositionCode() != null && position.getPositionCode().contains(keyword)) {
                    results.add(convertPositionToNode(position));
                }
            }
        } catch (Exception e) {
            log.error("搜索岗位失败", e);
        }

        return results;
    }

    /**
     * 搜索员工
     */
    private List<OrganizationChartModel> searchEmployees(Long tenantId, Date dataMonth, String keyword) {
        List<OrganizationChartModel> results = new ArrayList<>();

        try {
            List<EmployeeModel> employees = getAllEmployees(tenantId, dataMonth);
            for (EmployeeModel employee : employees) {
                if (employee.getFullName() != null && employee.getFullName().contains(keyword) ||
                    employee.getEmployeeNumber() != null && employee.getEmployeeNumber().contains(keyword) ||
                    employee.getPhoneNumber() != null && employee.getPhoneNumber().contains(keyword)) {
                    results.add(convertEmployeeToNode(employee));
                }
            }
        } catch (Exception e) {
            log.error("搜索员工失败", e);
        }

        return results;
    }
}

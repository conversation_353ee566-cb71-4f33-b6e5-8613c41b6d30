/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.opsli.core.base.entity.BaseEntity;

/**
 * 薪资项目基础定义表
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("salary_items")
public class SalaryItem extends BaseEntity {

    /** 租户ID */
    private Long tenantId;
    
    /** 薪资项目名称 */
    private String name;
    
    /** 薪资项目分类 */
    private String category;
    
    /** 数据类型 */
    private String dataType;
    
    /** 单位 (如: 元, %, 天) */
    private String unit;
    
    /** 小数位数（仅用于decimal类型） */
    private Integer decimalPlaces;
    
    /** 计算公式（支持动态计算） */
    private String calculationFormula;
    
    /** 是否系统内置项目 */
    private Boolean isSystemItem;
    
    /** 项目状态, 1:启用, 0:停用 */
    private Boolean status;
    
    /** 版本号 */
    @Version
    private Integer version;
    
    /** 逻辑删除标识：0-未删除，1-已删除 */
    @TableLogic
    private Integer deleted;
    
    /** 创建人 */
    private String createBy;
    
    /** 创建时间 */
    private java.util.Date createTime;
    
    /** 更新人 */
    private String updateBy;
    
    /** 更新时间 */
    private java.util.Date updateTime;
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.position.mapper.PositionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="positionResultMap" type="org.opsli.modulars.system.position.entity.PositionMonthly">
        <id column="tenant_id" property="tenantId" />
        <id column="data_month" property="dataMonth" />
        <id column="id" property="id" />
        <result column="position_code" property="positionCode" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="requirements" property="requirements" />
        <result column="department_id" property="departmentId" />
        <result column="position_level" property="positionLevel" />
        <result column="headcount" property="headcount" />
        <result column="status" property="status" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        tenant_id, data_month, id, position_code, title, description, requirements, 
        department_id, position_level, headcount, status, version,
        deleted, create_by, create_time, update_by, update_time
    </sql>

    <!-- 根据部门ID查询职位列表 -->
    <select id="findByDepartmentId" resultMap="positionResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM positions_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND department_id = #{departmentId}
        AND deleted = 0
    </select>

    <!-- 检查职位编码是否唯一（在同一租户和月份下） -->
    <select id="checkCodeUnique" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM positions_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND position_code = #{positionCode}
        AND deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <!-- 检查职位名称是否唯一（在同一租户和月份下） -->
    <select id="checkTitleUnique" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM positions_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND title = #{title}
        AND deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <!-- 根据租户ID和月份查询岗位列表 -->
    <select id="findByTenantAndMonth" resultMap="positionResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM positions_monthly
        WHERE tenant_id = #{tenantId}
        AND data_month = #{dataMonth}
        AND deleted = 0
    </select>

</mapper>

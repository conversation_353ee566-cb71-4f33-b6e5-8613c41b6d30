/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.web;

import cn.hutool.core.convert.Convert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.salary.SalaryTemplateMonthlyApi;
import org.opsli.api.wrapper.system.salary.SalaryTemplateMonthlyModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.QueryBuilder;
import org.opsli.core.persistence.querybuilder.GenQueryBuilder;
import org.opsli.core.persistence.querybuilder.WebQueryBuilder;

import org.opsli.modulars.system.salary.entity.SalaryTemplateMonthly;
import org.opsli.modulars.system.salary.service.ISalaryTemplateMonthlyService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 薪资模板月度数据表 Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Tag(name = SalaryTemplateMonthlyApi.TITLE, description = SalaryTemplateMonthlyApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/salary/template")
public class SalaryTemplateMonthlyRestController extends BaseRestController<SalaryTemplateMonthly, SalaryTemplateMonthlyModel, ISalaryTemplateMonthlyService>
        implements SalaryTemplateMonthlyApi {

    /**
     * 从请求中获取租户ID
     * @return 租户ID
     */
    private Long getTenantIdFromRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return Convert.toLong(request.getParameter("tenantId"));
        }
        return null;
    }

    /**
     * 薪资模板 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条薪资模板数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<SalaryTemplateMonthlyModel> get(SalaryTemplateMonthlyModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 薪资模板 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, SalaryTemplateMonthlyModel request) {
        // MANUALLY GET HttpServletRequest for parameter map
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest httpRequest = attributes.getRequest();

        QueryBuilder<SalaryTemplateMonthly> queryBuilder = new WebQueryBuilder<>(IService.getEntityClass(), httpRequest.getParameterMap());
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SalaryTemplateMonthly> queryWrapper = queryBuilder.build();

        // Manually add dataMonth filter if it exists in the request parameters
        String dataMonthParam = httpRequest.getParameter("dataMonth");
        if (dataMonthParam != null && !dataMonthParam.trim().isEmpty()) {
            // Convert string to Date and add equality condition
            try {
                // Parse the date string (format: yyyy-MM)
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
                java.util.Date dataMonth = sdf.parse(dataMonthParam);
                queryWrapper.eq("data_month", dataMonth);
            } catch (java.text.ParseException e) {
                // If parsing fails, try to handle it as a string comparison
                queryWrapper.eq("data_month", dataMonthParam);
            }
        }

        Page<SalaryTemplateMonthly, SalaryTemplateMonthlyModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryWrapper);
        page = IService.findPage(page);
        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 薪资模板 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部薪资模板数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryTemplateMonthlyModel>> findAll(SalaryTemplateMonthlyModel request) {
        QueryBuilder<SalaryTemplateMonthly> queryBuilder = new GenQueryBuilder<>();
        List<SalaryTemplateMonthly> entityList = IService.findList(queryBuilder.build());
        List<SalaryTemplateMonthlyModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 薪资模板 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增薪资模板数据")
    @PreAuthorize("hasAuthority('system_salary_template_insert')")
    @OperateLogger(description = "新增薪资模板数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(SalaryTemplateMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增薪资模板数据成功");
    }

    /**
     * 薪资模板 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改薪资模板数据")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "修改薪资模板数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(SalaryTemplateMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改薪资模板数据成功");
    }

    /**
     * 薪资模板 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除薪资模板数据")
    @PreAuthorize("hasAuthority('system_salary_template_delete')")
    @OperateLogger(description = "删除薪资模板数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除薪资模板数据成功");
    }

    /**
     * 薪资模板 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除薪资模板数据")
    @PreAuthorize("hasAuthority('system_salary_template_delete')")
    @OperateLogger(description = "批量删除薪资模板数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除薪资模板数据成功");
    }

    /**
     * 根据数据月份查询薪资模板
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @Operation(summary = "根据数据月份查询薪资模板")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryTemplateMonthlyModel>> findByDataMonth(Date dataMonth) {
        // 从请求参数中获取租户ID
        Long tenantId = getTenantIdFromRequest();
        if (tenantId == null) {
            return ResultWrapper.getSuccessResultWrapper(java.util.Collections.emptyList());
        }
        List<SalaryTemplateMonthlyModel> modelList = IService.findByTenantIdAndDataMonth(tenantId, dataMonth);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 检查模板名称是否唯一
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查模板名称是否唯一")
    @Override
    public ResultWrapper<Boolean> checkNameUnique(SalaryTemplateMonthlyModel model) {
        // 从model中获取租户ID，如果没有则从请求参数获取
        Long tenantId = model.getTenantId();
        if (tenantId == null) {
            tenantId = getTenantIdFromRequest();
        }
        if (tenantId == null) {
            return ResultWrapper.getSuccessResultWrapper(false);
        }
        boolean isUnique = IService.checkNameUnique(tenantId, model.getDataMonth(), model.getName(), model.getId());
        return ResultWrapper.getSuccessResultWrapper(isUnique);
    }

    /**
     * 复制模板到新月份
     * @param sourceTemplateId 源模板ID
     * @param targetDataMonth 目标月份
     * @param newTemplateName 新模板名称
     * @return ResultWrapper
     */
    @Operation(summary = "复制模板到新月份")
    @PreAuthorize("hasAuthority('system_salary_template_insert')")
    @OperateLogger(description = "复制模板到新月份",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<SalaryTemplateMonthlyModel> copyToNewMonth(String sourceTemplateId, Date targetDataMonth, String newTemplateName) {
        // 演示模式 不允许操作
        super.demoError();
        
        SalaryTemplateMonthlyModel newTemplate = IService.copyTemplateToNewMonth(sourceTemplateId, targetDataMonth, newTemplateName);
        return ResultWrapper.getSuccessResultWrapper(newTemplate);
    }

    /**
     * 批量启用/禁用薪资模板
     * @param ids ID数组
     * @param status 状态
     * @return ResultWrapper
     */
    @Operation(summary = "批量启用/禁用薪资模板")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "批量启用/禁用薪资模板",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchUpdateStatus(String ids, Boolean status) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        List<String> idList = Convert.toList(String.class, idArray);
        IService.batchUpdateStatus(idList, status);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量更新薪资模板状态成功");
    }
}

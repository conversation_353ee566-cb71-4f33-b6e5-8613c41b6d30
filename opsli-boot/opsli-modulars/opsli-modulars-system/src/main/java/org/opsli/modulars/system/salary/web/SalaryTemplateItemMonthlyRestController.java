/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.web;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.web.system.salary.SalaryTemplateItemMonthlyApi;
import org.opsli.api.wrapper.system.salary.SalaryTemplateItemMonthlyModel;
import org.opsli.common.annotation.ApiRestController;
import org.opsli.common.utils.WrapperUtil;
import org.opsli.core.base.controller.BaseRestController;
import org.opsli.core.log.annotation.OperateLogger;
import org.opsli.core.log.enums.ModuleEnum;
import org.opsli.core.log.enums.OperationTypeEnum;
import org.opsli.core.persistence.Page;
import org.opsli.core.persistence.querybuilder.GenQueryBuilder;
import org.opsli.core.persistence.querybuilder.QueryBuilder;
import org.opsli.modulars.system.salary.entity.SalaryTemplateItemMonthly;
import org.opsli.modulars.system.salary.service.ISalaryTemplateItemMonthlyService;
import org.opsli.modulars.system.salary.utils.SalaryDateUtils;
import org.springframework.security.access.prepost.PreAuthorize;

import cn.hutool.core.convert.Convert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 薪资模板项目关联表 Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Tag(name = SalaryTemplateItemMonthlyApi.TITLE, description = SalaryTemplateItemMonthlyApi.SUB_TITLE)
@Slf4j
@ApiRestController("/{ver}/system/salary/templateItem")
public class SalaryTemplateItemMonthlyRestController extends BaseRestController<SalaryTemplateItemMonthly, SalaryTemplateItemMonthlyModel, ISalaryTemplateItemMonthlyService>
        implements SalaryTemplateItemMonthlyApi {

    /**
     * 薪资模板项目 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "获得单条薪资模板项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<SalaryTemplateItemMonthlyModel> get(SalaryTemplateItemMonthlyModel model) {
        model = IService.get(model);
        return ResultWrapper.getSuccessResultWrapper(model);
    }

    /**
     * 薪资模板项目 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得分页数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<?> findPage(Integer pageNo, Integer pageSize, SalaryTemplateItemMonthlyModel request) {
        QueryBuilder<SalaryTemplateItemMonthly> queryBuilder = new GenQueryBuilder<>();
        Page<SalaryTemplateItemMonthly, SalaryTemplateItemMonthlyModel> page = new Page<>(pageNo, pageSize);
        page.setQueryWrapper(queryBuilder.build());
        page = IService.findPage(page);
        return ResultWrapper.getSuccessResultWrapper(page.getPageData());
    }

    /**
     * 薪资模板项目 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @Operation(summary = "获得全部薪资模板项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findAll(SalaryTemplateItemMonthlyModel request) {
        QueryBuilder<SalaryTemplateItemMonthly> queryBuilder = new GenQueryBuilder<>();
        List<SalaryTemplateItemMonthly> entityList = IService.findList(queryBuilder.build());
        List<SalaryTemplateItemMonthlyModel> modelList = WrapperUtil.transformInstance(entityList, IService.getModelClass());
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 薪资模板项目 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "新增薪资模板项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_insert')")
    @OperateLogger(description = "新增薪资模板项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> insert(SalaryTemplateItemMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.insert(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("新增薪资模板项目数据成功");
    }

    /**
     * 薪资模板项目 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "修改薪资模板项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "修改薪资模板项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> update(SalaryTemplateItemMonthlyModel model) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.update(model);
        return ResultWrapper.getSuccessResultWrapperByMsg("修改薪资模板项目数据成功");
    }

    /**
     * 薪资模板项目 删除
     * @param id ID
     * @return ResultWrapper
     */
    @Operation(summary = "删除薪资模板项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_delete')")
    @OperateLogger(description = "删除薪资模板项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> del(String id) {
        // 演示模式 不允许操作
        super.demoError();
        
        IService.delete(id);
        return ResultWrapper.getSuccessResultWrapperByMsg("删除薪资模板项目数据成功");
    }

    /**
     * 薪资模板项目 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @Operation(summary = "批量删除薪资模板项目数据")
    @PreAuthorize("hasAuthority('system_salary_template_delete')")
    @OperateLogger(description = "批量删除薪资模板项目数据",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> delAll(String ids) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        IService.deleteAll(idArray);
        return ResultWrapper.getSuccessResultWrapperByMsg("批量删除薪资模板项目数据成功");
    }

    /**
     * 根据租户ID、数据月份和模板ID查询模板项目
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @param templateId 模板ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据模板查询模板项目")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findByTemplate(Long tenantId, String dataMonth, Long templateId) {
        try {
            Date parsedDate = SalaryDateUtils.parseDataMonth(dataMonth);
            List<SalaryTemplateItemMonthlyModel> modelList = IService.findByTenantIdAndDataMonthAndTemplateId(tenantId, parsedDate, templateId);
            return ResultWrapper.getSuccessResultWrapper(modelList);
        } catch (ParseException e) {
            log.error("数据月份格式错误: {}", dataMonth, e);
            return ResultWrapper.getCustomResultWrapper(400, "数据月份格式错误，请使用 YYYY-MM 格式");
        }
    }

    /**
     * 根据租户ID、数据月份和模板ID查询启用的模板项目
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @param templateId 模板ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据模板查询启用的模板项目")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findEnabledByTemplate(Long tenantId, String dataMonth, Long templateId) {
        try {
            Date parsedDate = SalaryDateUtils.parseDataMonth(dataMonth);
            List<SalaryTemplateItemMonthlyModel> modelList = IService.findEnabledByTenantIdAndDataMonthAndTemplateId(tenantId, parsedDate, templateId);
            return ResultWrapper.getSuccessResultWrapper(modelList);
        } catch (ParseException e) {
            log.error("数据月份格式错误: {}", dataMonth, e);
            return ResultWrapper.getCustomResultWrapper(400, "数据月份格式错误，请使用 YYYY-MM 格式");
        }
    }

    /**
     * 根据薪资项目ID查询关联的模板项目
     * @param salaryItemId 薪资项目ID
     * @return ResultWrapper
     */
    @Operation(summary = "根据薪资项目查询关联的模板项目")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findBySalaryItem(Long salaryItemId) {
        List<SalaryTemplateItemMonthlyModel> modelList = IService.findBySalaryItemId(salaryItemId);
        return ResultWrapper.getSuccessResultWrapper(modelList);
    }

    /**
     * 检查模板项目是否已存在
     * @param model 模型
     * @return ResultWrapper
     */
    @Operation(summary = "检查模板项目是否已存在")
    @Override
    public ResultWrapper<Boolean> checkExists(SalaryTemplateItemMonthlyModel model) {
        boolean exists = IService.checkTemplateItemExists(model.getTenantId(), model.getDataMonth(), 
                model.getTemplateId(), model.getSalaryItemId(), model.getId());
        return ResultWrapper.getSuccessResultWrapper(exists);
    }

    /**
     * 批量保存模板项目配置
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @param templateItems 模板项目列表
     * @return ResultWrapper
     */
    @Operation(summary = "批量保存模板项目配置")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "批量保存模板项目配置",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchSave(Long templateId, Long tenantId, String dataMonth, List<SalaryTemplateItemMonthlyModel> templateItems) {
        // 演示模式 不允许操作
        super.demoError();
        
        try {
            Date parsedDate = SalaryDateUtils.parseDataMonth(dataMonth);
            boolean success = IService.batchSaveTemplateItems(templateId, tenantId, parsedDate, templateItems);
            if (success) {
                return ResultWrapper.getSuccessResultWrapperByMsg("批量保存模板项目配置成功");
            } else {
                return ResultWrapper.getCustomResultWrapper(500, "批量保存模板项目配置失败");
            }
        } catch (ParseException e) {
            log.error("数据月份格式错误: {}", dataMonth, e);
            return ResultWrapper.getCustomResultWrapper(400, "数据月份格式错误，请使用 YYYY-MM 格式");
        }
    }

    /**
     * 删除模板的所有项目
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @return ResultWrapper
     */
    @Operation(summary = "删除模板的所有项目")
    @PreAuthorize("hasAuthority('system_salary_template_delete')")
    @OperateLogger(description = "删除模板的所有项目",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.DELETE, db = true)
    @Override
    public ResultWrapper<?> deleteByTemplate(Long templateId, Long tenantId, String dataMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        try {
            Date parsedDate = SalaryDateUtils.parseDataMonth(dataMonth);
            boolean success = IService.deleteByTemplateId(templateId, tenantId, parsedDate);
            if (success) {
                return ResultWrapper.getSuccessResultWrapperByMsg("删除模板项目成功");
            } else {
                return ResultWrapper.getCustomResultWrapper(500, "删除模板项目失败");
            }
        } catch (ParseException e) {
            log.error("数据月份格式错误: {}", dataMonth, e);
            return ResultWrapper.getCustomResultWrapper(400, "数据月份格式错误，请使用 YYYY-MM 格式");
        }
    }

    /**
     * 更新显示顺序
     * @param templateItemIds 模板项目ID列表（按新顺序排列）
     * @return ResultWrapper
     */
    @Operation(summary = "更新显示顺序")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "更新模板项目显示顺序",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> updateDisplayOrder(List<String> templateItemIds) {
        // 演示模式 不允许操作
        super.demoError();
        
        boolean success = IService.updateDisplayOrder(templateItemIds);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("更新显示顺序成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "更新显示顺序失败");
        }
    }

    /**
     * 批量启用/禁用模板项目
     * @param ids ID列表
     * @param status 状态
     * @return ResultWrapper
     */
    @Operation(summary = "批量启用/禁用模板项目")
    @PreAuthorize("hasAuthority('system_salary_template_update')")
    @OperateLogger(description = "批量启用/禁用模板项目",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.UPDATE, db = true)
    @Override
    public ResultWrapper<?> batchUpdateStatus(String ids, Boolean status) {
        // 演示模式 不允许操作
        super.demoError();
        
        String[] idArray = Convert.toStrArray(ids);
        List<String> idList = Convert.toList(String.class, idArray);
        boolean success = IService.batchUpdateStatus(idList, status);
        if (success) {
            return ResultWrapper.getSuccessResultWrapperByMsg("批量更新模板项目状态成功");
        } else {
            return ResultWrapper.getCustomResultWrapper(500, "批量更新模板项目状态失败");
        }
    }

    /**
     * 复制模板项目到新模板
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @return ResultWrapper
     */
    @Operation(summary = "复制模板项目到新模板")
    @PreAuthorize("hasAuthority('system_salary_template_insert')")
    @OperateLogger(description = "复制模板项目到新模板",
            module = ModuleEnum.MODULE_COMMON, operationType = OperationTypeEnum.INSERT, db = true)
    @Override
    public ResultWrapper<?> copyToNewTemplate(Long sourceTemplateId, Long targetTemplateId, Long tenantId, String dataMonth) {
        // 演示模式 不允许操作
        super.demoError();
        
        try {
            Date parsedDate = SalaryDateUtils.parseDataMonth(dataMonth);
            boolean success = IService.copyTemplateItems(sourceTemplateId, targetTemplateId, tenantId, parsedDate);
            if (success) {
                return ResultWrapper.getSuccessResultWrapperByMsg("复制模板项目成功");
            } else {
                return ResultWrapper.getCustomResultWrapper(500, "复制模板项目失败");
            }
        } catch (ParseException e) {
            log.error("数据月份格式错误: {}", dataMonth, e);
            return ResultWrapper.getCustomResultWrapper(400, "数据月份格式错误，请使用 YYYY-MM 格式");
        }
    }

    /**
     * 获取模板项目的最大显示顺序
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @return ResultWrapper
     */
    @Operation(summary = "获取模板项目的最大显示顺序")
    @PreAuthorize("hasAuthority('system_salary_template_select')")
    @Override
    public ResultWrapper<Integer> getMaxDisplayOrder(Long templateId, Long tenantId, String dataMonth) {
        try {
            Date parsedDate = SalaryDateUtils.parseDataMonth(dataMonth);
            Integer maxOrder = IService.getMaxDisplayOrder(templateId, tenantId, parsedDate);
            return ResultWrapper.getSuccessResultWrapper(maxOrder);
        } catch (ParseException e) {
            log.error("数据月份格式错误: {}", dataMonth, e);
            return ResultWrapper.getCustomResultWrapper(400, "数据月份格式错误，请使用 YYYY-MM 格式");
        }
    }


}
/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.opsli.api.wrapper.system.salary.EmployeeSalaryDetailMonthlyModel;
import org.opsli.common.exception.ServiceException;
import org.opsli.core.base.service.impl.CrudServiceImpl;
import org.opsli.core.utils.UserUtil;
import org.opsli.modulars.system.salary.entity.EmployeeSalaryDetailMonthly;
import org.opsli.modulars.system.salary.entity.PositionSalaryStandardMonthly;
import org.opsli.modulars.system.salary.mapper.EmployeeSalaryDetailMonthlyMapper;
import org.opsli.modulars.system.salary.mapper.PositionSalaryStandardMonthlyMapper;
import org.opsli.modulars.system.salary.service.IEmployeeSalaryDetailMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 员工薪资明细月度数据表 Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Service
public class EmployeeSalaryDetailMonthlyServiceImpl extends CrudServiceImpl<EmployeeSalaryDetailMonthlyMapper, EmployeeSalaryDetailMonthly, EmployeeSalaryDetailMonthlyModel> 
        implements IEmployeeSalaryDetailMonthlyService {

    @Autowired(required = false)
    private EmployeeSalaryDetailMonthlyMapper mapper;

    @Autowired(required = false)
    private PositionSalaryStandardMonthlyMapper positionSalaryStandardMapper;

    @Override
    public List<EmployeeSalaryDetailMonthlyModel> findByTenantIdAndDataMonthAndEmployeeId(Long tenantId, Date dataMonth, Long employeeId) {
        if (tenantId == null || dataMonth == null || employeeId == null) {
            return CollUtil.newArrayList();
        }
        
        List<EmployeeSalaryDetailMonthly> entityList = mapper.findByTenantIdAndDataMonthAndEmployeeId(tenantId, dataMonth, employeeId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<EmployeeSalaryDetailMonthlyModel> findByTenantIdAndDataMonthAndEmployeeIds(Long tenantId, Date dataMonth, List<Long> employeeIds) {
        if (tenantId == null || dataMonth == null || CollUtil.isEmpty(employeeIds)) {
            return CollUtil.newArrayList();
        }
        
        List<EmployeeSalaryDetailMonthly> entityList = mapper.findByTenantIdAndDataMonthAndEmployeeIds(tenantId, dataMonth, employeeIds);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<EmployeeSalaryDetailMonthlyModel> findBySalaryItemId(Long salaryItemId) {
        if (salaryItemId == null) {
            return CollUtil.newArrayList();
        }
        
        List<EmployeeSalaryDetailMonthly> entityList = mapper.findBySalaryItemId(salaryItemId);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public EmployeeSalaryDetailMonthlyModel findByEmployeeIdAndSalaryItemIdAndDataMonth(Long employeeId, Long salaryItemId, Date dataMonth) {
        if (employeeId == null || salaryItemId == null || dataMonth == null) {
            return null;
        }
        
        EmployeeSalaryDetailMonthly entity = mapper.findByEmployeeIdAndSalaryItemIdAndDataMonth(employeeId, salaryItemId, dataMonth);
        return super.transformT2M(entity);
    }

    @Override
    public boolean checkEmployeeSalaryDetailExists(Long tenantId, Date dataMonth, Long employeeId, Long salaryItemId, String excludeId) {
        if (tenantId == null || dataMonth == null || employeeId == null || salaryItemId == null) {
            return false;
        }
        
        int count = mapper.checkEmployeeSalaryDetailExists(tenantId, dataMonth, employeeId, salaryItemId, excludeId);
        return count > 0;
    }

    @Override
    public List<EmployeeSalaryDetailMonthlyModel> findByValueSource(String valueSource) {
        if (StrUtil.isBlank(valueSource)) {
            return CollUtil.newArrayList();
        }
        
        List<EmployeeSalaryDetailMonthly> entityList = mapper.findByValueSource(valueSource);
        return super.transformTs2Ms(entityList);
    }

    @Override
    public List<EmployeeSalaryDetailMonthlyModel> findDetailHistoryByEmployeeIdAndSalaryItemId(Long employeeId, Long salaryItemId, Integer limit) {
        if (employeeId == null || salaryItemId == null) {
            return CollUtil.newArrayList();
        }
        
        List<EmployeeSalaryDetailMonthly> entityList = mapper.findDetailHistoryByEmployeeIdAndSalaryItemId(employeeId, salaryItemId, limit);
        return super.transformTs2Ms(entityList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveEmployeeSalaryDetails(Long employeeId, Long tenantId, Date dataMonth, List<EmployeeSalaryDetailMonthlyModel> salaryDetails) {
        if (employeeId == null || tenantId == null || dataMonth == null || CollUtil.isEmpty(salaryDetails)) {
            return false;
        }

        try {
            // 先删除原有的薪资明细
            this.deleteByEmployeeIdAndDataMonth(employeeId, dataMonth, tenantId);

            // 批量保存新的薪资明细
            for (EmployeeSalaryDetailMonthlyModel model : salaryDetails) {
                model.setEmployeeId(employeeId);
                model.setTenantId(tenantId);
                model.setDataMonth(dataMonth);
                
                // 设置默认值
                if (StrUtil.isBlank(model.getValueSource())) {
                    model.setValueSource("手动录入");
                }
                if (model.getIsModified() == null) {
                    model.setIsModified(false);
                }
                if (StrUtil.isBlank(model.getCalculationMethod())) {
                    model.setCalculationMethod("manual");
                }
                
                // 处理JSON字段：如果为空字符串，设置为null
                if (StrUtil.isBlank(model.getSourceData())) {
                    model.setSourceData(null);
                }
                
                // 计算数值
                this.calculateValue(model);
                
                super.insert(model);
            }
            
            return true;
        } catch (Exception e) {
            log.error("批量保存员工薪资明细失败", e);
            throw new ServiceException(500, "批量保存员工薪资明细失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByEmployeeIdAndDataMonth(Long employeeId, Date dataMonth, Long tenantId) {
        if (employeeId == null || dataMonth == null || tenantId == null) {
            return false;
        }
        
        try {
            int result = mapper.deleteByEmployeeIdAndDataMonth(employeeId, dataMonth, tenantId);
            return result >= 0;
        } catch (Exception e) {
            log.error("删除员工薪资明细失败", e);
            throw new ServiceException(500, "删除员工薪资明细失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoFillFromPositionStandards(Long employeeId, Long tenantId, Date dataMonth, Long templateId) {
        if (employeeId == null || tenantId == null || dataMonth == null || templateId == null) {
            return false;
        }

        try {
            // TODO: 这里需要获取员工的岗位信息，暂时使用假数据
            Long positionId = 1L; // 实际应该从员工信息中获取
            
            // 查询岗位薪资标准
            QueryWrapper<PositionSalaryStandardMonthly> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.eq("data_month", dataMonth);
            queryWrapper.eq("position_id", positionId);
            queryWrapper.eq("template_id", templateId);
            queryWrapper.eq("is_auto_fill", true);
            queryWrapper.eq("deleted", 0);
            
            List<PositionSalaryStandardMonthly> standards = positionSalaryStandardMapper.selectList(queryWrapper);
            
            if (CollUtil.isEmpty(standards)) {
                return true; // 没有标准值，预填充成功
            }

            // 根据标准值创建薪资明细
            for (PositionSalaryStandardMonthly standard : standards) {
                // 检查是否已存在
                if (!this.checkEmployeeSalaryDetailExists(tenantId, dataMonth, employeeId, standard.getSalaryItemId(), null)) {
                    EmployeeSalaryDetailMonthlyModel model = new EmployeeSalaryDetailMonthlyModel();
                    model.setTenantId(tenantId);
                    model.setDataMonth(dataMonth);
                    model.setEmployeeId(employeeId);
                    model.setSalaryItemId(standard.getSalaryItemId());
                    model.setActualValue(standard.getStandardValue());
                    model.setStandardValue(standard.getStandardValue());
                    model.setValueSource("岗位标准");
                    model.setIsModified(false);
                    model.setCalculationMethod("standard");
                    
                    // 处理JSON字段：如果为空字符串，设置为null
                    if (StrUtil.isBlank(model.getSourceData())) {
                        model.setSourceData(null);
                    }
                    
                    // 计算数值
                    this.calculateValue(model);
                    
                    super.insert(model);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("自动预填充薪资明细失败", e);
            throw new ServiceException(500, "自动预填充薪资明细失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCalculatedValues(List<String> salaryDetailIds) {
        if (CollUtil.isEmpty(salaryDetailIds)) {
            return false;
        }

        try {
            for (String id : salaryDetailIds) {
                if (StrUtil.isNotBlank(id)) {
                    EmployeeSalaryDetailMonthlyModel model = super.get(id);
                    if (model != null) {
                        // 重新计算数值
                        this.calculateValue(model);
                        model.setLastCalculatedAt(new Date());
                        super.update(model);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新计算值失败", e);
            throw new ServiceException(500, "批量更新计算值失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsModified(String id, String modificationReason) {
        if (StrUtil.isBlank(id)) {
            return false;
        }

        try {
            UpdateWrapper<EmployeeSalaryDetailMonthly> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("is_modified", true);
            updateWrapper.set("modification_reason", modificationReason);
            updateWrapper.set("update_time", new Date());
            updateWrapper.set("update_by", UserUtil.getUser().getUsername());
            
            return super.update(updateWrapper);
        } catch (Exception e) {
            log.error("标记为已修改失败", e);
            throw new ServiceException(500, "标记为已修改失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchImportSalaryDetails(Long tenantId, Date dataMonth, List<EmployeeSalaryDetailMonthlyModel> salaryDetails) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        if (tenantId == null || dataMonth == null || CollUtil.isEmpty(salaryDetails)) {
            result.put("success", false);
            result.put("message", "参数不能为空");
            return result;
        }

        try {
            for (int i = 0; i < salaryDetails.size(); i++) {
                try {
                    EmployeeSalaryDetailMonthlyModel model = salaryDetails.get(i);
                    model.setTenantId(tenantId);
                    model.setDataMonth(dataMonth);
                    
                    // 设置默认值
                    if (StrUtil.isBlank(model.getValueSource())) {
                        model.setValueSource("批量导入");
                    }
                    if (model.getIsModified() == null) {
                        model.setIsModified(false);
                    }
                    
                    // 处理JSON字段：如果为空字符串，设置为null
                    if (StrUtil.isBlank(model.getSourceData())) {
                        model.setSourceData(null);
                    }
                    
                    // 计算数值
                    this.calculateValue(model);
                    
                    super.insert(model);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMessages.add("第" + (i + 1) + "行导入失败: " + e.getMessage());
                }
            }
            
            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errorMessages", errorMessages);
            
            return result;
        } catch (Exception e) {
            log.error("批量导入薪资明细失败", e);
            result.put("success", false);
            result.put("message", "批量导入失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyFromPreviousMonth(Long employeeId, Long tenantId, Date sourceMonth, Date targetMonth) {
        if (employeeId == null || tenantId == null || sourceMonth == null || targetMonth == null) {
            return false;
        }

        try {
            // 查询上月薪资明细
            List<EmployeeSalaryDetailMonthlyModel> sourceDetails = this.findByTenantIdAndDataMonthAndEmployeeId(tenantId, sourceMonth, employeeId);
            
            if (CollUtil.isEmpty(sourceDetails)) {
                return true; // 上月没有数据，复制成功
            }

            // 复制到当月
            for (EmployeeSalaryDetailMonthlyModel sourceDetail : sourceDetails) {
                // 检查当月是否已存在
                if (!this.checkEmployeeSalaryDetailExists(tenantId, targetMonth, employeeId, sourceDetail.getSalaryItemId(), null)) {
                    sourceDetail.setId(null); // 清空ID，让系统自动生成
                    sourceDetail.setDataMonth(targetMonth);
                    sourceDetail.setValueSource("复制上月");
                    sourceDetail.setIsModified(false);
                    sourceDetail.setModificationReason(null);
                    sourceDetail.setCreateTime(null);
                    sourceDetail.setUpdateTime(null);
                    sourceDetail.setCreateBy(null);
                    sourceDetail.setUpdateBy(null);
                    sourceDetail.setVersion(0);
                    sourceDetail.setLastCalculatedAt(null);
                    
                    // 处理JSON字段：如果为空字符串，设置为null
                    if (StrUtil.isBlank(sourceDetail.getSourceData())) {
                        sourceDetail.setSourceData(null);
                    }
                    
                    super.insert(sourceDetail);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("复制上月薪资明细失败", e);
            throw new ServiceException(500, "复制上月薪资明细失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getEmployeeSalaryStatistics(Long tenantId, Date dataMonth, Long employeeId) {
        if (tenantId == null || dataMonth == null || employeeId == null) {
            return new HashMap<>();
        }
        
        return mapper.getEmployeeSalaryStatistics(tenantId, dataMonth, employeeId);
    }

    @Override
    protected void beforeInsert(EmployeeSalaryDetailMonthlyModel model, EmployeeSalaryDetailMonthly entity) {
        // 检查员工薪资明细是否已存在
        if (this.checkEmployeeSalaryDetailExists(model.getTenantId(), model.getDataMonth(), 
                model.getEmployeeId(), model.getSalaryItemId(), null)) {
            throw new ServiceException(400, "该员工的薪资项目明细已存在");
        }
        
        // 设置默认值
        if (StrUtil.isBlank(model.getValueSource())) {
            model.setValueSource("手动录入");
        }
        if (model.getIsModified() == null) {
            model.setIsModified(false);
        }
        if (StrUtil.isBlank(model.getCalculationMethod())) {
            model.setCalculationMethod("manual");
        }
        
        // 处理JSON字段：如果为空字符串，设置为null
        if (StrUtil.isBlank(model.getSourceData())) {
            model.setSourceData(null);
        }
        
        // 计算数值
        this.calculateValue(model);
    }

    @Override
    protected void beforeUpdate(EmployeeSalaryDetailMonthlyModel model, EmployeeSalaryDetailMonthly entity) {
        // 检查员工薪资明细是否已存在（排除当前记录）
        if (this.checkEmployeeSalaryDetailExists(model.getTenantId(), model.getDataMonth(), 
                model.getEmployeeId(), model.getSalaryItemId(), model.getId())) {
            throw new ServiceException(400, "该员工的薪资项目明细已存在");
        }
        
        // 处理JSON字段：如果为空字符串，设置为null
        if (StrUtil.isBlank(model.getSourceData())) {
            model.setSourceData(null);
        }
        
        // 重新计算数值
        this.calculateValue(model);
        
        // 如果实际值发生变化，标记为已修改
        EmployeeSalaryDetailMonthlyModel originalModel = super.get(model.getId());
        if (originalModel != null && !Objects.equals(originalModel.getActualValue(), model.getActualValue())) {
            model.setIsModified(true);
            if (StrUtil.isBlank(model.getModificationReason())) {
                model.setModificationReason("手动修改");
            }
        }
    }

    /**
     * 计算数值
     * @param model 薪资明细模型
     */
    private void calculateValue(EmployeeSalaryDetailMonthlyModel model) {
        if (model == null || StrUtil.isBlank(model.getActualValue())) {
            return;
        }
        
        try {
            // 尝试将实际值转换为数值
            BigDecimal value = new BigDecimal(model.getActualValue());
            model.setCalculatedValue(value);
        } catch (NumberFormatException e) {
            // 如果不是数值类型，设置为null
            model.setCalculatedValue(null);
        }
        
        model.setLastCalculatedAt(new Date());
    }
}
/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.modulars.system.salary.service;

import org.opsli.core.base.service.interfaces.CrudServiceInterface;
import org.opsli.modulars.system.salary.entity.EmployeeSalaryDetailMonthly;
import org.opsli.api.wrapper.system.salary.EmployeeSalaryDetailMonthlyModel;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 员工薪资明细月度数据表 Service接口
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IEmployeeSalaryDetailMonthlyService extends CrudServiceInterface<EmployeeSalaryDetailMonthly, EmployeeSalaryDetailMonthlyModel> {

    /**
     * 根据租户ID、数据月份和员工ID查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthlyModel> findByTenantIdAndDataMonthAndEmployeeId(Long tenantId, Date dataMonth, Long employeeId);

    /**
     * 根据租户ID、数据月份和员工ID列表查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeIds 员工ID列表
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthlyModel> findByTenantIdAndDataMonthAndEmployeeIds(Long tenantId, Date dataMonth, List<Long> employeeIds);

    /**
     * 根据薪资项目ID查询相关的薪资明细
     * @param salaryItemId 薪资项目ID
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthlyModel> findBySalaryItemId(Long salaryItemId);

    /**
     * 根据员工ID、薪资项目ID和数据月份查询薪资明细
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return 薪资明细
     */
    EmployeeSalaryDetailMonthlyModel findByEmployeeIdAndSalaryItemIdAndDataMonth(Long employeeId, Long salaryItemId, Date dataMonth);

    /**
     * 检查员工薪资明细是否已存在
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param excludeId 排除的ID（用于编辑时检查）
     * @return 是否已存在
     */
    boolean checkEmployeeSalaryDetailExists(Long tenantId, Date dataMonth, Long employeeId, Long salaryItemId, String excludeId);

    /**
     * 根据值来源查询薪资明细
     * @param valueSource 值来源
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthlyModel> findByValueSource(String valueSource);

    /**
     * 根据员工ID查询薪资明细历史记录
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param limit 限制数量
     * @return 薪资明细列表
     */
    List<EmployeeSalaryDetailMonthlyModel> findDetailHistoryByEmployeeIdAndSalaryItemId(Long employeeId, Long salaryItemId, Integer limit);

    /**
     * 批量保存员工薪资明细
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param salaryDetails 薪资明细列表
     * @return 是否成功
     */
    boolean batchSaveEmployeeSalaryDetails(Long employeeId, Long tenantId, Date dataMonth, List<EmployeeSalaryDetailMonthlyModel> salaryDetails);

    /**
     * 删除员工薪资明细
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @param tenantId 租户ID
     * @return 是否成功
     */
    boolean deleteByEmployeeIdAndDataMonth(Long employeeId, Date dataMonth, Long tenantId);

    /**
     * 根据岗位标准自动预填充员工薪资明细
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 薪资模板ID
     * @return 是否成功
     */
    boolean autoFillFromPositionStandards(Long employeeId, Long tenantId, Date dataMonth, Long templateId);

    /**
     * 批量更新计算值
     * @param salaryDetailIds 薪资明细ID列表
     * @return 是否成功
     */
    boolean batchUpdateCalculatedValues(List<String> salaryDetailIds);

    /**
     * 标记为已修改
     * @param id 薪资明细ID
     * @param modificationReason 修改原因
     * @return 是否成功
     */
    boolean markAsModified(String id, String modificationReason);

    /**
     * 批量导入员工薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param salaryDetails 薪资明细列表
     * @return 导入结果
     */
    Map<String, Object> batchImportSalaryDetails(Long tenantId, Date dataMonth, List<EmployeeSalaryDetailMonthlyModel> salaryDetails);

    /**
     * 复制上月薪资明细到当月
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param sourceMonth 源月份
     * @param targetMonth 目标月份
     * @return 是否成功
     */
    boolean copyFromPreviousMonth(Long employeeId, Long tenantId, Date sourceMonth, Date targetMonth);

    /**
     * 获取员工薪资明细统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return 统计信息
     */
    Map<String, Object> getEmployeeSalaryStatistics(Long tenantId, Date dataMonth, Long employeeId);
}
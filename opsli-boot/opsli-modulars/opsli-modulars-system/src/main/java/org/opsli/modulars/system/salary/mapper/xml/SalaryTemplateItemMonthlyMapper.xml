<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.opsli.modulars.system.salary.mapper.SalaryTemplateItemMonthlyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.opsli.modulars.system.salary.entity.SalaryTemplateItemMonthly">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="data_month" property="dataMonth" />
        <result column="template_id" property="templateId" />
        <result column="salary_item_id" property="salaryItemId" />
        <result column="display_order" property="displayOrder" />
        <result column="is_required" property="isRequired" />
        <result column="is_editable" property="isEditable" />
        <result column="default_value" property="defaultValue" />
        <result column="validation_rules" property="validationRules" />
        <result column="status" property="status" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 带薪资项目信息的查询映射结果 -->
    <resultMap id="DetailResultMap" type="org.opsli.modulars.system.salary.entity.SalaryTemplateItemMonthly" extends="BaseResultMap">
        <result column="salary_item_name" property="salaryItemName" />
        <result column="salary_item_category" property="salaryItemCategory" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        sti.id, sti.tenant_id, sti.data_month, sti.template_id, sti.salary_item_id, 
        sti.display_order, sti.is_required, sti.is_editable, sti.default_value, 
        sti.validation_rules, sti.status, sti.version, sti.deleted, 
        sti.create_by, sti.create_time, sti.update_by, sti.update_time
    </sql>

    <!-- 带薪资项目信息的查询结果列 -->
    <sql id="Detail_Column_List">
        <include refid="Base_Column_List" />,
        si.name as salary_item_name, si.category as salary_item_category
    </sql>

    <!-- 根据模板ID查询模板项目（按显示顺序排序） -->
    <select id="findByTemplateIdOrderByDisplayOrder" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM salary_template_items_monthly sti
        LEFT JOIN salary_items si ON sti.salary_item_id = si.id AND si.deleted = 0
        WHERE sti.template_id = #{templateId} AND sti.deleted = 0
        ORDER BY sti.display_order ASC, sti.create_time ASC
    </select>

    <!-- 根据租户ID、数据月份和模板ID查询模板项目 -->
    <select id="findByTenantIdAndDataMonthAndTemplateId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM salary_template_items_monthly sti
        LEFT JOIN salary_items si ON sti.salary_item_id = si.id AND si.deleted = 0
        WHERE sti.tenant_id = #{tenantId} 
        AND sti.data_month = #{dataMonth} 
        AND sti.template_id = #{templateId} 
        AND sti.deleted = 0
        ORDER BY sti.display_order ASC, sti.create_time ASC
    </select>

    <!-- 根据薪资项目ID查询关联的模板项目 -->
    <select id="findBySalaryItemId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM salary_template_items_monthly sti
        WHERE sti.salary_item_id = #{salaryItemId} AND sti.deleted = 0
        ORDER BY sti.data_month DESC, sti.template_id ASC
    </select>

    <!-- 批量删除模板项目（软删除） -->
    <update id="deleteByTemplateId">
        UPDATE salary_template_items_monthly 
        SET deleted = 1, update_time = NOW()
        WHERE template_id = #{templateId} 
        AND tenant_id = #{tenantId} 
        AND data_month = #{dataMonth}
        AND deleted = 0
    </update>

    <!-- 物理删除模板项目 -->
    <delete id="hardDeleteByTemplateId">
        DELETE FROM salary_template_items_monthly 
        WHERE template_id = #{templateId} 
        AND tenant_id = #{tenantId} 
        AND data_month = #{dataMonth}
    </delete>

    <!-- 检查模板项目是否已存在 -->
    <select id="checkTemplateItemExists" resultType="int">
        SELECT COUNT(1)
        FROM salary_template_items_monthly
        WHERE tenant_id = #{tenantId} 
        AND data_month = #{dataMonth}
        AND template_id = #{templateId} 
        AND salary_item_id = #{salaryItemId} 
        AND deleted = 0
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据租户ID、数据月份和模板ID查询启用的模板项目（按显示顺序排序） -->
    <select id="findEnabledByTenantIdAndDataMonthAndTemplateIdOrderByDisplayOrder" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List" />
        FROM salary_template_items_monthly sti
        LEFT JOIN salary_items si ON sti.salary_item_id = si.id AND si.deleted = 0
        WHERE sti.tenant_id = #{tenantId} 
        AND sti.data_month = #{dataMonth} 
        AND sti.template_id = #{templateId} 
        AND sti.status = 1 
        AND sti.deleted = 0
        ORDER BY sti.display_order ASC, sti.create_time ASC
    </select>

    <!-- 获取模板项目的最大显示顺序 -->
    <select id="getMaxDisplayOrder" resultType="int">
        SELECT COALESCE(MAX(display_order), 0)
        FROM salary_template_items_monthly
        WHERE template_id = #{templateId} 
        AND tenant_id = #{tenantId} 
        AND data_month = #{dataMonth}
        AND deleted = 0
    </select>

</mapper>
/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.salary;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.salary.SalaryTemplateMonthlyModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * 薪资模板月度数据表 Api
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface SalaryTemplateMonthlyApi {

    /** 标题 */
    String TITLE = "薪资模板管理";
    /** 子标题 */
    String SUB_TITLE = "薪资模板月度数据";

    /**
     * 薪资模板 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<SalaryTemplateMonthlyModel> get(SalaryTemplateMonthlyModel model);

    /**
     * 薪资模板 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            SalaryTemplateMonthlyModel request
    );

    /**
     * 薪资模板 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findAll")
    ResultWrapper<List<SalaryTemplateMonthlyModel>> findAll(SalaryTemplateMonthlyModel request);

    /**
     * 薪资模板 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody SalaryTemplateMonthlyModel model);

    /**
     * 薪资模板 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody SalaryTemplateMonthlyModel model);

    /**
     * 薪资模板 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(@RequestParam("id") String id);

    /**
     * 薪资模板 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(@RequestParam("ids") String ids);

    /**
     * 根据数据月份查询薪资模板
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @GetMapping("/findByDataMonth")
    ResultWrapper<List<SalaryTemplateMonthlyModel>> findByDataMonth(@RequestParam("dataMonth") Date dataMonth);

    /**
     * 检查模板名称是否唯一
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/checkNameUnique")
    ResultWrapper<Boolean> checkNameUnique(@RequestBody SalaryTemplateMonthlyModel model);

    /**
     * 复制模板到新月份
     * @param sourceTemplateId 源模板ID
     * @param targetDataMonth 目标月份
     * @param newTemplateName 新模板名称
     * @return ResultWrapper
     */
    @PostMapping("/copyToNewMonth")
    ResultWrapper<SalaryTemplateMonthlyModel> copyToNewMonth(
            @RequestParam("sourceTemplateId") String sourceTemplateId,
            @RequestParam("targetDataMonth") Date targetDataMonth,
            @RequestParam("newTemplateName") String newTemplateName
    );

    /**
     * 批量启用/禁用薪资模板
     * @param ids ID数组
     * @param status 状态
     * @return ResultWrapper
     */
    @PostMapping("/batchUpdateStatus")
    ResultWrapper<?> batchUpdateStatus(@RequestParam("ids") String ids, @RequestParam("status") Boolean status);
}

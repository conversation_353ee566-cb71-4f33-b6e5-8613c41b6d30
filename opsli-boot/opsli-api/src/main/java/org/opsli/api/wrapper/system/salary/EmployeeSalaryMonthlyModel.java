/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.salary;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.common.annotation.validator.Validator;
import org.opsli.common.enums.ValidatorType;
import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工月度薪资汇总表 Model
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "员工月度薪资汇总表")
public class EmployeeSalaryMonthlyModel extends ApiWrapper {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @ExcelProperty(value = "数据月份", order = 1)
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    @Validator({ValidatorType.IS_NOT_NULL})
    private Date dataMonth;
    
    /** 员工ID */
    @Schema(description = "员工ID")
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long employeeId;
    
    /** 员工姓名 */
    @Schema(description = "员工姓名")
    @ExcelProperty(value = "员工姓名", order = 2)
    private String employeeName;
    
    /** 员工编号 */
    @Schema(description = "员工编号")
    @ExcelProperty(value = "员工编号", order = 3)
    private String employeeNumber;
    
    /** 使用的薪资模板ID */
    @Schema(description = "使用的薪资模板ID")
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long templateId;
    
    /** 模板名称 */
    @Schema(description = "模板名称")
    @ExcelProperty(value = "模板名称", order = 4)
    private String templateName;
    
    /** 基本工资合计 */
    @Schema(description = "基本工资合计")
    @ExcelProperty(value = "基本工资合计", order = 5)
    private BigDecimal baseSalaryTotal;
    
    /** 补贴合计 */
    @Schema(description = "补贴合计")
    @ExcelProperty(value = "补贴合计", order = 6)
    private BigDecimal allowanceTotal;
    
    /** 提成合计 */
    @Schema(description = "提成合计")
    @ExcelProperty(value = "提成合计", order = 7)
    private BigDecimal commissionTotal;
    
    /** 奖金合计 */
    @Schema(description = "奖金合计")
    @ExcelProperty(value = "奖金合计", order = 8)
    private BigDecimal bonusTotal;
    
    /** 扣除合计 */
    @Schema(description = "扣除合计")
    @ExcelProperty(value = "扣除合计", order = 9)
    private BigDecimal deductionTotal;
    
    /** 应发工资总额 */
    @Schema(description = "应发工资总额")
    @ExcelProperty(value = "应发工资总额", order = 10)
    private BigDecimal grossSalary;
    
    /** 实发工资 */
    @Schema(description = "实发工资")
    @ExcelProperty(value = "实发工资", order = 11)
    private BigDecimal netSalary;
    
    /** 计算时间 */
    @Schema(description = "计算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date calculatedAt;
    
    /** 状态 */
    @Schema(description = "状态")
    @ExcelProperty(value = "状态", order = 12)
    private String status;
    
    /** 审批人 */
    @Schema(description = "审批人")
    @ExcelProperty(value = "审批人", order = 13)
    private String approvedBy;
    
    /** 审批时间 */
    @Schema(description = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvedTime;
    
    /** 发放日期 */
    @Schema(description = "发放日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date payDate;
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.organizationChart;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.opsli.api.base.warpper.ApiWrapper;

import java.util.List;

/**
 * 组织架构图 Model
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Schema(description = "组织架构图 Model")
@Data
@EqualsAndHashCode(callSuper = true)
public class OrganizationChartModel extends ApiWrapper {

    /** 节点类型：department-部门，position-岗位，employee-员工 */
    @Schema(description = "节点类型")
    private String nodeType;
    
    /** 节点ID */
    @Schema(description = "节点ID")
    private Long nodeId;
    
    /** 节点名称 */
    @Schema(description = "节点名称")
    private String nodeName;
    
    /** 节点编码 */
    @Schema(description = "节点编码")
    private String nodeCode;
    
    /** 父节点ID */
    @Schema(description = "父节点ID")
    private Long parentId;
    
    /** 父节点类型 */
    @Schema(description = "父节点类型")
    private String parentType;
    
    /** 节点级别 */
    @Schema(description = "节点级别")
    private Integer level;
    
    /** 状态：1-启用，0-禁用 */
    @Schema(description = "状态")
    private Integer status;
    
    /** 排序 */
    @Schema(description = "排序")
    private Integer sort;
    
    /** 子节点列表 */
    @Schema(description = "子节点列表")
    private List<OrganizationChartModel> children;
    
    /** 扩展信息 - 部门相关 */
    @Schema(description = "部门描述")
    private String departmentDescription;
    
    /** 扩展信息 - 岗位相关 */
    @Schema(description = "岗位描述")
    private String positionDescription;
    
    @Schema(description = "岗位要求")
    private String positionRequirements;
    
    @Schema(description = "岗位级别")
    private String positionLevel;
    
    @Schema(description = "编制人数")
    private Integer headcount;
    
    /** 扩展信息 - 员工相关 */
    @Schema(description = "员工姓名")
    private String employeeName;
    
    @Schema(description = "员工编号")
    private String employeeNumber;
    
    @Schema(description = "性别")
    private String gender;
    
    @Schema(description = "手机号码")
    private String phoneNumber;
    
    @Schema(description = "员工状态")
    private String employeeStatus;
    
    @Schema(description = "入职日期")
    private String hireDate;
    
    /** 统计信息 */
    @Schema(description = "直接下属数量")
    private Integer directSubordinates;
    
    @Schema(description = "总下属数量")
    private Integer totalSubordinates;
    
    @Schema(description = "部门员工总数")
    private Integer departmentEmployeeCount;
    
    @Schema(description = "岗位员工数")
    private Integer positionEmployeeCount;
}

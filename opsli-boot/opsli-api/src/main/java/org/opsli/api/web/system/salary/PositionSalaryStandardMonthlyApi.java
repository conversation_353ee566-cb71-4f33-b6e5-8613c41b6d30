/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.salary;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.salary.PositionSalaryStandardMonthlyModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 岗位薪资标准月度数据表 Api
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface PositionSalaryStandardMonthlyApi {

    /** 标题 */
    String TITLE = "岗位薪资标准管理";
    /** 子标题 */
    String SUB_TITLE = "岗位薪资标准月度数据";

    /**
     * 岗位薪资标准 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<PositionSalaryStandardMonthlyModel> get(PositionSalaryStandardMonthlyModel model);

    /**
     * 岗位薪资标准 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            PositionSalaryStandardMonthlyModel request
    );

    /**
     * 岗位薪资标准 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findAll")
    ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findAll(PositionSalaryStandardMonthlyModel request);

    /**
     * 岗位薪资标准 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody PositionSalaryStandardMonthlyModel model);

    /**
     * 岗位薪资标准 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody PositionSalaryStandardMonthlyModel model);

    /**
     * 岗位薪资标准 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(@RequestParam("id") String id);

    /**
     * 岗位薪资标准 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(@RequestParam("ids") String ids);

    /**
     * 根据租户ID、数据月份和岗位ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return ResultWrapper
     */
    @GetMapping("/findByPosition")
    ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findByPosition(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("positionId") Long positionId
    );

    /**
     * 根据租户ID、数据月份和模板ID查询薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 模板ID
     * @return ResultWrapper
     */
    @GetMapping("/findByTemplate")
    ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findByTemplate(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("templateId") Long templateId
    );

    /**
     * 根据岗位ID、模板ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @GetMapping("/findByPositionAndTemplate")
    ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findByPositionAndTemplate(
            @RequestParam("positionId") Long positionId,
            @RequestParam("templateId") Long templateId,
            @RequestParam("dataMonth") Date dataMonth
    );

    /**
     * 根据薪资项目ID查询关联的岗位标准
     * @param salaryItemId 薪资项目ID
     * @return ResultWrapper
     */
    @GetMapping("/findBySalaryItem")
    ResultWrapper<List<PositionSalaryStandardMonthlyModel>> findBySalaryItem(@RequestParam("salaryItemId") Long salaryItemId);

    /**
     * 根据岗位ID、薪资项目ID和数据月份查询薪资标准
     * @param positionId 岗位ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @GetMapping("/findByPositionAndSalaryItem")
    ResultWrapper<PositionSalaryStandardMonthlyModel> findByPositionAndSalaryItem(
            @RequestParam("positionId") Long positionId,
            @RequestParam("salaryItemId") Long salaryItemId,
            @RequestParam("dataMonth") Date dataMonth
    );

    /**
     * 检查岗位薪资标准是否已存在
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/checkExists")
    ResultWrapper<Boolean> checkExists(@RequestBody PositionSalaryStandardMonthlyModel model);

    /**
     * 批量保存岗位薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param standards 薪资标准列表
     * @return ResultWrapper
     */
    @PostMapping("/batchSave")
    ResultWrapper<?> batchSave(
            @RequestParam("positionId") Long positionId,
            @RequestParam("templateId") Long templateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestBody List<PositionSalaryStandardMonthlyModel> standards
    );

    /**
     * 删除岗位薪资标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @PostMapping("/deleteByPositionAndTemplate")
    ResultWrapper<?> deleteByPositionAndTemplate(
            @RequestParam("positionId") Long positionId,
            @RequestParam("templateId") Long templateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth
    );

    /**
     * 批量启用/禁用自动预填充
     * @param ids ID列表
     * @param isAutoFill 是否自动预填充
     * @return ResultWrapper
     */
    @PostMapping("/batchUpdateAutoFill")
    ResultWrapper<?> batchUpdateAutoFill(@RequestParam("ids") String ids, @RequestParam("isAutoFill") Boolean isAutoFill);

    /**
     * 批量设置只读状态
     * @param ids ID列表
     * @param isReadonly 是否只读
     * @return ResultWrapper
     */
    @PostMapping("/batchUpdateReadonly")
    ResultWrapper<?> batchUpdateReadonly(@RequestParam("ids") String ids, @RequestParam("isReadonly") Boolean isReadonly);

    /**
     * 复制岗位薪资标准到新模板
     * @param sourcePositionId 源岗位ID
     * @param sourceTemplateId 源模板ID
     * @param targetPositionId 目标岗位ID
     * @param targetTemplateId 目标模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @PostMapping("/copyToNewTemplate")
    ResultWrapper<?> copyToNewTemplate(
            @RequestParam("sourcePositionId") Long sourcePositionId,
            @RequestParam("sourceTemplateId") Long sourceTemplateId,
            @RequestParam("targetPositionId") Long targetPositionId,
            @RequestParam("targetTemplateId") Long targetTemplateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth
    );

    /**
     * 复制岗位薪资标准到新月份
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param sourceMonth 源月份
     * @param targetMonth 目标月份
     * @return ResultWrapper
     */
    @PostMapping("/copyToNewMonth")
    ResultWrapper<?> copyToNewMonth(
            @RequestParam("positionId") Long positionId,
            @RequestParam("templateId") Long templateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("sourceMonth") Date sourceMonth,
            @RequestParam("targetMonth") Date targetMonth
    );

    /**
     * 根据模板项目自动创建岗位标准
     * @param positionId 岗位ID
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @PostMapping("/autoCreateFromTemplate")
    ResultWrapper<?> autoCreateFromTemplate(
            @RequestParam("positionId") Long positionId,
            @RequestParam("templateId") Long templateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth
    );

    /**
     * 批量导入岗位薪资标准
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param standards 薪资标准列表
     * @return ResultWrapper
     */
    @PostMapping("/batchImport")
    ResultWrapper<Map<String, Object>> batchImport(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestBody List<PositionSalaryStandardMonthlyModel> standards
    );

    /**
     * 验证标准值范围
     * @param standardValue 标准值
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return ResultWrapper
     */
    @PostMapping("/validateValueRange")
    ResultWrapper<Boolean> validateValueRange(
            @RequestParam("standardValue") String standardValue,
            @RequestParam(value = "minValue", required = false) String minValue,
            @RequestParam(value = "maxValue", required = false) String maxValue
    );

    /**
     * 获取岗位薪资标准统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param positionId 岗位ID
     * @return ResultWrapper
     */
    @GetMapping("/getStatistics")
    ResultWrapper<Map<String, Object>> getStatistics(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("positionId") Long positionId
    );
}
/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.salary;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.common.annotation.validator.Validator;
import org.opsli.common.annotation.validator.ValidatorLenMax;
import org.opsli.common.enums.ValidatorType;
import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工薪资明细月度数据表 Model
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "员工薪资明细月度数据表")
public class EmployeeSalaryDetailMonthlyModel extends ApiWrapper {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @ExcelProperty(value = "数据月份", order = 1)
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    @Validator({ValidatorType.IS_NOT_NULL})
    private Date dataMonth;
    
    /** 员工ID */
    @Schema(description = "员工ID")
    @ExcelProperty(value = "员工ID", order = 2)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long employeeId;
    
    /** 员工姓名 (关联查询字段) */
    @Schema(description = "员工姓名")
    @ExcelProperty(value = "员工姓名", order = 3)
    private String employeeName;
    
    /** 员工工号 (关联查询字段) */
    @Schema(description = "员工工号")
    @ExcelProperty(value = "员工工号", order = 4)
    private String employeeCode;
    
    /** 薪资项目ID */
    @Schema(description = "薪资项目ID")
    @ExcelProperty(value = "薪资项目ID", order = 5)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long salaryItemId;
    
    /** 薪资项目名称 (关联查询字段) */
    @Schema(description = "薪资项目名称")
    @ExcelProperty(value = "薪资项目名称", order = 6)
    private String salaryItemName;
    
    /** 薪资项目分类 (关联查询字段) */
    @Schema(description = "薪资项目分类")
    @ExcelProperty(value = "薪资项目分类", order = 7)
    private String salaryItemCategory;
    
    /** 实际值（用户录入或系统计算） */
    @Schema(description = "实际值")
    @ExcelProperty(value = "实际值", order = 8)
    @ValidatorLenMax(500)
    private String actualValue;
    
    /** 岗位标准值（来源于position_salary_standards） */
    @Schema(description = "岗位标准值")
    @ExcelProperty(value = "岗位标准值", order = 9)
    @ValidatorLenMax(500)
    private String standardValue;
    
    /** 计算后的数值（仅用于统计，非decimal类型为NULL） */
    @Schema(description = "计算后的数值")
    @ExcelProperty(value = "计算后的数值", order = 10)
    private BigDecimal calculatedValue;
    
    /** 值来源 */
    @Schema(description = "值来源")
    @ExcelProperty(value = "值来源", order = 11)
    private String valueSource;
    
    /** 是否已修改（区别于预填充值） */
    @Schema(description = "是否已修改")
    @ExcelProperty(value = "是否已修改", order = 12)
    private Boolean isModified;
    
    /** 修改原因 */
    @Schema(description = "修改原因")
    @ExcelProperty(value = "修改原因", order = 13)
    @ValidatorLenMax(255)
    private String modificationReason;
    
    /** 计算方式 */
    @Schema(description = "计算方式")
    @ExcelProperty(value = "计算方式", order = 14)
    @ValidatorLenMax(50)
    private String calculationMethod;
    
    /** 计算来源数据 */
    @Schema(description = "计算来源数据")
    @ExcelProperty(value = "计算来源数据", order = 15)
    private String sourceData;
    
    /** 计算说明 */
    @Schema(description = "计算说明")
    @ExcelProperty(value = "计算说明", order = 16)
    @ValidatorLenMax(500)
    private String calculationNote;
    
    /** 最后计算时间 */
    @Schema(description = "最后计算时间")
    @ExcelProperty(value = "最后计算时间", order = 17)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCalculatedAt;
}
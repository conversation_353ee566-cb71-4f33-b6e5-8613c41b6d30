/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.salary;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.common.annotation.validator.Validator;
import org.opsli.common.annotation.validator.ValidatorLenMax;
import org.opsli.common.annotation.validator.ValidatorLenMin;
import org.opsli.common.enums.ValidatorType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.util.Date;

/**
 * 薪资模板月度数据表 Model
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "薪资模板月度数据表")
public class SalaryTemplateMonthlyModel extends ApiWrapper {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @ExcelProperty(value = "数据月份", order = 1)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Validator({ValidatorType.IS_NOT_NULL})
    private Date dataMonth;
    
    /** 模板名称 */
    @Schema(description = "模板名称")
    @ExcelProperty(value = "模板名称", order = 2)
    @Validator({ValidatorType.IS_NOT_NULL})
    @ValidatorLenMax(255)
    @ValidatorLenMin(1)
    private String name;
    
    /** 适用范围类型 */
    @Schema(description = "适用范围类型")
    @ExcelProperty(value = "适用范围类型", order = 3)
    private String scopeType;
    
    /** 适用范围配置 (部门ID列表/岗位ID列表/员工ID列表) */
    @Schema(description = "适用范围配置")
    @ExcelProperty(value = "适用范围配置", order = 4)
    private String scopeConfig;
    
    /** 是否为默认模板 */
    @Schema(description = "是否为默认模板")
    @ExcelProperty(value = "是否为默认模板", order = 5)
    private Boolean isDefault;
    
    /** 优先级（数字越小优先级越高） */
    @Schema(description = "优先级")
    @ExcelProperty(value = "优先级", order = 6)
    private Integer priority;
    
    /** 模板状态 */
    @Schema(description = "模板状态")
    @ExcelProperty(value = "模板状态", order = 7)
    private Boolean status;
    
    /** 模板描述 */
    @Schema(description = "模板描述")
    @ExcelProperty(value = "模板描述", order = 8)
    private String description;
}

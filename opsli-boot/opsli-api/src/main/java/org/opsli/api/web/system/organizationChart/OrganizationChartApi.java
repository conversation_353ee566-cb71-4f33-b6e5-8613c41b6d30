/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.organizationChart;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.organizationChart.OrganizationChartModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 组织架构图 API
 *
 * 对外 API 直接 暴露 @GetMapping 或者 @PostMapping
 * 对内也推荐 单机版 不需要设置 Mapping 但是调用方法得从Controller写起
 *
 * 这样写法虽然比较绕，但是当单体项目想要改造微服务架构时 时非常容易的
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface OrganizationChartApi {

    /** 标题 */
    String TITLE = "组织架构图";
    /** 子标题 */
    String SUB_TITLE = "组织架构图 - 可视化展示";

    /**
     * 获取完整的组织架构树
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @return ResultWrapper
     */
    @GetMapping("/getOrganizationTree")
    ResultWrapper<List<OrganizationChartModel>> getOrganizationTree(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth
    );

    /**
     * 获取指定部门的组织架构子树
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @param departmentId 部门ID
     * @return ResultWrapper
     */
    @GetMapping("/getDepartmentTree")
    ResultWrapper<List<OrganizationChartModel>> getDepartmentTree(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestParam("departmentId") Long departmentId
    );

    /**
     * 获取组织架构统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @return ResultWrapper
     */
    @GetMapping("/getOrganizationStats")
    ResultWrapper<OrganizationChartModel> getOrganizationStats(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth
    );

    /**
     * 搜索组织架构节点
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: yyyy-MM)
     * @param keyword 搜索关键词
     * @param nodeType 节点类型 (可选: department, position, employee)
     * @return ResultWrapper
     */
    @GetMapping("/searchNodes")
    ResultWrapper<List<OrganizationChartModel>> searchNodes(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "nodeType", required = false) String nodeType
    );
}

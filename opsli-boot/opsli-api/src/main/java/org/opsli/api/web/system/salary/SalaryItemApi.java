/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.salary;

import java.util.List;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.salary.SalaryItemModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 薪资项目基础定义表 Api
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface SalaryItemApi {

    /** 标题 */
    String TITLE = "薪资项目管理";
    /** 子标题 */
    String SUB_TITLE = "薪资项目基础定义";

    /**
     * 薪资项目 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<SalaryItemModel> get(SalaryItemModel model);

    /**
     * 薪资项目 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            SalaryItemModel request
    );

    /**
     * 薪资项目 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findAll")
    ResultWrapper<List<SalaryItemModel>> findAll(SalaryItemModel request);

    /**
     * 薪资项目 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody SalaryItemModel model);

    /**
     * 薪资项目 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody SalaryItemModel model);

    /**
     * 薪资项目 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(@RequestParam("id") String id);

    /**
     * 薪资项目 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(@RequestParam("ids") String ids);

    /**
     * 根据分类查询薪资项目
     * @param category 分类
     * @return ResultWrapper
     */
    @GetMapping("/findByCategory")
    ResultWrapper<List<SalaryItemModel>> findByCategory(@RequestParam("category") String category);

    /**
     * 查询启用的薪资项目
     * @return ResultWrapper
     */
    @GetMapping("/findEnabled")
    ResultWrapper<List<SalaryItemModel>> findEnabled();

    /**
     * 检查薪资项目名称是否唯一
     * @param tenantId 租户ID
     * @param name 项目名称
     * @param id 项目ID（编辑时排除自己）
     * @return ResultWrapper
     */
    @PostMapping("/checkNameUnique")
    ResultWrapper<Boolean> checkNameUnique(@RequestParam(value = "tenantId", required = false) Long tenantId, 
                                          @RequestParam("name") String name, 
                                          @RequestParam(value = "id", required = false) String id);

    /**
     * 批量启用/禁用薪资项目
     * @param ids ID数组
     * @param status 状态
     * @return ResultWrapper
     */
    @PostMapping("/batchUpdateStatus")
    ResultWrapper<?> batchUpdateStatus(@RequestParam("ids") String ids, @RequestParam("status") Boolean status);

    /**
     * 初始化系统内置薪资项目
     * @return ResultWrapper
     */
    @PostMapping("/initSystemItems")
    ResultWrapper<?> initSystemItems();

    /**
     * 验证计算公式
     * @param formula 计算公式
     * @return ResultWrapper
     */
    @PostMapping("/validateFormula")
    ResultWrapper<Boolean> validateFormula(@RequestParam("formula") String formula);
}

/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.salary;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.salary.EmployeeSalaryDetailMonthlyModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 员工薪资明细月度数据表 Api
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface EmployeeSalaryDetailMonthlyApi {

    /** 标题 */
    String TITLE = "员工薪资明细管理";
    /** 子标题 */
    String SUB_TITLE = "员工薪资明细月度数据";

    /**
     * 员工薪资明细 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<EmployeeSalaryDetailMonthlyModel> get(EmployeeSalaryDetailMonthlyModel model);

    /**
     * 员工薪资明细 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            EmployeeSalaryDetailMonthlyModel request
    );

    /**
     * 员工薪资明细 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findAll")
    ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findAll(EmployeeSalaryDetailMonthlyModel request);

    /**
     * 员工薪资明细 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody EmployeeSalaryDetailMonthlyModel model);

    /**
     * 员工薪资明细 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody EmployeeSalaryDetailMonthlyModel model);

    /**
     * 员工薪资明细 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(@RequestParam("id") String id);

    /**
     * 员工薪资明细 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(@RequestParam("ids") String ids);

    /**
     * 根据租户ID、数据月份和员工ID查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return ResultWrapper
     */
    @GetMapping("/findByEmployee")
    ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findByEmployee(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("employeeId") Long employeeId
    );

    /**
     * 根据租户ID、数据月份和员工ID列表查询薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeIds 员工ID列表
     * @return ResultWrapper
     */
    @GetMapping("/findByEmployees")
    ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findByEmployees(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("employeeIds") String employeeIds
    );

    /**
     * 根据薪资项目ID查询相关的薪资明细
     * @param salaryItemId 薪资项目ID
     * @return ResultWrapper
     */
    @GetMapping("/findBySalaryItem")
    ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findBySalaryItem(@RequestParam("salaryItemId") Long salaryItemId);

    /**
     * 根据员工ID、薪资项目ID和数据月份查询薪资明细
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @GetMapping("/findByEmployeeAndSalaryItem")
    ResultWrapper<EmployeeSalaryDetailMonthlyModel> findByEmployeeAndSalaryItem(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam("salaryItemId") Long salaryItemId,
            @RequestParam("dataMonth") Date dataMonth
    );

    /**
     * 检查员工薪资明细是否已存在
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/checkExists")
    ResultWrapper<Boolean> checkExists(@RequestBody EmployeeSalaryDetailMonthlyModel model);

    /**
     * 根据值来源查询薪资明细
     * @param valueSource 值来源
     * @return ResultWrapper
     */
    @GetMapping("/findByValueSource")
    ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findByValueSource(@RequestParam("valueSource") String valueSource);

    /**
     * 根据员工ID查询薪资明细历史记录
     * @param employeeId 员工ID
     * @param salaryItemId 薪资项目ID
     * @param limit 限制数量
     * @return ResultWrapper
     */
    @GetMapping("/findDetailHistory")
    ResultWrapper<List<EmployeeSalaryDetailMonthlyModel>> findDetailHistory(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam("salaryItemId") Long salaryItemId,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit
    );

    /**
     * 批量保存员工薪资明细
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param salaryDetails 薪资明细列表
     * @return ResultWrapper
     */
    @PostMapping("/batchSave")
    ResultWrapper<?> batchSave(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestBody List<EmployeeSalaryDetailMonthlyModel> salaryDetails
    );

    /**
     * 删除员工薪资明细
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @param tenantId 租户ID
     * @return ResultWrapper
     */
    @PostMapping("/deleteByEmployee")
    ResultWrapper<?> deleteByEmployee(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("tenantId") Long tenantId
    );

    /**
     * 根据岗位标准自动预填充员工薪资明细
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param templateId 薪资模板ID
     * @return ResultWrapper
     */
    @PostMapping("/autoFillFromStandards")
    ResultWrapper<?> autoFillFromStandards(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("templateId") Long templateId
    );

    /**
     * 批量更新计算值
     * @param salaryDetailIds 薪资明细ID列表
     * @return ResultWrapper
     */
    @PostMapping("/batchUpdateCalculatedValues")
    ResultWrapper<?> batchUpdateCalculatedValues(@RequestBody List<String> salaryDetailIds);

    /**
     * 标记为已修改
     * @param id 薪资明细ID
     * @param modificationReason 修改原因
     * @return ResultWrapper
     */
    @PostMapping("/markAsModified")
    ResultWrapper<?> markAsModified(
            @RequestParam("id") String id,
            @RequestParam("modificationReason") String modificationReason
    );

    /**
     * 批量导入员工薪资明细
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param salaryDetails 薪资明细列表
     * @return ResultWrapper
     */
    @PostMapping("/batchImport")
    ResultWrapper<Map<String, Object>> batchImport(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestBody List<EmployeeSalaryDetailMonthlyModel> salaryDetails
    );

    /**
     * 复制上月薪资明细到当月
     * @param employeeId 员工ID
     * @param tenantId 租户ID
     * @param sourceMonth 源月份
     * @param targetMonth 目标月份
     * @return ResultWrapper
     */
    @PostMapping("/copyFromPreviousMonth")
    ResultWrapper<?> copyFromPreviousMonth(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("sourceMonth") Date sourceMonth,
            @RequestParam("targetMonth") Date targetMonth
    );

    /**
     * 获取员工薪资明细统计信息
     * @param tenantId 租户ID
     * @param dataMonth 数据月份
     * @param employeeId 员工ID
     * @return ResultWrapper
     */
    @GetMapping("/getStatistics")
    ResultWrapper<Map<String, Object>> getStatistics(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") Date dataMonth,
            @RequestParam("employeeId") Long employeeId
    );
}
/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.salary;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.common.annotation.validator.Validator;
import org.opsli.common.annotation.validator.ValidatorLenMax;
import org.opsli.common.annotation.validator.ValidatorLenMin;
import org.opsli.common.enums.ValidatorType;

import java.io.Serial;

/**
 * 薪资项目基础定义表 Model
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "薪资项目基础定义表")
public class SalaryItemModel extends ApiWrapper {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /** 薪资项目名称 */
    @Schema(description = "薪资项目名称")
    @ExcelProperty(value = "薪资项目名称", order = 1)
    @Validator({ValidatorType.IS_NOT_NULL})
    @ValidatorLenMax(100)
    @ValidatorLenMin(1)
    private String name;
    
    /** 薪资项目分类 */
    @Schema(description = "薪资项目分类")
    @ExcelProperty(value = "薪资项目分类", order = 2)
    @Validator({ValidatorType.IS_NOT_NULL})
    private String category;
    
    /** 数据类型 */
    @Schema(description = "数据类型")
    @ExcelProperty(value = "数据类型", order = 3)
    @Validator({ValidatorType.IS_NOT_NULL})
    private String dataType;
    
    /** 单位 (如: 元, %, 天) */
    @Schema(description = "单位")
    @ExcelProperty(value = "单位", order = 4)
    @ValidatorLenMax(20)
    private String unit;
    
    /** 小数位数（仅用于decimal类型） */
    @Schema(description = "小数位数")
    @ExcelProperty(value = "小数位数", order = 5)
    private Integer decimalPlaces;
    
    /** 计算公式（支持动态计算） */
    @Schema(description = "计算公式")
    @ExcelProperty(value = "计算公式", order = 6)
    private String calculationFormula;
    
    /** 是否系统内置项目 */
    @Schema(description = "是否系统内置项目")
    @ExcelProperty(value = "是否系统内置项目", order = 7)
    private Boolean isSystemItem;
    
    /** 项目状态, 1:启用, 0:停用 */
    @Schema(description = "项目状态")
    @ExcelProperty(value = "项目状态", order = 8)
    private Boolean status;
}

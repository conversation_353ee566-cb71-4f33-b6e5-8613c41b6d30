/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.salary;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.salary.EmployeeSalaryMonthlyModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * 员工月度薪资汇总表 Api
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface EmployeeSalaryMonthlyApi {

    /** 标题 */
    String TITLE = "员工薪资管理";
    /** 子标题 */
    String SUB_TITLE = "员工月度薪资汇总";

    /**
     * 员工薪资 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<EmployeeSalaryMonthlyModel> get(EmployeeSalaryMonthlyModel model);

    /**
     * 员工薪资 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            EmployeeSalaryMonthlyModel request
    );

    /**
     * 员工薪资 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findAll")
    ResultWrapper<List<EmployeeSalaryMonthlyModel>> findAll(EmployeeSalaryMonthlyModel request);

    /**
     * 员工薪资 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody EmployeeSalaryMonthlyModel model);

    /**
     * 员工薪资 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody EmployeeSalaryMonthlyModel model);

    /**
     * 员工薪资 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(@RequestParam("id") String id);

    /**
     * 员工薪资 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(@RequestParam("ids") String ids);

    /**
     * 根据数据月份查询员工薪资
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @GetMapping("/findByDataMonth")
    ResultWrapper<List<EmployeeSalaryMonthlyModel>> findByDataMonth(@RequestParam("dataMonth") Date dataMonth);

    /**
     * 根据员工ID查询薪资
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @GetMapping("/findByEmployee")
    ResultWrapper<EmployeeSalaryMonthlyModel> findByEmployee(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam("dataMonth") Date dataMonth
    );

    /**
     * 批量更新薪资状态
     * @param ids ID数组
     * @param status 状态
     * @return ResultWrapper
     */
    @PostMapping("/batchUpdateStatus")
    ResultWrapper<?> batchUpdateStatus(@RequestParam("ids") String ids, @RequestParam("status") String status);

    /**
     * 计算员工薪资
     * @param employeeId 员工ID
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @PostMapping("/calculateSalary")
    ResultWrapper<?> calculateSalary(@RequestParam("employeeId") Long employeeId, @RequestParam("dataMonth") Date dataMonth);

    /**
     * 批量计算员工薪资
     * @param employeeIds 员工ID数组
     * @param dataMonth 数据月份
     * @return ResultWrapper
     */
    @PostMapping("/batchCalculateSalary")
    ResultWrapper<?> batchCalculateSalary(@RequestParam("employeeIds") String employeeIds, @RequestParam("dataMonth") Date dataMonth);

    /**
     * 审批员工薪资
     * @param id 薪资ID
     * @return ResultWrapper
     */
    @PostMapping("/approve")
    ResultWrapper<?> approve(@RequestParam("id") String id);

    /**
     * 批量审批员工薪资
     * @param ids ID数组
     * @return ResultWrapper
     */
    @PostMapping("/batchApprove")
    ResultWrapper<?> batchApprove(@RequestParam("ids") String ids);

    /**
     * 查询员工薪资历史记录
     * @param employeeId 员工ID
     * @param limit 限制数量
     * @return ResultWrapper
     */
    @GetMapping("/findSalaryHistory")
    ResultWrapper<List<EmployeeSalaryMonthlyModel>> findSalaryHistory(
            @RequestParam("employeeId") Long employeeId,
            @RequestParam(value = "limit", defaultValue = "12") Integer limit
    );
}

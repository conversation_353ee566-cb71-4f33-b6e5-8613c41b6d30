/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.salary;

import java.io.Serial;
import java.util.Date;

import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.common.annotation.validator.Validator;
import org.opsli.common.annotation.validator.ValidatorLenMax;
import org.opsli.common.enums.ValidatorType;
import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位薪资标准月度数据表 Model
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "岗位薪资标准月度数据表")
public class PositionSalaryStandardMonthlyModel extends ApiWrapper {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @ExcelProperty(value = "数据月份", order = 1)
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    @Validator({ValidatorType.IS_NOT_NULL})
    private Date dataMonth;
    
    /** 岗位ID */
    @Schema(description = "岗位ID")
    @ExcelProperty(value = "岗位ID", order = 2)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long positionId;
    
    /** 岗位名称 (关联查询字段) */
    @Schema(description = "岗位名称")
    @ExcelProperty(value = "岗位名称", order = 3)
    private String positionName;
    
    /** 薪资模板ID */
    @Schema(description = "薪资模板ID")
    @ExcelProperty(value = "薪资模板ID", order = 4)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long templateId;
    
    /** 薪资模板名称 (关联查询字段) */
    @Schema(description = "薪资模板名称")
    @ExcelProperty(value = "薪资模板名称", order = 5)
    private String templateName;
    
    /** 薪资项目ID */
    @Schema(description = "薪资项目ID")
    @ExcelProperty(value = "薪资项目ID", order = 6)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long salaryItemId;
    
    /** 薪资项目名称 (关联查询字段) */
    @Schema(description = "薪资项目名称")
    @ExcelProperty(value = "薪资项目名称", order = 7)
    private String salaryItemName;
    
    /** 薪资项目分类 (关联查询字段) */
    @Schema(description = "薪资项目分类")
    @ExcelProperty(value = "薪资项目分类", order = 8)
    private String salaryItemCategory;
    
    /** 标准值（用于预填充） */
    @Schema(description = "标准值")
    @ExcelProperty(value = "标准值", order = 9)
    @ValidatorLenMax(500)
    private String standardValue;
    
    /** 最小值（验证用） */
    @Schema(description = "最小值")
    @ExcelProperty(value = "最小值", order = 10)
    @ValidatorLenMax(500)
    private String minValue;
    
    /** 最大值（验证用） */
    @Schema(description = "最大值")
    @ExcelProperty(value = "最大值", order = 11)
    @ValidatorLenMax(500)
    private String maxValue;
    
    /** 是否自动预填充到员工薪资 */
    @Schema(description = "是否自动预填充")
    @ExcelProperty(value = "是否自动预填充", order = 12)
    private Boolean isAutoFill;
    
    /** 预填充后是否只读（1=只读，0=可编辑） */
    @Schema(description = "预填充后是否只读")
    @ExcelProperty(value = "预填充后是否只读", order = 13)
    private Boolean isReadonly;
    
    /** 标准值说明 */
    @Schema(description = "标准值说明")
    @ExcelProperty(value = "标准值说明", order = 14)
    @ValidatorLenMax(255)
    private String description;
}
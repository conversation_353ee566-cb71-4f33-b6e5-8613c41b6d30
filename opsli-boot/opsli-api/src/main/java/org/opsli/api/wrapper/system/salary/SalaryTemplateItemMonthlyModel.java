/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.wrapper.system.salary;

import java.io.Serial;
import java.util.Date;

import org.opsli.api.base.warpper.ApiWrapper;
import org.opsli.common.annotation.validator.Validator;
import org.opsli.common.annotation.validator.ValidatorLenMax;
import org.opsli.common.enums.ValidatorType;
import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 薪资模板项目关联表 Model
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "薪资模板项目关联表")
public class SalaryTemplateItemMonthlyModel extends ApiWrapper {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;
    
    /** 数据月份 */
    @Schema(description = "数据月份")
    @ExcelProperty(value = "数据月份", order = 1)
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    @Validator({ValidatorType.IS_NOT_NULL})
    private Date dataMonth;
    
    /** 薪资模板ID */
    @Schema(description = "薪资模板ID")
    @ExcelProperty(value = "薪资模板ID", order = 2)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long templateId;
    
    /** 薪资项目ID */
    @Schema(description = "薪资项目ID")
    @ExcelProperty(value = "薪资项目ID", order = 3)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Long salaryItemId;
    
    /** 薪资项目名称 (关联查询字段) */
    @Schema(description = "薪资项目名称")
    @ExcelProperty(value = "薪资项目名称", order = 4)
    private String salaryItemName;
    
    /** 薪资项目分类 (关联查询字段) */
    @Schema(description = "薪资项目分类")
    @ExcelProperty(value = "薪资项目分类", order = 5)
    private String salaryItemCategory;
    
    /** 显示顺序 */
    @Schema(description = "显示顺序")
    @ExcelProperty(value = "显示顺序", order = 6)
    @Validator({ValidatorType.IS_NOT_NULL})
    private Integer displayOrder;
    
    /** 是否必填 */
    @Schema(description = "是否必填")
    @ExcelProperty(value = "是否必填", order = 7)
    private Boolean isRequired;
    
    /** 是否可编辑 */
    @Schema(description = "是否可编辑")
    @ExcelProperty(value = "是否可编辑", order = 8)
    private Boolean isEditable;
    
    /** 默认值 */
    @Schema(description = "默认值")
    @ExcelProperty(value = "默认值", order = 9)
    @ValidatorLenMax(255)
    private String defaultValue;
    
    /** 验证规则 (如: {"min": 0, "max": 50000, "required": true}) */
    @Schema(description = "验证规则")
    @ExcelProperty(value = "验证规则", order = 10)
    private String validationRules;
    
    /** 状态 */
    @Schema(description = "状态")
    @ExcelProperty(value = "状态", order = 11)
    private Boolean status;
}
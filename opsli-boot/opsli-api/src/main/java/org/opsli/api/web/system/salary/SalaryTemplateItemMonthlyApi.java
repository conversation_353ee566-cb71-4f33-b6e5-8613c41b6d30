/**
 * Copyright 2020 OPSLI 快速开发平台 https://www.opsli.com
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.opsli.api.web.system.salary;

import java.util.List;

import org.opsli.api.base.result.ResultWrapper;
import org.opsli.api.wrapper.system.salary.SalaryTemplateItemMonthlyModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 薪资模板项目关联表 Api
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface SalaryTemplateItemMonthlyApi {

    /** 标题 */
    String TITLE = "薪资模板项目管理";
    /** 子标题 */
    String SUB_TITLE = "薪资模板项目关联";

    /**
     * 薪资模板项目 查一条
     * @param model 模型
     * @return ResultWrapper
     */
    @GetMapping("/get")
    ResultWrapper<SalaryTemplateItemMonthlyModel> get(SalaryTemplateItemMonthlyModel model);

    /**
     * 薪资模板项目 查询分页
     * @param pageNo 当前页
     * @param pageSize 每页条数
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findPage")
    ResultWrapper<?> findPage(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            SalaryTemplateItemMonthlyModel request
    );

    /**
     * 薪资模板项目 查询全部
     * @param request request
     * @return ResultWrapper
     */
    @GetMapping("/findAll")
    ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findAll(SalaryTemplateItemMonthlyModel request);

    /**
     * 薪资模板项目 新增
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/insert")
    ResultWrapper<?> insert(@RequestBody SalaryTemplateItemMonthlyModel model);

    /**
     * 薪资模板项目 修改
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/update")
    ResultWrapper<?> update(@RequestBody SalaryTemplateItemMonthlyModel model);

    /**
     * 薪资模板项目 删除
     * @param id ID
     * @return ResultWrapper
     */
    @PostMapping("/del")
    ResultWrapper<?> del(@RequestParam("id") String id);

    /**
     * 薪资模板项目 批量删除
     * @param ids ID 数组
     * @return ResultWrapper
     */
    @PostMapping("/delAll")
    ResultWrapper<?> delAll(@RequestParam("ids") String ids);

    /**
     * 根据租户ID、数据月份和模板ID查询模板项目
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @param templateId 模板ID
     * @return ResultWrapper
     */
    @GetMapping("/findByTemplate")
    ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findByTemplate(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestParam("templateId") Long templateId
    );

    /**
     * 根据租户ID、数据月份和模板ID查询启用的模板项目
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @param templateId 模板ID
     * @return ResultWrapper
     */
    @GetMapping("/findEnabledByTemplate")
    ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findEnabledByTemplate(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestParam("templateId") Long templateId
    );

    /**
     * 根据薪资项目ID查询关联的模板项目
     * @param salaryItemId 薪资项目ID
     * @return ResultWrapper
     */
    @GetMapping("/findBySalaryItem")
    ResultWrapper<List<SalaryTemplateItemMonthlyModel>> findBySalaryItem(@RequestParam("salaryItemId") Long salaryItemId);

    /**
     * 检查模板项目是否已存在
     * @param model 模型
     * @return ResultWrapper
     */
    @PostMapping("/checkExists")
    ResultWrapper<Boolean> checkExists(@RequestBody SalaryTemplateItemMonthlyModel model);

    /**
     * 批量保存模板项目配置
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @param templateItems 模板项目列表
     * @return ResultWrapper
     */
    @PostMapping("/batchSave")
    ResultWrapper<?> batchSave(
            @RequestParam("templateId") Long templateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth,
            @RequestBody List<SalaryTemplateItemMonthlyModel> templateItems
    );

    /**
     * 删除模板的所有项目
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @return ResultWrapper
     */
    @PostMapping("/deleteByTemplate")
    ResultWrapper<?> deleteByTemplate(
            @RequestParam("templateId") Long templateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth
    );

    /**
     * 更新显示顺序
     * @param templateItemIds 模板项目ID列表（按新顺序排列）
     * @return ResultWrapper
     */
    @PostMapping("/updateDisplayOrder")
    ResultWrapper<?> updateDisplayOrder(@RequestBody List<String> templateItemIds);

    /**
     * 批量启用/禁用模板项目
     * @param ids ID列表
     * @param status 状态
     * @return ResultWrapper
     */
    @PostMapping("/batchUpdateStatus")
    ResultWrapper<?> batchUpdateStatus(@RequestParam("ids") String ids, @RequestParam("status") Boolean status);

    /**
     * 复制模板项目到新模板
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @return ResultWrapper
     */
    @PostMapping("/copyToNewTemplate")
    ResultWrapper<?> copyToNewTemplate(
            @RequestParam("sourceTemplateId") Long sourceTemplateId,
            @RequestParam("targetTemplateId") Long targetTemplateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth
    );

    /**
     * 获取模板项目的最大显示顺序
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @param dataMonth 数据月份 (格式: YYYY-MM)
     * @return ResultWrapper
     */
    @GetMapping("/getMaxDisplayOrder")
    ResultWrapper<Integer> getMaxDisplayOrder(
            @RequestParam("templateId") Long templateId,
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("dataMonth") String dataMonth
    );
}
-- 最终简化版薪资管理数据库设计
-- 核心功能：工资表编制 -> 薪资结构设置 -> 员工薪资录入

-- 1. 薪资项目基础定义表
CREATE TABLE `salary_items` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '薪资项目ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '薪资项目名称',
  `category` enum('基本工资','补贴','提成','奖金','扣除') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '薪资项目分类',
  `data_type` enum('decimal','integer','percentage','text','boolean') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'decimal' COMMENT '数据类型',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位 (如: 元, %, 天)',
  `decimal_places` tinyint DEFAULT 2 COMMENT '小数位数（仅用于decimal类型）',
  `calculation_formula` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '计算公式（支持动态计算）',
  `is_system_item` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统内置项目',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '项目状态, 1:启用, 0:停用',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` int DEFAULT '0',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_name` (`tenant_id`, `name`),
  KEY `idx_tenant_category` (`tenant_id`, `category`),
  KEY `idx_tenant_type` (`tenant_id`, `data_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='薪资项目基础定义表';

-- 2. 薪资模板表
CREATE TABLE `salary_templates_monthly` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `data_month` date NOT NULL COMMENT '数据月份',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '模板状态',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '模板描述',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` int DEFAULT '0',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_tenant_month` (`tenant_id`, `data_month`),
  KEY `idx_tenant_month_priority` (`tenant_id`, `data_month`, `priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='薪资模板月度数据表';

-- 3. 薪资模板项目关联表
CREATE TABLE `salary_template_items_monthly` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `data_month` date NOT NULL COMMENT '数据月份',
  `template_id` bigint NOT NULL COMMENT '薪资模板ID',
  `salary_item_id` bigint NOT NULL COMMENT '薪资项目ID',
  `display_order` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
  `is_required` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否必填',
  `is_editable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可编辑',
  `default_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '默认值',
  `validation_rules` json COMMENT '验证规则 (如: {"min": 0, "max": 50000, "required": true})',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` int DEFAULT '0',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_item` (`tenant_id`, `data_month`, `template_id`, `salary_item_id`),
  KEY `idx_template_order` (`template_id`, `display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='薪资模板项目关联表';

-- 4. 岗位薪资标准表
CREATE TABLE `position_salary_standards_monthly` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `data_month` date NOT NULL COMMENT '数据月份',
  `position_id` bigint NOT NULL COMMENT '岗位ID',
  `template_id` bigint NOT NULL COMMENT '薪资模板ID',
  `salary_item_id` bigint NOT NULL COMMENT '薪资项目ID',
  `standard_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标准值（用于预填充）',
  `min_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最小值（验证用）',
  `max_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最大值（验证用）',
  `is_auto_fill` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否自动预填充到员工薪资',
  `is_readonly` tinyint(1) NOT NULL DEFAULT '0' COMMENT '预填充后是否只读（1=只读，0=可编辑）',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标准值说明',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` int DEFAULT '0',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_position_template_item` (`tenant_id`, `data_month`, `position_id`, `template_id`, `salary_item_id`),
  KEY `idx_position_template` (`position_id`, `template_id`, `data_month`),
  KEY `idx_template_item` (`template_id`, `salary_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位薪资标准月度数据表';

-- 5. 员工薪资汇总表
CREATE TABLE `employee_salaries_monthly` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `data_month` date NOT NULL COMMENT '数据月份',
  `employee_id` bigint NOT NULL COMMENT '员工ID',
  `template_id` bigint NOT NULL COMMENT '使用的薪资模板ID',
  `base_salary_total` decimal(12,2) DEFAULT '0.00' COMMENT '基本工资合计',
  `allowance_total` decimal(12,2) DEFAULT '0.00' COMMENT '补贴合计',
  `commission_total` decimal(12,2) DEFAULT '0.00' COMMENT '提成合计',
  `bonus_total` decimal(12,2) DEFAULT '0.00' COMMENT '奖金合计',
  `deduction_total` decimal(12,2) DEFAULT '0.00' COMMENT '扣除合计',
  `gross_salary` decimal(12,2) DEFAULT '0.00' COMMENT '应发工资总额',
  `net_salary` decimal(12,2) DEFAULT '0.00' COMMENT '实发工资',
  `calculated_at` timestamp NULL DEFAULT NULL COMMENT '计算时间',
  `calculation_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计算结果哈希（用于检测变更）',
  `status` enum('草稿','待审核','已审核','已发放','已归档') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '草稿' COMMENT '状态',
  `approved_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审批人',
  `approved_time` timestamp NULL DEFAULT NULL COMMENT '审批时间',
  `pay_date` date DEFAULT NULL COMMENT '发放日期',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` int DEFAULT '0',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_month_employee` (`tenant_id`, `data_month`, `employee_id`),
  KEY `idx_employee_month` (`employee_id`, `data_month`),
  KEY `idx_tenant_month_status` (`tenant_id`, `data_month`, `status`),
  KEY `idx_pay_date` (`pay_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工月度薪资汇总表';

-- 6. 员工薪资明细表
CREATE TABLE `employee_salary_details_monthly` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `data_month` date NOT NULL COMMENT '数据月份',
  `employee_id` bigint NOT NULL COMMENT '员工ID',
  `salary_item_id` bigint NOT NULL COMMENT '薪资项目ID',
  `actual_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '实际值（用户录入或系统计算）',
  `standard_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '岗位标准值（来源于position_salary_standards）',
  `calculated_value` decimal(12,2) DEFAULT NULL COMMENT '计算后的数值（仅用于统计，非decimal类型为NULL）',
  `value_source` enum('手动录入','岗位标准','公式计算','批量导入','系统默认') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '手动录入' COMMENT '值来源',
  `is_modified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已修改（区别于预填充值）',
  `modification_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改原因',
  `calculation_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'manual' COMMENT '计算方式',
  `source_data` json COMMENT '计算来源数据',
  `calculation_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计算说明',
  `last_calculated_at` timestamp NULL DEFAULT NULL COMMENT '最后计算时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` int DEFAULT '0',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_month_item` (`tenant_id`, `data_month`, `employee_id`, `salary_item_id`),
  KEY `idx_employee_month` (`employee_id`, `data_month`),
  KEY `idx_salary_item` (`salary_item_id`),
  KEY `idx_tenant_month_item` (`tenant_id`, `data_month`, `salary_item_id`),
  KEY `idx_calculated_value` (`calculated_value`),
  KEY `idx_value_source` (`value_source`),
  KEY `idx_modified` (`is_modified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工薪资明细月度数据表';